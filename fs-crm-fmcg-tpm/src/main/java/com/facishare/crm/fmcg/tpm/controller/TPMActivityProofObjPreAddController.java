package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.api.proof.PreAdd;
import com.facishare.crm.fmcg.tpm.business.ActivityService;
import com.facishare.crm.fmcg.tpm.business.DescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.ICostStandardService;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ProofCalculateType;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.ObjectRecordTypeService;
import com.facishare.paas.appframework.core.predef.service.dto.recordType.FindRecordTypeMatchList;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.dto.RecordTypeMatchInfo;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityProofObjPreAddController extends PreDefineController<PreAdd.Arg, PreAdd.Result> {

    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);

    private static final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);

    private static final ICostStandardService costStandardService = SpringUtil.getContext().getBean(ICostStandardService.class);

    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);

    private static final DescribeCacheService describeCacheService = SpringUtil.getContext().getBean(DescribeCacheService.class);

    public static final ActivityService activityService = SpringUtil.getContext().getBean(ActivityService.class);

    public static final ObjectRecordTypeService objectRecordTypeService = SpringUtil.getContext().getBean(ObjectRecordTypeService.class);


    private boolean openAI = false;

    @SneakyThrows
    @Override
    protected PreAdd.Result doService(PreAdd.Arg arg) {

        IObjectData activityData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getActivityId(), ApiNames.TPM_ACTIVITY_OBJ);
        if (activityData == null) {
            throw new ValidateException("activity not found.");
        }

        if (!Strings.isNullOrEmpty(arg.getProofId())) {
            return convertToPreAddResultV2(arg, null, activityData, null, null);
        }

        if (ActivityCustomerTypeEnum.BRAND.value().equals(activityData.get(TPMActivityFields.CUSTOMER_TYPE, String.class))) {
            return convertToPreAddResult(arg, null, activityData, null, null, false, null);
        }

        IObjectData storeData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        if (storeData == null) {
            throw new ValidateException("store not found.");
        }

        boolean activityIsAgreementRequired = tpm2Service.isNeedAgreement(Integer.valueOf(controllerContext.getTenantId()), activityData);

        IObjectData agreementData = null;
        if (activityIsAgreementRequired && !TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId())) {
            long now = System.currentTimeMillis();
            agreementData = findAgreement(controllerContext, now, arg.getActivityId(), arg.getStoreId());
        } else if (TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId()) && !Strings.isNullOrEmpty(arg.getActivityAgreementId())) {
            agreementData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getActivityAgreementId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        }

        IObjectData dealerData = null;
        if (GrayRelease.isAllow("fmcg", "YINLU_TPM", controllerContext.getTenantId()) && agreementData != null) {
            String agreementDealerId = agreementData.get(TPMActivityAgreementFields.DEALER_ID, String.class);
            if (Strings.isNullOrEmpty(agreementDealerId)) {
                throw new ValidateException(I18N.text(I18NKeys.DEALER_ID_FIELD_IS_NULL_CAN_NOT_PROOF));
            }
            dealerData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), agreementDealerId, ApiNames.ACCOUNT_OBJ);
            if (dealerData == null) {
                throw new ValidateException(I18N.text(I18NKeys.DEALER_ID_FIELD_IS_NULL_CAN_NOT_PROOF));
            }
        } else if (TPMGrayUtils.dealerProofEnable(controllerContext.getTenantId())) {
            String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), storeData);

            if (!Strings.isNullOrEmpty(dealerId)) {
                dealerData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
            }
        } else {
            String dealerId = storeBusiness.findDealerId(controllerContext.getTenantId(), storeData);
            if (!Strings.isNullOrEmpty(dealerId)) {
                dealerData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
            }
        }

        String proofTimePeriodDetailId = getProofTimePeriodDetailId(controllerContext.getTenantId(), activityData, agreementData, activityIsAgreementRequired);
        return convertToPreAddResult(arg, storeData, activityData, agreementData, dealerData, activityIsAgreementRequired, proofTimePeriodDetailId);
    }

    /**
     * 获取证明时间段详情ID
     */
    private String getProofTimePeriodDetailId(String tenantId, IObjectData activityData, IObjectData agreementData, boolean activityIsAgreementRequired) {
        try {
            serviceFacade.findObject(tenantId, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ);

            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(1);
            query.setOffset(0);
            query.setSearchSource("db");

            Filter refenceFilter = new Filter();
            refenceFilter.setOperator(Operator.EQ);
            refenceFilter.setFieldName(TPMProofTimePeriodDetailFields.ACTIVITY_ID);
            refenceFilter.setFieldValues(Lists.newArrayList(activityData.getId()));
            if (activityIsAgreementRequired && agreementData != null) {
                refenceFilter.setFieldName(TPMProofTimePeriodDetailFields.AGREEMENT_ID);
                refenceFilter.setFieldValues(Lists.newArrayList(agreementData.getId()));
            }

            // 当前时间过滤，确保在有效期内
            long now = System.currentTimeMillis();
            Filter beginDateFilter = new Filter();
            beginDateFilter.setFieldName(TPMProofTimePeriodDetailFields.BEGIN_DATE);
            beginDateFilter.setOperator(Operator.LT);
            beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

            Filter endDateFilter = new Filter();
            endDateFilter.setFieldName(TPMProofTimePeriodDetailFields.END_DATE);
            endDateFilter.setOperator(Operator.GT);
            endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

            query.setFilters(Lists.newArrayList(refenceFilter, beginDateFilter, endDateFilter));

            // 执行查询
            List<IObjectData> timePeriodDetails = CommonUtils.queryData(
                    serviceFacade,
                    User.systemUser(tenantId),
                    ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ,
                    query
            );

            if (!CollectionUtils.isEmpty(timePeriodDetails)) {
                return timePeriodDetails.get(0).getId();
            }
        } catch (ObjectDefNotFoundError ex) {
            log.info("proof time period detail object not found.");
        } catch (Exception e) {
            log.info("查询证明时间段详情失败", e);
        }

        return null;
    }

    private IObjectData findAgreement(ControllerContext context, long now, String activityId, String storeId) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, activityIdFilter, storeIdFilter));

        boolean isContainsProtocolStatus = describeCacheService.isExistField(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.RIO_PROTOCOL_STATUS);

        Filter invalidFilter = new Filter();
        invalidFilter.setFieldName(TPMActivityAgreementFields.RIO_PROTOCOL_STATUS);
        invalidFilter.setOperator(Operator.NEQ);
        invalidFilter.setFieldValues(Lists.newArrayList("void"));

        Filter invalidEmptyFilter = new Filter();
        invalidEmptyFilter.setFieldName(TPMActivityAgreementFields.RIO_PROTOCOL_STATUS);
        invalidEmptyFilter.setOperator(Operator.IS);
        invalidEmptyFilter.setFieldValues(Lists.newArrayList());

        if (isContainsProtocolStatus) {
            query.getFilters().add(invalidFilter);
            query.getFilters().add(invalidEmptyFilter);
            query.setPattern("1 and 2 and 3 and 4 and (5 or 6)");
        }

        List<IObjectData> agreements = CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);
        if (!agreements.isEmpty()) {
            return agreements.get(0);
        }
        return null;
    }

    @NotNull
    private PreAdd.Result convertToPreAddResult(PreAdd.Arg arg, IObjectData storeData, IObjectData activityData, IObjectData agreementData, IObjectData dealerData, boolean activityIsAgreementRequired, String proofTimePeriodDetailId) throws MetadataServiceException {
        PreAdd.Result data = new PreAdd.Result();

        String activityType = activityData.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        ActivityTypeExt typeExt = activityTypeManager.find(controllerContext.getTenantId(), activityType);

        // 判断是否开起来 AI
        boolean enableAi = typeExt.proofConfig().getAiConfig() != null && typeExt.proofConfig().getAiConfig().getEnableAiDisplayRecognition();
        data.setDisplayFields(getDisplayField(typeExt));


        data.setActivityIsAgreementRequired(tpm2Service.isNeedAgreement(Integer.valueOf(controllerContext.getTenantId()), activityData));
        PreAdd.ProofDataVO proof = new PreAdd.ProofDataVO();

        if (storeData != null) {
            String dealerRecordType = storeData.getRecordType();
            data.setIsDealerActivity(!Strings.isNullOrEmpty(dealerRecordType) && storeBusiness.findDealerRecordType(controllerContext.getTenantId()).contains(dealerRecordType));
            proof.setStoreId(storeData.getId());
            proof.setStoreName(storeData.getName());
            String activityStoreId = findActivityStoreId(controllerContext, activityData.getId(), storeData.getId());
            if (!Strings.isNullOrEmpty(activityStoreId)) {
                proof.setActivityStoreId(activityStoreId);
                proof.setActivityStoreName(storeData.getName());
            }
        }

        if (dealerData != null) {
            proof.setDealerId(dealerData.getId());
            proof.setDealerName(dealerData.getName());
        }
        proof.setActivityId(activityData.getId());
        proof.setCostConversionRatio(getCostConversionRatio(typeExt));
        proof.setActivityName(activityData.getName());
        proof.setVisitId(arg.getVisitId());
        proof.setRecordType(typeExt.proofNode().getObjectRecordType());
        proof.setActionId(arg.getActionId());
        proof.setAuditStatus(TPMActivityProofFields.AUDIT_STATUS__SCHEDULE);
        proof.setOwner(Lists.newArrayList(controllerContext.getUser().getUpstreamOwnerIdOrUserId()));

        openAI = activityTypeManager.getEnableAi(controllerContext.getTenantId()) && enableAi;
        proof.setOpenAi(openAI);
        if (StringUtil.isNotEmpty(proofTimePeriodDetailId)) {
            proof.setProofTimePeriodDetailId(proofTimePeriodDetailId);
        }

        data.setObjectData(proof);
        data.setDetails(new HashMap<>());

        List<PreAdd.ProofDisplayImgDataVO> displayImgDetails = new ArrayList<>();
        List<IObjectData> activityDetails = queryActivityDetail(controllerContext, activityData.getId());
        if (agreementData != null) {
            proof.setActivityAgreementId(agreementData.getId());
            proof.setActivityAgreementName(agreementData.getName());

            // 获取协议详情数据
            List<IObjectData> agreementDetails = queryActivityAgreementDetailMap(controllerContext, proof.getActivityAgreementId());
            List<PreAdd.ProofDetailDataVO> details;

            // 根据不同条件处理详情数据
            if (TPMGrayUtils.agreementNotRelatedToActivity(controllerContext.getTenantId())) {
                // 协议与活动无关的情况
                details = processAgreementDetails(agreementDetails, null, agreementData, arg);
                // 处理展示图片对象
                displayImgDetails = processActivityDisplayImgDetails(agreementDetails);
            } else {
                // 协议与活动有关的情况
                Map<String, IObjectData> activityDetailMap = activityDetails.stream()
                        .collect(Collectors.toMap(DBRecord::getId, v -> v, (a, b) -> a));

                // 处理协议详情与活动详情的关联
                details = new ArrayList<>();
                for (IObjectData agreementDetail : agreementDetails) {
                    String activityDetailId = agreementDetail.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID, String.class);
                    IObjectData activityDetail = activityDetailMap.get(activityDetailId);
                    if (activityDetail == null) {
                        continue;
                    }

                    // 处理单个协议详情与活动详情的关联
                    List<PreAdd.ProofDetailDataVO> detailVOs = processAgreementDetails(
                            Lists.newArrayList(agreementDetail),
                            activityDetail,
                            agreementData,
                            arg
                    );
                    details.addAll(detailVOs);
                }

                // 处理展示图片对象
                displayImgDetails = processActivityDisplayImgDetails(agreementDetails);
            }

            // 设置处理结果
            data.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, details);

        } else if (!activityIsAgreementRequired) {
            // 不需要协议的情况，直接处理活动详情
            List<PreAdd.ProofDetailDataVO> details = processActivityDetails(activityDetails, arg);
            data.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, details);

            // 处理展示图片对象
            displayImgDetails = processActivityDisplayImgDetails(activityDetails);
        }

        // 添加展示图片对象
        try {
            serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ);
            if (openAI) {
                data.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ, displayImgDetails);
            }
        } catch (ObjectDefNotFoundError ignore) {
        }

        // 填充引用对象
        if (!CollectionUtils.isEmpty(data.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ))) {
            if (openAI) {
                data.getDetails().remove(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ);
                return data;
            }
            List<JSONObject> list = JSON.parseArray(JSON.toJSONString(data.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ)), JSONObject.class);
            fillProofDetailRecordType(list, proof.getRecordType());
            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ);
            serviceFacade.fillObjectDataWithRefObject(describe, list.stream().map(ObjectData::new).collect(Collectors.toList()), controllerContext.getUser());
            List<PreAdd.ProofDetailDataVO> proofDetailDataVOS = JSON.parseArray(JSON.toJSONString(list), PreAdd.ProofDetailDataVO.class);
            proofDetailDataVOS.forEach(proofDetailDataVO -> proofDetailDataVO.setProofItem(proofDetailDataVO.getActivityItemLabel()));
            data.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, proofDetailDataVOS);
        }
        return data;
    }

    private void fillProofDetailRecordType(List<JSONObject> objects, String recordType) {
        FindRecordTypeMatchList.Arg findArg = new FindRecordTypeMatchList.Arg();
        findArg.setDescribeApiName(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ);
        FindRecordTypeMatchList.Result findResult = objectRecordTypeService.findRecordTypeMatchList(new ServiceContext(controllerContext.getRequestContext(), "a", "b"), findArg);
        List<String> records = findResult.getMasterRecordList().stream().filter(v -> v.getApiName().equals(recordType)).findFirst().orElse(new RecordTypeMatchInfo()).getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            objects.forEach(record -> record.put("record_type", records.get(0)));
        }
    }

    @NotNull
    private PreAdd.Result convertToPreAddResultV2(PreAdd.Arg arg, IObjectData storeData, IObjectData activityData, IObjectData dealerData, String proofTimePeriodDetailId) throws MetadataServiceException {
        PreAdd.Result data = new PreAdd.Result();

        fillPreDataByProof(arg, activityData, data);

        String activityType = activityData.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        ActivityTypeExt typeExt = activityTypeManager.find(controllerContext.getTenantId(), activityType);

        // 判断是否开起来 AI
        boolean enableAi = typeExt.proofConfig() != null && typeExt.proofConfig().getAiConfig() != null && typeExt.proofConfig().getAiConfig().getEnableAiDisplayRecognition();
        data.setDisplayFields(getDisplayField(typeExt));


        data.setActivityIsAgreementRequired(tpm2Service.isNeedAgreement(Integer.valueOf(controllerContext.getTenantId()), activityData));
        PreAdd.ProofDataVO proof = new PreAdd.ProofDataVO();

        if (storeData != null) {
            String dealerRecordType = storeData.getRecordType();
            data.setIsDealerActivity(!Strings.isNullOrEmpty(dealerRecordType) && storeBusiness.findDealerRecordType(controllerContext.getTenantId()).contains(dealerRecordType));
            proof.setStoreId(storeData.getId());
            proof.setStoreName(storeData.getName());
            String activityStoreId = findActivityStoreId(controllerContext, activityData.getId(), storeData.getId());
            if (!Strings.isNullOrEmpty(activityStoreId)) {
                proof.setActivityStoreId(activityStoreId);
                proof.setActivityStoreName(storeData.getName());
            }
        }

        if (dealerData != null) {
            proof.setDealerId(dealerData.getId());
            proof.setDealerName(dealerData.getName());
        }
        proof.setActivityId(activityData.getId());
        proof.setCostConversionRatio(getCostConversionRatio(typeExt));
        proof.setActivityName(activityData.getName());
        proof.setVisitId(arg.getVisitId());
        proof.setRecordType(typeExt.proofNode().getObjectRecordType());
        proof.setActionId(arg.getActionId());
        proof.setAuditStatus(TPMActivityProofFields.AUDIT_STATUS__SCHEDULE);
        proof.setOwner(Lists.newArrayList(controllerContext.getUser().getUpstreamOwnerIdOrUserId()));

        openAI = activityTypeManager.getEnableAi(controllerContext.getTenantId()) && enableAi;
        proof.setOpenAi(openAI);
        if (StringUtil.isNotEmpty(proofTimePeriodDetailId)) {
            proof.setProofTimePeriodDetailId(proofTimePeriodDetailId);
        }

        data.setObjectData(proof);
        data.setDetails(new HashMap<>());

        List<IObjectData> proofDetail = queryActivityProofDetail(controllerContext.getTenantId(), arg.getProofId());
        if (!CollectionUtils.isEmpty(proofDetail)) {
            data.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, proofDetail.stream().map(ObjectDataExt::toMap).collect(Collectors.toList()));
        }

        // 根据举证id 查询 举证陈列图片对象TPMActivityProofDisplayImgObj
        List<IObjectData> proofDisplayImg = null;
        try {
            proofDisplayImg = queryActivityProofDisplayImg(controllerContext.getTenantId(), arg.getProofId());
        } catch (ObjectDefNotFoundError ignore) {
        }
        if (!CollectionUtils.isEmpty(proofDisplayImg)) {
            data.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ, proofDisplayImg.stream().map(ObjectDataExt::toMap).collect(Collectors.toList()));
        }


        // 填充引用对象
        if (!CollectionUtils.isEmpty(data.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ))) {
            if (openAI) {
                data.getDetails().remove(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ);
                return data;
            }
            List<JSONObject> list = JSON.parseArray(JSON.toJSONString(data.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ)), JSONObject.class);
            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ);
            serviceFacade.fillObjectDataWithRefObject(describe, list.stream().map(ObjectData::new).collect(Collectors.toList()), controllerContext.getUser());
            List<PreAdd.ProofDetailDataVO> proofDetailDataVOS = JSON.parseArray(JSON.toJSONString(list), PreAdd.ProofDetailDataVO.class);
            proofDetailDataVOS.forEach(proofDetailDataVO -> proofDetailDataVO.setProofItem(proofDetailDataVO.getActivityItemLabel()));
            data.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, proofDetailDataVOS);
        }
        return data;
    }

    private List<IObjectData> queryActivityProofDetail(String tenantId, String proofId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(proofId));

        query.setFilters(Lists.newArrayList(masterFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, query);

    }

    private void fillPreDataByProof(PreAdd.Arg arg, IObjectData activityData, PreAdd.Result data) {
        // 如果 proof_id 不为空，则查询举证对象
        if (!Strings.isNullOrEmpty(arg.getProofId())) {
            IObjectData proofData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getProofId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
            if (proofData == null) {
                throw new ValidateException("proof not found.");
            }
            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
            serviceFacade.fillObjectDataWithRefObject(describe, Lists.newArrayList(proofData), controllerContext.getUser(), null, false);
            data.setProofData(ObjectDataDocument.of(proofData));

            //是否编辑
            boolean flag = activityService.validateActivityProofEnableEdit(controllerContext.getTenantId(), proofData.getId(), activityData.getId());
            data.setIsEdit(flag);
        }
    }

    private String findActivityStoreId(ControllerContext controllerContext, String activityId, String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityStoreFields.STORE_ID);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(activityFilter, storeFilter));

        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_STORE_OBJ, query);

        if (result.getData().isEmpty()) {
            return null;
        }
        return result.getData().get(0).getId();
    }

    private List<IObjectData> queryActivityDetail(ControllerContext context, String activityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityDetailFields.ACTIVITY_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(activityId));

        query.setFilters(Lists.newArrayList(masterFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_DETAIL_OBJ, query);
    }

    private List<IObjectData> queryActivityAgreementDetailMap(ControllerContext context, String activityAgreementId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityAgreementDetailFields.ACTIVITY_AGREEMENT_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(activityAgreementId));

        query.setFilters(Lists.newArrayList(masterFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ, query);
    }

    private double getCostConversionRatio(ActivityTypeExt typeExt) {
        double ratio = 1.0;
        if (typeExt != null) {
            ActivityProofConfigEntity configEntity = typeExt.proofConfig();
            if (configEntity != null && configEntity.getCostCalculateConfig() != null && ProofCalculateType.BY_SESSIONS.value().equals(configEntity.getCostCalculateConfig().getCalculateType())) {
                ratio = Double.parseDouble(configEntity.getCostCalculateConfig().getRatio());
            }
        }
        return ratio;
    }

    private List<PreAdd.Field> getDisplayField(ActivityTypeExt typeExt) throws MetadataServiceException {
        List<PreAdd.Field> fields = new ArrayList<>();
        String recordType = typeExt.proofNode().getObjectRecordType();
        ILayout layout = serviceFacade.getLayoutLogicService().findObjectLayout(controllerContext.getUser(), recordType, ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ);
        IObjectDescribe detailDescribe = serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ);
        layout.getComponents().forEach(iComponent -> {
            if (!"form".equals(iComponent.getType()))
                return;
            FormComponent component = (FormComponent) iComponent;
            if (!CollectionUtils.isEmpty(component.getFieldSections())) {
                component.getFieldSections().forEach(v -> v.getFields().forEach(iFormField -> {
                    if (TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD.equals(iFormField.getFieldName()) ||
                            TPMActivityProofDetailFields.PROOF_DETAIL_COST_STANDARD.equals(iFormField.getFieldName())) {
                        IFieldDescribe fieldDescribe = detailDescribe.getFieldDescribe(iFormField.getFieldName());
                        fields.add(PreAdd.Field.builder().type(fieldDescribe.getType()).apiName(fieldDescribe.getApiName()).label(fieldDescribe.getLabel()).build());
                    }
                }));
            }
        });
        return fields;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    private List<PreAdd.ProofDetailDataVO> processAgreementDetails(List<IObjectData> agreementDetails, IObjectData activityDetail, IObjectData agreementData, PreAdd.Arg arg) {
        List<PreAdd.ProofDetailDataVO> details = new ArrayList<>();
        for (IObjectData agreementDetail : agreementDetails) {
            PreAdd.ProofDetailDataVO detailVO = new PreAdd.ProofDetailDataVO();

            // 设置协议详情相关属性
            detailVO.setActivityAgreementDetailId(agreementDetail.getId());
            detailVO.setActivityAgreementDetailName(agreementDetail.getName());

            // 从活动详情或协议详情获取数据
            if (activityDetail != null) {
                // 从活动详情中获取数据
                detailVO.setAmountStandardCheck(String.valueOf(activityDetail.get(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK)));
                detailVO.setAmountStandardCheckValue((Boolean) activityDetail.get(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK__V));
                detailVO.setCalculatePattern((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN));
                detailVO.setCalculatePatternValue((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN__V));
                detailVO.setCalculatePatternLabel((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN__R));
                detailVO.setActivityItemId((String) activityDetail.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID));
                detailVO.setActivityDetailId(activityDetail.getId());
                detailVO.setActivityDetailName(activityDetail.getName());
                detailVO.setType((String) activityDetail.get(TPMActivityDetailFields.TYPE));
            } else {
                // 从协议详情中获取数据
                detailVO.setActivityItemId((String) agreementDetail.get(TPMActivityAgreementDetailFields.ACTIVITY_ITEM_ID));
                detailVO.setAmountStandardCheck(String.valueOf(agreementDetail.get(TPMActivityAgreementDetailFields.AMOUNT_STANDARD_CHECK)));
                detailVO.setAmountStandardCheckValue((Boolean) agreementDetail.get(TPMActivityAgreementDetailFields.AMOUNT_STANDARD_CHECK__V));
                detailVO.setCalculatePattern((String) agreementDetail.get(TPMActivityAgreementDetailFields.CALCULATE_PATTERN));
                detailVO.setCalculatePatternValue((String) agreementDetail.get(TPMActivityAgreementDetailFields.CALCULATE_PATTERN__V));
                detailVO.setCalculatePatternLabel((String) agreementDetail.get(TPMActivityAgreementDetailFields.CALCULATE_PATTERN__R));
                detailVO.setType((String) agreementDetail.get(TPMActivityAgreementDetailFields.TYPE));
            }

            // 设置标准值属性
            detailVO.setAmountStandard(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD, Double.class, 0D));
            detailVO.setActivityCostStandard(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_COST_STANDARD, Double.class, 0D));
            detailVO.setAgreementAmountStandard(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD, Double.class, 0D));
            detailVO.setProofDetailAmountStandard(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD, Double.class, 0D));
            detailVO.setProofDetailCostStandard(agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_COST_STANDARD, Double.class, 0D));
            detailVO.setIsReportItemQuantity(Boolean.valueOf(agreementDetail.get(TPMActivityAgreementDetailFields.IS_REPORT_ITEM_QUANTITY, Boolean.class, false).toString()));

            // 获取成本标准
            IObjectData costStandard = costStandardService.queryCostStandard(
                    controllerContext.getTenantId(),
                    arg.getStoreId(),
                    detailVO.getActivityItemId()
            );
            detailVO.setActivityItemCostStandardId(costStandard == null ? null : costStandard.getId());

            // 设置证明数据类型
            if (TPMGrayUtils.proofDataTypeAllUseExistOrNot(controllerContext.getTenantId())) {
                detailVO.setProofDataType(TPMActivityItemFields.PROOF_DATA_TYPE__EXIST_OR_NOT);
            } else {
                detailVO.setProofDataType(TPMActivityItemFields.PROOF_DATA_TYPE__NUMBER);
            }

            details.add(detailVO);
        }
        return details;
    }

    /**
     * 处理活动详情数据，转换为证明详情数据
     *
     * @param activityDetails 活动详情列表
     * @param arg             请求参数
     * @return 证明详情数据列表
     */
    private List<PreAdd.ProofDetailDataVO> processActivityDetails(List<IObjectData> activityDetails, PreAdd.Arg arg) {
        List<PreAdd.ProofDetailDataVO> details = new ArrayList<>();
        for (IObjectData activityDetail : activityDetails) {
            PreAdd.ProofDetailDataVO detail = new PreAdd.ProofDetailDataVO();

            // 从活动详情中获取数据
            detail.setAmountStandardCheck(String.valueOf(activityDetail.get(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK)));
            detail.setAmountStandardCheckValue((Boolean) activityDetail.get(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK__V));
            detail.setCalculatePattern((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN));
            detail.setCalculatePatternValue((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN__V));
            detail.setCalculatePatternLabel((String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN__R));
            detail.setActivityItemId((String) activityDetail.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID));
            detail.setActivityDetailId(activityDetail.getId());
            detail.setActivityDetailName(activityDetail.getName());
            detail.setType((String) activityDetail.get(TPMActivityDetailFields.TYPE));
            detail.setIsReportItemQuantity(Boolean.valueOf(activityDetail.get(TPMActivityAgreementDetailFields.IS_REPORT_ITEM_QUANTITY, Boolean.class, false).toString()));

            // 设置证明数据类型
            if (TPMGrayUtils.proofDataTypeAllUseExistOrNot(controllerContext.getTenantId())) {
                detail.setProofDataType(TPMActivityItemFields.PROOF_DATA_TYPE__EXIST_OR_NOT);
            } else {
                detail.setProofDataType(TPMActivityItemFields.PROOF_DATA_TYPE__NUMBER);
            }

            // 获取成本标准
            IObjectData costStandard = costStandardService.queryCostStandard(
                    controllerContext.getTenantId(),
                    arg.getStoreId(),
                    detail.getActivityItemId()
            );

            // 设置标准值
            if (costStandard == null) {
                detail.setAmountStandard(activityDetail.get(TPMActivityDetailFields.ACTIVITY_AMOUNT_STANDARD, Double.class, 0D));
                detail.setActivityCostStandard(activityDetail.get(TPMActivityDetailFields.ACTIVITY_COST_STANDARD, Double.class, 0D));
                detail.setProofDetailAmountStandard(activityDetail.get(TPMActivityDetailFields.ACTIVITY_AMOUNT_STANDARD, Double.class, 0D));
                detail.setProofDetailCostStandard(activityDetail.get(TPMActivityDetailFields.ACTIVITY_COST_STANDARD, Double.class, 0D));
                detail.setActivityItemCostStandardId(null);
            } else {
                detail.setAmountStandard(costStandard.get(TPMActivityItemCostStandardFields.AMOUNT_STANDARD, Double.class, 0D));
                detail.setActivityCostStandard(costStandard.get(TPMActivityItemCostStandardFields.COST_STANDARD, Double.class, 0D));
                detail.setProofDetailAmountStandard(costStandard.get(TPMActivityItemCostStandardFields.AMOUNT_STANDARD, Double.class, 0D));
                detail.setProofDetailCostStandard(costStandard.get(TPMActivityItemCostStandardFields.COST_STANDARD, Double.class, 0D));
                detail.setActivityItemCostStandardId(costStandard.getId());
            }

            details.add(detail);
        }
        return details;
    }

    // 新增方法 - 处理协议相关的展示图片对象
    private List<PreAdd.ProofDisplayImgDataVO> processAgreementDisplayImgDetails(List<IObjectData> agreementDetails) {
        if (CollectionUtils.isEmpty(agreementDetails)) {
            return new ArrayList<>();
        }

        List<String> displayFormIds = agreementDetails.stream().filter(agreementDetail ->
                        StringUtils.isNotBlank(agreementDetail.get(TPMActivityDetailFields.DISPLAY_FORM_ID, String.class)))
                .distinct()
                .map(agreementDetail -> agreementDetail.get(TPMActivityDetailFields.DISPLAY_FORM_ID, String.class))
                .collect(Collectors.toList());

        List<String> activityItemIds = agreementDetails.stream().filter(agreementDetail ->
                        StringUtils.isNotBlank(agreementDetail.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID, String.class)))
                .distinct()
                .map(agreementDetail -> agreementDetail.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID, String.class))
                .collect(Collectors.toList());

        Map<String, IObjectData> displayFormMap = (CollectionUtils.isEmpty(displayFormIds)) ? Maps.newHashMap() :
                serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), displayFormIds, ApiNames.DISPLAY_FORM_OBJ).stream()
                        .collect(Collectors.toMap(DBRecord::getId, displayForm -> displayForm));

        Map<String, IObjectData> activityItemMap = (CollectionUtils.isEmpty(activityItemIds)) ? Maps.newHashMap() :
                serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), activityItemIds, ApiNames.TPM_ACTIVITY_ITEM_OBJ).stream()
                        .collect(Collectors.toMap(DBRecord::getId, activityDetail -> activityDetail));


        List<PreAdd.ProofDisplayImgDataVO> displayImgDetails = new ArrayList<>();
        for (IObjectData agreementDetail : agreementDetails) {

            PreAdd.ProofDisplayImgDataVO detail = new PreAdd.ProofDisplayImgDataVO();

            // 设置展示图片专用字段
            detail.setImage("");
            detail.setActivityItemId(agreementDetail.get(TPMActivityAgreementDetailFields.ACTIVITY_ITEM_ID, String.class));
            detail.setActivityItemLabel(activityItemMap.get(detail.getActivityItemId()).getName());

            String displayFormId = agreementDetail.get(TPMActivityAgreementDetailFields.DISPLAY_FORM_ID, String.class);
            if (StringUtils.isNotBlank(displayFormId)) {
                detail.setDisplayFormId(displayFormId);
                detail.setDisplayFormLabel(displayFormMap.get(displayFormId).getName());
                //display_position
                detail.setStandardDisplayPosition(displayFormMap.get(displayFormId).get("display_position", String.class, ""));
            }

            if (openAI) {
                detail.setShowDisplayId(displayFormId);
                detail.setShowDisplayLabel(displayFormMap.get(displayFormId).getName());
            } else {
                if (StringUtils.isNotBlank(displayFormId)) {
                    detail.setShowDisplayId(displayFormId);
                    detail.setShowDisplayLabel(displayFormMap.get(displayFormId).getName());
                } else {
                    detail.setShowDisplayId(agreementDetail.get(TPMActivityAgreementDetailFields.ACTIVITY_ITEM_ID, String.class));
                    detail.setShowDisplayLabel(activityItemMap.get(detail.getActivityItemId()).getName());
                }
            }

            detail.setActualDisplayPosition("");
            displayImgDetails.add(detail);
        }
        return displayImgDetails;
    }

    // 处理活动相关的陈列图片对象
    private List<PreAdd.ProofDisplayImgDataVO> processActivityDisplayImgDetails(List<IObjectData> details) {
        if (CollectionUtils.isEmpty(details)) {
            return new ArrayList<>();
        }

        // 使用Stream优化数据收集
        Map<Boolean, List<String>> groupedIds = details.stream()
                .collect(Collectors.partitioningBy(
                        d -> StringUtils.isNotBlank(d.get(TPMActivityDetailFields.DISPLAY_FORM_ID, String.class)),
                        Collectors.mapping(d -> {
                            String formId = d.get(TPMActivityDetailFields.DISPLAY_FORM_ID, String.class);
                            String itemId = d.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID, String.class);
                            return StringUtils.isNotBlank(formId) ? formId : itemId;
                        }, Collectors.toList()))
                );

        List<String> displayFormIds = groupedIds.get(true).stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        List<String> activityItemIds = groupedIds.get(false).stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 批量获取关联数据（合并查询）
        Map<String, IObjectData> combinedMap = new HashMap<>();
        if (!displayFormIds.isEmpty()) {
            combinedMap.putAll(serviceFacade.findObjectDataByIds(
                    controllerContext.getTenantId(),
                    displayFormIds,
                    ApiNames.DISPLAY_FORM_OBJ
            ).stream().collect(Collectors.toMap(DBRecord::getId, v -> v)));
        }
        if (!activityItemIds.isEmpty()) {
            combinedMap.putAll(serviceFacade.findObjectDataByIds(
                    controllerContext.getTenantId(),
                    activityItemIds,
                    ApiNames.TPM_ACTIVITY_ITEM_OBJ
            ).stream().collect(Collectors.toMap(DBRecord::getId, v -> v)));
        }

        return Stream.concat(
                displayFormIds.stream().map(id -> createDisplayImgVO(id, true, combinedMap)),
                activityItemIds.stream().map(id -> createDisplayImgVO(id, false, combinedMap))
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private PreAdd.ProofDisplayImgDataVO createDisplayImgVO(String id, boolean isForm, Map<String, IObjectData> dataMap) {
        if (StringUtils.isBlank(id) || !dataMap.containsKey(id)) {
            return null;
        }

        IObjectData data = dataMap.get(id);
        PreAdd.ProofDisplayImgDataVO vo = new PreAdd.ProofDisplayImgDataVO();
        vo.setImage("");

        if (isForm) {
            vo.setDisplayFormId(id);
            vo.setDisplayFormLabel(data.getName());
            vo.setStandardDisplayPosition(data.get("display_position", String.class, ""));
            vo.setShowDisplayId(id);
            vo.setShowDisplayLabel(data.getName());
        } else {
            vo.setActivityItemId(id);
            vo.setActivityItemLabel(data.getName());
            vo.setShowDisplayId(id);
            vo.setShowDisplayLabel(data.getName());
        }

        vo.setRecordType("default__c");
        vo.setActualDisplayPosition("");
        return vo;
    }

    /**
     * 根据举证ID查询举证陈列图片对象
     *
     * @param tenantId 租户ID
     * @param proofId  举证ID
     * @return 举证陈列图片对象列表
     */
    private List<IObjectData> queryActivityProofDisplayImg(String tenantId, String proofId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityProofDisplayImgFields.ACTIVITY_PROOF_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(proofId));

        query.setFilters(Lists.newArrayList(masterFilter));
        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ, query);
    }
}