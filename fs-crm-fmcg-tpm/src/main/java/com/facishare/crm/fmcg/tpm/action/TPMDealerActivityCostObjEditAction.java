package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.CrmAuditLogService;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.UnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDealerActivityCostRebateService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDealerActivityCostService;
import com.facishare.crm.fmcg.tpm.business.dto.CrmAuditLogDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/12/16 下午4:02
 */
@SuppressWarnings("Duplicates")
public class TPMDealerActivityCostObjEditAction extends StandardEditAction {
    private static final Logger log = LoggerFactory.getLogger(TPMDealerActivityCostObjFlowCompletedAction.class);

    public static final IBudgetService budgetService = SpringUtil.getContext().getBean(IBudgetService.class);
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private final CrmAuditLogService crmAuditLogService = SpringUtil.getContext().getBean(CrmAuditLogService.class);
    private final UnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(UnifiedActivityCommonLogicBusiness.class);

    private final IDealerActivityCostService dealerActivityCostService = SpringUtil.getContext().getBean(IDealerActivityCostService.class);

    private final IActivityService activityService = SpringUtil.getContext().getBean(IActivityService.class);

    private final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private final IDealerActivityCostRebateService dealerActivityCostRebateService = SpringUtil.getContext().getBean(IDealerActivityCostRebateService.class);
    private boolean isApprovalEdit = false;
    private boolean isOpenBudge = false;
    private boolean enableOverWriteOff = false;
    private IObjectData activity;

    private boolean isOnceWriteOff = false;

    private boolean isForceCloseActivity = false;

    private IObjectData store;
    private IObjectData fundAccount;
    private static final Set<String> ALLOW_EDIT_FIELD = Sets.newHashSet();

    static {
        ALLOW_EDIT_FIELD.add(TPMDealerActivityCostFields.CONFIRMED_AMOUNT);
        ALLOW_EDIT_FIELD.add(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID);
        ALLOW_EDIT_FIELD.add(TPMDealerActivityCostFields.CASH_USAGE);
        ALLOW_EDIT_FIELD.add(TPMDealerActivityCostFields.GOODS_PAY_USAGE);
        ALLOW_EDIT_FIELD.add(TPMDealerActivityCostFields.EFFECTIVE_PERIOD);
        ALLOW_EDIT_FIELD.add(TPMDealerActivityCostFields.GOODS_PAY_NUMBER);
        ALLOW_EDIT_FIELD.add(TPMDealerActivityCostFields.COST_CASHING_QUANTITY);

        ConfigFactory.getConfig("fs-fmcg-tpm-config", config -> {
            String allowEditFields = config.get("DEALER_COST_ALLOW_EDIT_FIELDS_2");
            if (!StringUtils.isEmpty(allowEditFields)) {
                ALLOW_EDIT_FIELD.addAll(JSON.parseArray(allowEditFields, String.class));
            }
        });
    }

    @Override
    protected void before(Arg arg) {
        sendActivityObjAuditLog(arg);
        fillArgData(arg);
        super.before(arg);
        initData();
        validateDateRange(arg);
        validateEffectivePeriod(arg);
        this.isOpenBudge = budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()));
        IObjectData master = arg.getObjectData().toObjectData();
        String masterLifeFStatus = master.get(CommonFields.LIFE_STATUS, String.class);
        if (CommonFields.LIFE_STATUS__UNDER_REVIEW.equals(masterLifeFStatus) || CommonFields.LIFE_STATUS__IN_CHANGE.equals(masterLifeFStatus)) {
            this.isApprovalEdit = true;
            setDefaultVal();
            checkIsOnlyEditSelfDefine(arg);
            // 860 申请核销兑付产品 从对象允许非必填，去掉校验
            validateCashingProductObj(arg);
            validateConfirmAmountForRebate(arg);
            validateAmount(arg);
            validateConfirmAmount(arg);
        } else {
            forbiddenCheck(arg);
        }

    }

    private void fillArgData(Arg arg) {
        //日期为空，表示默认值。
        ObjectDataDocument objectData = arg.getObjectData();
        if (objectData.containsKey(TPMDealerActivityCostFields.BEGIN_DATE)) {
            objectData.putIfAbsent(TPMDealerActivityCostFields.BEGIN_DATE, TimeUtils.MIN_DATE);
        }
        if (objectData.containsKey(TPMDealerActivityCostFields.END_DATE)) {
            objectData.putIfAbsent(TPMDealerActivityCostFields.END_DATE, TimeUtils.MAX_DATE);
        }
    }

    private void setDefaultVal() {
        String confirmedAmount = this.arg.getObjectData().toObjectData().get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, String.class);
        if (StringUtils.isEmpty(confirmedAmount)) {
            this.arg.getObjectData().put(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, "0");
        }
    }

    private void initData() {
        String activityId = this.arg.getObjectData().toObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
        if (Strings.isNullOrEmpty(activityId)) {
            return;
        }
        ActivityTypeExt activityType = activityTypeManager.findByActivityId(actionContext.getTenantId(), activityId);
        if (Objects.nonNull(activityType)) {
            ActivityWriteOffConfigEntity activityWriteOffConfigEntity = activityType.writeOffConfig();
            if (activityWriteOffConfigEntity != null && activityWriteOffConfigEntity.getEnableOverWriteOff() != null) {
                this.enableOverWriteOff = activityWriteOffConfigEntity.getEnableOverWriteOff();
                log.info("dealer activity cost enableOverWriteOff is {}", this.enableOverWriteOff);
            }
        }

        String storeId = arg.getObjectData().toObjectData().get(TPMDealerActivityCostFields.DEALER_ID, String.class);
        if (!Strings.isNullOrEmpty(storeId)) {
            store = serviceFacade.findObjectData(actionContext.getUser(), storeId, ApiNames.ACCOUNT_OBJ);
        }
        if (Strings.isNullOrEmpty(objectData.get(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, String.class))) {
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
            if (store == null || storeBusiness.findDealerRecordType(actionContext.getTenantId()).contains(store.getRecordType())) {
                objectData.set(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, activity.get(TPMActivityFields.DEALER_CASHING_TYPE));
            } else {
                objectData.set(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, activity.get(TPMActivityFields.STORE_CASHING_TYPE));
            }
        }
        String cashingType = objectData.get(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, String.class);
        if (!TPMActivityCashingProductFields.GOODS.equals(cashingType)) {
            return;
        }
        String fundAccountId = this.arg.getObjectData().toObjectData().get(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID, String.class);
        if (!Strings.isNullOrEmpty(fundAccountId)) {
            fundAccount = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), fundAccountId, ApiNames.FUND_ACCOUNT_OBJ);
            if (fundAccount == null) {
                throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_EDIT_ACTION_0));
            }
            String accountType = fundAccount.get(FundAccountFields.ACCOUNT_TYPE, String.class);
            if (Objects.equals(FundAccountFields.ACCOUNT_TYPE__GOODS_AMOUNT, accountType)) {
                objectData.set(TPMDealerActivityCostFields.GOODS_PAY_USAGE, TPMDealerActivityCostFields.GOODS_PAY_USAGE__AMOUNT);
            } else if (Objects.equals(FundAccountFields.ACCOUNT_TYPE__GOODS_NUMBER, accountType)) {
                objectData.set(TPMDealerActivityCostFields.GOODS_PAY_USAGE, TPMDealerActivityCostFields.GOODS_PAY_USAGE__QUANTITY);
            }
        }
    }

    private void validateConfirmAmount(Arg arg) {
        String tenantId = actionContext.getTenantId();
        if (TPMGrayUtils.skipValidateConfirmAmount(tenantId) || enableOverWriteOff) {
            return;
        }
        String activityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        IObjectData cost = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_DEALER_ACTIVITY_COST);
        activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (activity == null) {
            log.error("validateConfirmAmount activity is null");
            return;
        }
        Map<String, Double> amountMap = budgetService.getActivityAmountFields(tenantId, activityId);
        BigDecimal actualUsedAmount = BigDecimal.valueOf(amountMap.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT)).setScale(3, RoundingMode.HALF_UP);
        BigDecimal frozenAmount = BigDecimal.valueOf(amountMap.getOrDefault(TPMActivityFields.ACTIVITY_FROZEN_AMOUNT, 0.0)).setScale(3, RoundingMode.HALF_UP);
        BigDecimal total = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class, BigDecimal.ZERO).setScale(3, RoundingMode.HALF_UP);
        BigDecimal beforeConfirmedAmount = cost.get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, BigDecimal.class, BigDecimal.ZERO).setScale(3, RoundingMode.HALF_UP);
        //如果是编辑审批，需要从snapshot里面获取锁定的数值
        if ("in_change".equals(cost.get(CommonFields.LIFE_STATUS))) {
            BigDecimal usedAmount = BigDecimal.valueOf(amountMap.getOrDefault(String.format("%s:%s", TPMDealerActivityCostFields.CONFIRMED_AMOUNT, cost.getId()), beforeConfirmedAmount.doubleValue())).setScale(3, RoundingMode.HALF_UP);
            beforeConfirmedAmount = beforeConfirmedAmount.max(usedAmount);
        }
        String nowConfirmAmount = (String) arg.getObjectData().get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT);
        if (StringUtils.isEmpty(nowConfirmAmount)) {
            nowConfirmAmount = "0";
        }
        BigDecimal nowConfirmedAmount = new BigDecimal(nowConfirmAmount).setScale(3, RoundingMode.HALF_UP);

        if (total.subtract(frozenAmount).subtract(actualUsedAmount).add(beforeConfirmedAmount).subtract(nowConfirmedAmount).compareTo(BigDecimal.ZERO) < 0) {
            throw new ValidateException(I18N.text(I18NKeys.AUDIT_AMOUNT_CAN_NOT_OVER_THE_ACTIVITY_AMOUNT));
        }
    }

    private void validateAmount(Arg arg) {
        List<ObjectDataDocument> details = arg.getDetails().get(ApiNames.TPM_DEALER_ACTIVITY_CASHING_PRODUCT_OBJ);
        if (CollectionUtils.isNotEmpty(details)) {
            String confirmedAmountStr = (String) arg.getObjectData().get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT);
            if (StringUtils.isEmpty(confirmedAmountStr)) {
                confirmedAmountStr = "0";
            }
            BigDecimal confirmedAmount = new BigDecimal(confirmedAmountStr);
            BigDecimal totalPrice = new BigDecimal("0");
            for (ObjectDataDocument detail : details) {
                String priceStr = (String) detail.get(TPMDealerActivityCashingProductFields.PRICE);
                BigDecimal price = new BigDecimal(StringUtils.isEmpty(priceStr) ? "0" : priceStr);
                String costCashingQuantity = (String) detail.get(TPMDealerActivityCashingProductFields.COST_CASHING_QUANTITY);
                BigDecimal quantity = new BigDecimal(StringUtils.isEmpty(costCashingQuantity) ? "0" : costCashingQuantity);
                totalPrice = totalPrice.add(price.multiply(quantity));
            }
            if (confirmedAmount.compareTo(totalPrice) < 0) {
                throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_EDIT_ACTION_1));
            }
        }
    }

    private void validateCashingProductObj(Arg arg) {
        if (arg.getBizInfo() != null && RequestContext.Biz.ApprovalFlow.getCode().equals(arg.getBizInfo().getBiz())) {
            String cashingType = objectData.get(TPMDealerActivityCostFields.DEALER_CASHING_TYPE, String.class);
            if (Objects.equals(cashingType, TPMActivityCashingProductFields.GOODS)) {
                List<ObjectDataDocument> details = arg.getDetails().get(ApiNames.TPM_DEALER_ACTIVITY_CASHING_PRODUCT_OBJ);
                if (fundAccount == null) {
                    return;
                }
                String accountType = fundAccount.get(FundAccountFields.ACCOUNT_TYPE, String.class);
                if (!Objects.equals(FundAccountFields.ACCOUNT_TYPE__GOODS_NUMBER, accountType)) {
                    return;
                }
                boolean allZero = details.stream().map(ObjectDataDocument::toObjectData).allMatch(detail -> Objects.isNull(detail.get(TPMDealerActivityCashingProductFields.COST_CASHING_QUANTITY, Integer.class))
                        || 0 == detail.get(TPMDealerActivityCashingProductFields.COST_CASHING_QUANTITY, Integer.class));
                if (allZero) {
                    throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_EDIT_ACTION_2));
                }

            }
        }
    }

    private void validateConfirmAmountForRebate(Arg arg) {
        if (arg.getBizInfo() != null && RequestContext.Biz.ApprovalFlow.getCode().equals(arg.getBizInfo().getBiz())) {
            String fundAccountId = this.arg.getObjectData().toObjectData().get(TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID, String.class);
            if (StringUtils.isEmpty(fundAccountId)) {
                return;
            }
            IObjectData masterData = arg.getObjectData().toObjectData();
            //核销单入账开启，且返利账户
            if (Boolean.TRUE.equals(masterData.get(TPMDealerActivityCostFields.ENTER_ACCOUNT, Boolean.class))) {

                if (dealerActivityCostRebateService.isRebateAccount(actionContext.getTenantId(), fundAccountId)) {

                    String confirmedAmountStr = masterData.get(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, String.class);
                    if (StringUtils.isEmpty(confirmedAmountStr)) {
                        confirmedAmountStr = "0";
                    }
                    BigDecimal confirmedAmount = new BigDecimal(confirmedAmountStr);
                    if (BigDecimal.ZERO.compareTo(confirmedAmount) >= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_EDIT_ACTION_3));
                    }
                }
            }

        }
    }

    private void checkIsOnlyEditSelfDefine(Arg arg) {
        if (!isOpenBudge) {
            this.updatedFieldMap.keySet().forEach(key -> {
                if (!key.endsWith("__c") && !ALLOW_EDIT_FIELD.contains(key)) {
                    throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_EDIT_ACTION_4));
                }
            });
        } else {
            this.updatedFieldMap.keySet().forEach(key -> {
                if (!key.endsWith("__c") && !ALLOW_EDIT_FIELD.contains(key)) {
                    throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_EDIT_ACTION_5));
                }
            });
        }
    }

    private void forbiddenCheck(Arg arg) {
        arg.getObjectData().put(TPMDealerActivityCostFields.CONFIRMED_AMOUNT, 0);
        if (!arg.incrementUpdate()) {
            setDefaultValue(arg);
        }
//        validateEndDate(arg);
        Long startTime = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endTime = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.END_DATE);
        if (startTime == null || endTime == null) {
            throw new ValidateException("begin time or end time could not be empty.");
        }

        String activityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        String closedStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_CLOSED_CAN_NOT_CREATE_COST));
        }
        this.isOnceWriteOff = ActivityMaxWriteOffCountEnum.ONCE.value().equals(activity.get(TPMActivityFields.MAX_WRITE_OFF_COUNT, String.class));
        this.isForceCloseActivity = Boolean.TRUE.equals(activity.get(TPMActivityFields.IS_AUTO_CLOSE, Boolean.class));
        dealerActivityCostService.validateOnceWriteOffData(actionContext.getTenantId(), activity, store, startTime, endTime);
        validateActivity(arg);

        if (!"ineffective".equals(arg.getObjectData().get(CommonFields.LIFE_STATUS))) {
            throw new ValidateException(I18N.text(I18NKeys.NORMAL_STATUS_COST_CAN_NOT_EDIT));
        }

        if (this.updatedFieldMap.get(TPMDealerActivityCostFields.BEGIN_DATE) != null
                || this.updatedFieldMap.get(TPMDealerActivityCostFields.END_DATE) != null) {
            throw new ValidateException(I18N.text(I18NKeys.REJECT_COST_CAN_NOT_EDIT_TIME_FIELD));
        }
        validateIfAllowCreate(arg);
    }

    private void sendActivityObjAuditLog(Arg arg) {
        crmAuditLogService.sendLog(CrmAuditLogDTO.builder()
                .tenantId(actionContext.getTenantId())
                .userId(String.valueOf(User.systemUser(actionContext.getTenantId())))
                .action("Edit")
                .objectApiNames(ApiNames.TPM_DEALER_ACTIVITY_COST)
                .message("编辑费用核销")//ignorei18n
                .parameters(JSONObject.toJSONString(arg))
                .build());
    }

    private void validateDateRange(Arg arg) {
        arg.getObjectData().putIfAbsent(TPMDealerActivityCostFields.BEGIN_DATE, TimeUtils.MIN_DATE);
        arg.getObjectData().putIfAbsent(TPMDealerActivityCostFields.END_DATE, TimeUtils.MAX_DATE);
        if (this.updatedFieldMap.containsKey(TPMDealerActivityCostFields.BEGIN_DATE)) {
            this.updatedFieldMap.putIfAbsent(TPMDealerActivityCostFields.BEGIN_DATE, TimeUtils.MIN_DATE);
        }
        if (this.updatedFieldMap.containsKey(TPMDealerActivityCostFields.END_DATE)) {
            this.updatedFieldMap.putIfAbsent(TPMDealerActivityCostFields.BEGIN_DATE, TimeUtils.MIN_DATE);
        }
        if (Objects.equals(arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE), 0)) {
            arg.getObjectData().put(TPMDealerActivityCostFields.BEGIN_DATE, TimeUtils.MIN_DATE);
        }
        long begin = (long) arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) arg.getObjectData().get(TPMDealerActivityCostFields.END_DATE));
        arg.getObjectData().put(TPMDealerActivityCostFields.END_DATE, end);

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.COST_TIME_RANGE_IS_ERROR));
        }
    }


    private void validateEffectivePeriod(Arg arg) {
        String period = arg.getObjectData().toObjectData().get(TPMDealerActivityCostFields.EFFECTIVE_PERIOD, String.class);
        if (period != null && Integer.parseInt(period) < 0) {
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_EDIT_ACTION_6));
        }
    }

    private void validateActivity(Arg arg) {
        double amount = Double.parseDouble(arg.getObjectData().getOrDefault(TPMDealerActivityCostFields.AUDITED_AMOUNT, 0.0).toString());
        String activityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);

        String activityBudgetTableId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class);
        Double activityAmount = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, Double.class);

        if (!TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId()) && !Strings.isNullOrEmpty(activityBudgetTableId) &&
                activityAmount != null &&
                amount > activityAmount) {
            throw new ValidateException(I18N.text(I18NKeys.AMOUNT_SHOULD_LESS_THAN_AUDIT_AMOUNT));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (!this.isApprovalEdit) {
            setWriteOffStatus(result);
            updateWriteOffSourceReferenceFieldValue(result.getObjectData());
        }
        Result tempResult = super.after(arg, result);
        IObjectData resultObjectData = tempResult.getObjectData().toObjectData();
        String activityId = resultObjectData.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
        if (!isOpenBudge && !Strings.isNullOrEmpty(activityId)) {
            budgetService.calculateActivity(actionContext.getTenantId(), activityId);
            unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), resultObjectData.get(TPMDealerActivityCostFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
        }

        if (!isApprovalFlowStartSuccess(result.getObjectData().getId()) && this.isOnceWriteOff && this.isForceCloseActivity) {
            activityService.triggerCloseTPMActivity(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), activityId, true, false);
        }
        return tempResult;
    }


    private void updateWriteOffSourceReferenceFieldValue(ObjectDataDocument data) {

        String id = data.getId();
        String activityId = (String) data.get(TPMDealerActivityCostFields.ACTIVITY_ID);

        Long beginDate = (Long) data.get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endDate = (Long) data.get(TPMDealerActivityCostFields.END_DATE);

        ActivityTypeExt activityType = activityTypeManager.findByActivityId(actionContext.getTenantId(), activityId);
        ActivityWriteOffSourceConfigEntity config = activityType.writeOffSourceConfig();
        if (config == null || Strings.isNullOrEmpty(config.getApiName())) {
            return;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setSearchSource("db");

        Filter activityIdFiler = new Filter();
        activityIdFiler.setFieldName(config.getReferenceActivityFieldApiName());
        activityIdFiler.setOperator(Operator.EQ);
        activityIdFiler.setFieldValues(Lists.newArrayList(activityId));

        Filter referenceWriteOffFilter = new Filter();
        referenceWriteOffFilter.setFieldName(config.getReferenceWriteOffFieldApiName());
        referenceWriteOffFilter.setOperator(Operator.IS);
        referenceWriteOffFilter.setFieldValues(Lists.newArrayList());

        Filter createTimeFilter = new Filter();
        createTimeFilter.setFieldName(CommonFields.CREATE_TIME);
        createTimeFilter.setOperator(Operator.BETWEEN);
        createTimeFilter.setFieldValues(Lists.newArrayList(beginDate.toString(), endDate.toString()));

        query.setFilters(Lists.newArrayList(activityIdFiler, referenceWriteOffFilter, createTimeFilter));

        setStoreFilter(config, query);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        query.setOrders(Lists.newArrayList(order));

        List<String> fields = Lists.newArrayList(config.getReferenceWriteOffFieldApiName());
        List<String> queryFields = Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, config.getReferenceWriteOffFieldApiName());
        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(actionContext.getTenantId()), config.getApiName(), query, queryFields, 1000, sourceData -> {
            for (IObjectData datum : sourceData) {
                datum.set(config.getReferenceWriteOffFieldApiName(), id);
            }
            serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), sourceData, fields);
        });
    }

    private void setStoreFilter(ActivityWriteOffSourceConfigEntity config, SearchTemplateQuery query) {
        if (store != null) {
            Filter storeFilter = new Filter();
            storeFilter.setOperator(Operator.EQ);
            if (storeBusiness.findDealerRecordType(actionContext.getTenantId()).contains(store.getRecordType())) {
                storeFilter.setFieldName(config.getDealerFieldApiName());
                if (!Strings.isNullOrEmpty(config.getDealerFieldApiName())) {
                    storeFilter.setFieldValues(Lists.newArrayList(store.getId()));
                    query.getFilters().add(storeFilter);
                }
            } else {
                storeFilter.setFieldName(config.getAccountFieldApiName());
                if (!Strings.isNullOrEmpty(config.getAccountFieldApiName())) {
                    storeFilter.setFieldValues(Lists.newArrayList(store.getId()));
                    query.getFilters().add(storeFilter);
                }
            }
        }
    }

    private void validateIfAllowCreate(Arg arg) {
        String activityId = (String) arg.getObjectData().get(TPMDealerActivityCostFields.ACTIVITY_ID);
        Long beginDate = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.BEGIN_DATE);
        Long endDate = (Long) arg.getObjectData().get(TPMDealerActivityCostFields.END_DATE);
        ActivityTypeExt activityType = activityTypeManager.findByActivityId(actionContext.getTenantId(), activityId);
        ActivityWriteOffSourceConfigEntity config = activityType.writeOffSourceConfig();
        if (config == null || Strings.isNullOrEmpty(config.getApiName())) {
            return;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setSearchSource("db");

        Filter activityIdFiler = new Filter();
        activityIdFiler.setFieldName(config.getReferenceActivityFieldApiName());
        activityIdFiler.setOperator(Operator.EQ);
        activityIdFiler.setFieldValues(Lists.newArrayList(activityId));

        Filter referenceWriteOffFilter = new Filter();
        referenceWriteOffFilter.setFieldName(config.getReferenceWriteOffFieldApiName());
        referenceWriteOffFilter.setOperator(Operator.IS);
        referenceWriteOffFilter.setFieldValues(Lists.newArrayList());


        Filter createTimeFilter = new Filter();
        createTimeFilter.setFieldName(CommonFields.CREATE_TIME);
        createTimeFilter.setOperator(Operator.BETWEEN);
        createTimeFilter.setFieldValues(Lists.newArrayList(beginDate.toString(), endDate.toString()));

        query.setFilters(Lists.newArrayList(activityIdFiler, referenceWriteOffFilter, createTimeFilter));

        setStoreFilter(config, query);

        if (serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), config.getApiName(), query).getTotalNumber() == 0) {
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_EDIT_ACTION_7));
        }
    }


    private void setWriteOffStatus(Result result) {
        String lifeStatus = (String) result.getObjectData().get(CommonFields.LIFE_STATUS);
        if (ObjectLifeStatus.UNDER_REVIEW.getCode().equals(lifeStatus) || ObjectLifeStatus.IN_CHANGE.getCode().equals(lifeStatus)) {
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(TPMDealerActivityCostFields.WRITE_OFF_STATUS, TPMDealerActivityCostFields.WriteOffStatus.TO_BE_WRITE_OFF);
            serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updateMap);
        }
    }

    private void setDefaultValue(Arg arg) {
        arg.getObjectData().putIfAbsent(TPMDealerActivityCostFields.WRITE_OFF_STATUS, TPMDealerActivityCostFields.WriteOffStatus.PASS);
    }
}
