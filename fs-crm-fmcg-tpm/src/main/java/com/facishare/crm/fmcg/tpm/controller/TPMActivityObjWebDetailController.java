package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.web.contract.PromotionPolicyGet;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectReferenceWrapper;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class TPMActivityObjWebDetailController extends StandardWebDetailController {

    private static final ActivityTypeDAO activityTypeDAO = SpringUtil.getContext().getBean(ActivityTypeDAO.class);
    private static final PromotionPolicyService promotionPolicyService = SpringUtil.getContext().getBean(PromotionPolicyService.class);

    public static final String ACTIVITY_TYPE_INFORMATION_FIELD_KEY = "__activity_type_information";
    public static final String PRODUCT_GIFT_DATA_JSON = "product_gift_data_json";

    private ActivityTypeExt activityTypeExt;

    @Override
    protected Result after(Arg arg, Result result) {
        fillActivityTypeInformation(result);
        buttonFilter(result);
        fillActivityDate(result);
        fillStoreRange(result);
        return super.after(arg, result);
    }

    private void fillStoreRange(Result result) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(result.getData());
        JSONObject storeRange = JSON.parseObject(objectDataExt.getStringValue(TPMActivityFields.STORE_RANGE));
        if (storeRange == null || !"CONDITION".equalsIgnoreCase(storeRange.getString("type"))) {
            return;
        }
        String valueJson = storeRange.getString("value");
        JSONArray wheres = JSON.parseArray(valueJson);
        for (int whereIndex = 0; whereIndex < wheres.size(); whereIndex++) {
            JSONObject where = wheres.getJSONObject(0);
            JSONArray filters = where.getJSONArray("filters");
            for (int filterIndex = 0; filterIndex < filters.size(); filterIndex++) {
                JSONObject filter = filters.getJSONObject(filterIndex);

                if (!"object_reference_many".equals(filter.getString("type"))) {
                    continue;
                }
                List<String> fieldValues = filter.getJSONArray("field_values").toJavaList(String.class);
                String fieldName = filter.getString("field_name");
                if (CollectionUtils.isEmpty(fieldValues)) {
                    continue;
                }
                String fieldValuesTranslate = queryFieldValuesName(controllerContext.getTenantId(), targetObjApiName(fieldName), fieldName, fieldValues);
                filter.put("field_values__s", fieldValuesTranslate);
            }
        }
        storeRange.put("value",JSON.toJSONString(wheres));
        result.getData().put(TPMActivityFields.STORE_RANGE,JSON.toJSONString(storeRange));
    }

    private String targetObjApiName(String fieldName) {
        DescribeResult accountDescribe = serviceFacade.findDescribeAndLayout(User.systemUser(controllerContext.getTenantId()), ApiNames.ACCOUNT_OBJ, false, null);
        String targetObjApiName = "";
        ObjectDescribeExt of = ObjectDescribeExt.of(accountDescribe.getObjectDescribe());
        for (ObjectReferenceWrapper referenceFieldDescribe : of.getReferenceFieldDescribes()) {
            if (referenceFieldDescribe.getApiName().equals(fieldName)) {
                targetObjApiName = referenceFieldDescribe.getTargetApiName();
                break;
            }
        }
        return targetObjApiName;
    }

    private String queryFieldValuesName(String tenantId, String apiName, String fieldName, List<String> values) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(fieldName);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(values);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);
        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                apiName,
                stq,
                Lists.newArrayList(
                        CommonFields.NAME
                )
        );
        if (CollectionUtils.isEmpty(data)) {
            return "";
        }
        return data.stream().map(IObjectData::getName).collect(Collectors.joining(","));
    }

    private void fillActivityDate(Result result) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(result.getData());
        if (objectDataExt.get(TPMActivityFields.BEGIN_DATE) == null && objectDataExt.get(TPMActivityFields.END_DATE) == null) {
            log.info("objectDataExt id value is {}", objectDataExt.getId());
        }
        Long beginDate = objectDataExt.get(TPMActivityFields.BEGIN_DATE, Long.class);
        Long endDate = objectDataExt.get(TPMActivityFields.END_DATE, Long.class);

        if (beginDate <= TimeUtils.MIN_DATE) {
            result.getData().put(TPMActivityFields.BEGIN_DATE, null);
        }
        if (endDate >= TimeUtils.MAX_DATE) {
            result.getData().put(TPMActivityFields.END_DATE, null);
        }

        if (TPMGrayUtils.isRioTenant(controllerContext.getTenantId())) {
            try {
                ActivityTypePO activityTypePO = activityTypeExt.get();
                String templateId = activityTypePO.getTemplateId();
                if (templateId == null || !templateId.startsWith("promotion")) {
                    return;
                }
                PromotionPolicyGet.Arg policyArg = new PromotionPolicyGet.Arg();
                policyArg.setObjectApiName(ApiNames.TPM_ACTIVITY_OBJ);
                policyArg.setObjectId(result.getData().getId());

                ApiContext apiContext = ApiContextManager.getContext();
                if (Objects.isNull(apiContext)) {
                    apiContext = ApiContext.builder().tenantId(controllerContext.getTenantId()).build();
                    ApiContextManager.setContext(apiContext);
                }
                PromotionPolicyGet.Result policyResult = promotionPolicyService.get(policyArg);
                if (Objects.nonNull(policyResult)) {
                    result.getData().put(PRODUCT_GIFT_DATA_JSON, policyResult.getProductGiftData());
                }
            } finally {
                ApiContextManager.removeContext();
            }

        }
    }

    private void fillActivityTypeInformation(Result result) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(result.getData());
        String activityTypeId = objectDataExt.getStringValue(TPMActivityFields.ACTIVITY_TYPE);
        if (!Objects.isNull(activityTypeId) && objectIdValidate(activityTypeId)) {
            ActivityTypeVO activityType = ActivityTypeVO.fromPO(activityTypeDAO.get(controllerContext.getTenantId(), activityTypeId));
            activityTypeExt = ActivityTypeExt.of(ActivityTypePO.fromVO(activityType));
            result.getData().put(ACTIVITY_TYPE_INFORMATION_FIELD_KEY, activityType);
        }
    }

    private void buttonFilter(Result result) {
        // 判断是否流程布局进入、流程布局跳过

        List<ILayout> byTypesIncludeFlowLayout = serviceFacade.getLayoutLogicService().findByTypesIncludeFlowLayout(controllerContext.getTenantId(),
                ApiNames.TPM_ACTIVITY_OBJ, Lists.newArrayList(LayoutTypes.DETAIL));
        List<String> flowLayoutApiNames = byTypesIncludeFlowLayout.stream().filter(iLayout -> Objects.equals(iLayout.getNamespace(), "flow"))
                .map(ILayout::getName).collect(Collectors.toList());
        if (flowLayoutApiNames.contains(arg.getLayoutApiName())) {
            return;
        }

        if (Objects.isNull(result.getLayout())){
            return;
        }
        List components = (ArrayList) (result.getLayout().get("components"));
        String activityStatus = (String) result.getData().get(TPMActivityFields.ACTIVITY_STATUS);
        String closedStatus = (String) result.getData().get(TPMActivityFields.CLOSED_STATUS);
        String lifeStatus = (String) result.getData().get(CommonFields.LIFE_STATUS);
        boolean statusJudge = TPMGrayUtils.isAllowProcessActivityClose(controllerContext.getTenantId()) ? !"normal".equals(lifeStatus) : !TPMActivityFields.ACTIVITY_STATUS__END.equals(activityStatus);
        if (statusJudge || TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            for (Object component : components) {
                Map com = (Map) component;
                if ("head_info".equals(com.get("api_name"))) {
                    ArrayList buttons = (ArrayList) com.get("buttons");
                    buttons.removeIf(button -> {
                        Map btn = (Map) (button);
                        return ObjectAction.CLOSE_TPM_ACTIVITY.getActionCode().equals(btn.get("action"));
                    });
                }
            }
            if ("mobile".equals(arg.getLayoutAgentType())) {
                ArrayList buttons = (ArrayList) result.getLayout().get("buttons");
                buttons.removeIf(button -> {
                    Map btn = (Map) (button);
                    return ObjectAction.CLOSE_TPM_ACTIVITY.getActionCode().equals(btn.get("action"));
                });
            }
        }
    }

    private boolean objectIdValidate(String id) {
        if (Strings.isNullOrEmpty(id) || id.length() != 24)
            return false;
        for (char c : id.toCharArray()) {
            if ((c < '0' || c > '9') && (c < 'a' || c > 'f'))
                return false;
        }
        return true;
    }
}
