package com.facishare.crm.fmcg.tpm.web.manager.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetNewConsumeRulePO;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetNewConsumeRuleVO;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/1 下午4:20
 */
public interface IBudgetConsumeRuleManager {

    void consumeRuleInfoValidate(String tenantId, BudgetNewConsumeRuleVO budgetNewConsumeRuleVO);

    void consumeRuleEditValidate(String tenantId, BudgetNewConsumeRuleVO budgetNewConsumeRuleVO, BudgetNewConsumeRulePO oldPO);

    void enclosureConditionFilter(String tenantId, Integer employeeId, BudgetNewConsumeRuleVO budgetNewConsumeRuleVO);

    void bindObjectPluginInstance(String tenantId, Integer employeeId, String apiName, String pluginName);

    void deleteObjectPluginInstance(String tenantId, String apiName, String recordType, String pluginName);

    Integer deleteUseLessPlugin(String tenantId, String pluginName);

    void addClosureComponents(String tenantId, Integer userId, String apiName);

    Map<String, String> findObjectsByTenantId(String tenantId);

    Map<String, Map<String, String>> getConsumeObjects(String tenantId);

    Boolean isProcessFlowCompleted(String tenantId, String ruleId);

    void existsEnableRuleValidate(String tenantId, BudgetNewConsumeRulePO budgetNewConsumeRulePO);

    void deleteConsumeRuleByTypeId(String tenantId, int employeeId, String budgetTypeId);

    Map<String, String> getReleaseObjectLabels(String tenantId);

    void addBudgetProvisionField(String tenantId, BudgetNewConsumeRulePO budgetNewConsumeRulePO);

}
