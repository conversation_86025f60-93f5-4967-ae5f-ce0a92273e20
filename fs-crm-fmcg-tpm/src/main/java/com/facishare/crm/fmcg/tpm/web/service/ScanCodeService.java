package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.appserver.checkins.api.model.GetOpenIdByToken;
import com.facishare.appserver.checkins.api.service.ShopMMService;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.adapter.ReceiveMoneyService;
import com.facishare.crm.fmcg.common.adapter.abstraction.IFMCGTokenService;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetailStatusEnum;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.CloseWXOrder;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.FormWxPayMini;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.QueryEnterpriseUnionISV;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.QueryTransferDetailForReceipts;
import com.facishare.crm.fmcg.common.adapter.dto.token.GetWXOpenIdAndPhone;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.pojo.SnCode;
import com.facishare.crm.fmcg.common.utils.EncryptionService;
import com.facishare.crm.fmcg.common.utils.IdentityIdGenerator;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.MengNiuManufacturerInformation;
import com.facishare.crm.fmcg.tpm.api.scan.*;
import com.facishare.crm.fmcg.tpm.business.ActivityService;
import com.facishare.crm.fmcg.tpm.business.DescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFmcgSerialNumberService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRedPacketService;
import com.facishare.crm.fmcg.tpm.business.enums.ConsumerScanCodeStatusEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityRewardRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BizCodeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.reward.decoder.QRDecoderCenter;
import com.facishare.crm.fmcg.tpm.reward.dto.ActivityInformation;
import com.facishare.crm.fmcg.tpm.reward.dto.SerialNumberData;
import com.facishare.crm.fmcg.tpm.reward.dto.SnInformation;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.annotation.WeChatSecurityApi;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IRewardRuleManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IScanCodeService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.entity.metadata.RelevantTeam;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fmcg.framework.http.MengNiuProxy;
import com.fmcg.framework.http.contract.mengniu.Regeo;
import com.fs.fmcg.sdk.ai.plat.SecretUtil;
import com.fxiaoke.api.IdGenerator;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2023/9/20 10:40
 */
@Slf4j
@Component
@SuppressWarnings("Duplicates")
public class ScanCodeService implements IScanCodeService {

    public final ThreadLocal<SimpleDateFormat> SIMPLE_DATE_FORMAT_THREAD_LOCAL = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    @Resource
    private ReceiveMoneyService receiveMoneyService;

    @Autowired
    private IFMCGTokenService tokenService;

    @Resource
    private EncryptionService encryptionService;

    @Resource
    private IRedPacketService redPacketService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ActivityService activityService;

    @Resource
    private ActivityTypeDAO activityTypeDAO;

    @Resource
    protected RedissonClient redissonCmd;

    @Resource
    private BizCodeDAO bizCodeDAO;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private IFmcgSerialNumberService fmcgSerialNumberService;

    @Resource
    private ActivityRewardRuleDAO activityRewardRuleDAO;

    @Resource
    private IRewardRuleManager rewardRuleManager;

    @Resource
    protected EnterpriseEditionService enterpriseEditionService;

    @Resource
    private DescribeCacheService describeCacheService;

    @Resource
    private MengNiuProxy mengNiuProxy;

    @Resource
    private IRangeFieldBusiness rangeFieldBusiness;

    @Resource
    private UnlockOuterCodeService unlockOuterCodeService;

    @Resource
    private TenantHierarchyService tenantHierarchyService;

    @Resource
    private ShopMMService shopMMService;

    private static final long LOCK_WAIT = 100;

    private static final long LOCK_LEASE = 120000;

    private static Long MAX_DELAY_TIME_FOR_PAY;

    private static Cache<Integer, Boolean> UNION_OPEN_CACHE = CacheBuilder.newBuilder().maximumSize(5000).expireAfterWrite(300, TimeUnit.MINUTES).build();

    static {
        String time = ConfigFactory.getConfig("gray-rel-fmcg").get("max_delay_time_for_pay", "360000");
        MAX_DELAY_TIME_FOR_PAY = Long.parseLong(time);
    }

    @Override
    public BigDatePay.Result bigDatePay(BigDatePay.Arg arg) {
        setTrace();
        arg.setEnvironment(arg.getEnvironment().toLowerCase());

        String tenantId = redPacketService.getTopTenantId(arg.getTenantCode(), arg.getEnvironment());

        GetWXOpenIdAndPhone.Result tokenResult = tokenService.getWXOpenIdAndPhone(new GetWXOpenIdAndPhone.Arg(tenantId, arg.getAppId(), arg.getToken(), arg.getPhoneToken()));
        Map<String, String> code2activityMap = new HashMap<>();
        Map<String, ActivityRewardRulePO> activity2RewardRuleMap = new HashMap<>();
        Map<String, String> code2StoreMap = new HashMap<>();
        Map<String, List<String>> productRange2CodeMap = new HashMap<>();
        List<RLock> lockList = new ArrayList<>();
        BigDatePay.Result result = new BigDatePay.Result();
        try {
            arg.getCodes().forEach(code -> {
                SnCode snCode = encryptionService.verify(code);
                log.info("codeSplit:{}", snCode);
                if (code2activityMap.containsKey(snCode.getSnId())) {
                    throw new RewardFmcgException("10011", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_0));
                }
                code2activityMap.put(snCode.getSnId(), snCode.getActivityId());
                code2StoreMap.put(snCode.getSnId(), String.format("%s#%s", snCode.getStoreTenantId(), snCode.getStoreId()));
                List<String> codeList = productRange2CodeMap.getOrDefault(snCode.getActivityProductId(), new ArrayList<>());
                codeList.add(snCode.getSnId());
                productRange2CodeMap.put(snCode.getActivityProductId(), codeList);
                ActivityRewardRulePO activityRewardRulePO = activityRewardRuleDAO.getByRelatedObject(tenantId, ApiNames.TPM_ACTIVITY_OBJ, snCode.getActivityId());
                activity2RewardRuleMap.put(snCode.getActivityId(), activityRewardRulePO);
                lockList.add(tryLock(String.format(ScanCodeActionConstants.CODE_LOCK_KEY, snCode.getSnId())));
            });
            List<String> codeIds = new ArrayList<>(code2activityMap.keySet());
            RewardMethodEnum rewardMethodEnum = validateRewardMethod(new ArrayList<>(activity2RewardRuleMap.values()));
            if (rewardMethodEnum == RewardMethodEnum.REDUCED_PAYMENT) {
                result.setPaymentInfo(deductPay(tenantId, codeIds, arg.getAppId(), tokenResult.getOpenId(), code2activityMap, activity2RewardRuleMap, code2StoreMap, productRange2CodeMap));
            } else {
                //todo:红包
            }
        } finally {
            lockList.forEach(this::unlock);
        }
        return result;
    }

    private PaymentInfoDTO deductPay(String tenantId, List<String> codeIds, String appId, String openId, Map<String, String> code2activityMap, Map<String, ActivityRewardRulePO> activity2RewardRuleMap, Map<String, String> code2StoreMap, Map<String, List<String>> productRange2CodeMap) {
        BizCodePO bizCode = bizCodeDAO.getValidBizCode(tenantId, codeIds);
        if (bizCode == null) {
            return dealFormWXOrder(tenantId, appId, openId, codeIds, code2activityMap, activity2RewardRuleMap, code2StoreMap, productRange2CodeMap);
        } else if (bizCode.getStatus() == BizCodeStatusEnum.INIT.code()) {
            List<BizCodePO> validPos = bizCodeDAO.getValidBizCodes(tenantId, codeIds);
            for (BizCodePO bizCodePO : validPos) {
                if (bizCodePO.getStatus() == BizCodeStatusEnum.INIT.code()) {
                    if (System.currentTimeMillis() - bizCodePO.getCreateTime() > MAX_DELAY_TIME_FOR_PAY) {
                        CloseWXOrder.Result closeWxxOrderResult = closeWxOrder(bizCodePO);
                        if (TransferDetailStatusEnum.SUCCESS.codes().contains(closeWxxOrderResult.getTransferDetail().getStatus())) {
                            throw new RewardFmcgException("10012", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_1));
                        } else if (TransferDetailStatusEnum.FAIL.codes().contains(closeWxxOrderResult.getTransferDetail().getStatus()) || TransferDetailStatusEnum.CANCEL.codes().contains(closeWxxOrderResult.getTransferDetail().getStatus())) {
                            //todo：是否需要多校验一步paas对象是否存在？
                            bizCodeDAO.setStatus(tenantId, bizCodePO.getBizCode(), BizCodeStatusEnum.EXPIRED);
                        } else {
                            log.info("transferDetail:{}", closeWxxOrderResult.getTransferDetail());
                            throw new RewardFmcgException("10011", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_2));
                        }
                    } else {
                        throw new RewardFmcgException("10011", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_3));
                    }
                }
            }
            return dealFormWXOrder(tenantId, appId, openId, codeIds, code2activityMap, activity2RewardRuleMap, code2StoreMap, productRange2CodeMap);
        } else if (bizCode.getStatus() == BizCodeStatusEnum.USED.code()) {
            throw new RewardFmcgException("10012", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_4));
        }
        return null;
    }

    @Override
    public BigDateScanCode.Result bigDateScanCode(BigDateScanCode.Arg arg) {
        setTrace();
        arg.setEnvironment(arg.getEnvironment().toLowerCase());

        String tenantId = redPacketService.getTopTenantId(arg.getTenantCode(), arg.getEnvironment());
        log.info("manufacturer tenant id : {}", tenantId);

        SerialNumberData sn = QRDecoderCenter.getDecoder(arg.getTenantCode()).decodeOuter(tenantId, Strings.isNullOrEmpty(arg.getCode()) ? arg.getUrl() : arg.getCode());

        if (TPMGrayUtils.disableBigDateScan(tenantId)) {
            throw new ValidateException(I18N.text(I18NKeys.SCAN_CODE_SERVICE_1));
        }

        if (Objects.isNull(sn)) {
            throw new RewardFmcgException("10011", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_0));
        }

        IObjectData skuObj = fmcgSerialNumberService.getProductObjFromSerialNumberObj(tenantId, sn.getSkuId());

        validateCode(tenantId, sn.getSnId());

        //获取满足产品条件的活动
        IObjectData store = getStoreByStoreSign(tenantId, sn.getSnId(), false);
        if (store == null) {
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_5));
        }
        log.info("store id : {}, store name : {}", store.getId(), store.getName());

        IObjectData activity = findAgentSalesActivity(tenantId, skuObj, sn.getData(), store);

        if (activity == null) {
            throw new RewardFmcgException("10011", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_6));
        }
        IObjectData activityProduct = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), activity.get("matched_product_range_id", String.class), ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ);

        ProductInfoDTO product = new ProductInfoDTO();

        product.setStore(store.getId());
        product.setProductName(skuObj.getName());

        SnCode snCode = SnCode.builder()
                .manufacturerTenantId(tenantId)
                .type(SnCode.OUTER_TYPE)
                .snId(sn.getSnId())
                .activityId(activity.getId())
                .storeTenantId(store.getTenantId())
                .storeId(store.getId())
                .activityProductId(activityProduct.getId())
                .build();
        product.setCode(encryptionService.sign(snCode));
        product.setOriginalAmount(activityProduct.get(TPMActivityProductRangeFields.CONSUMER_RETAIL_PRICE, BigDecimal.class, new BigDecimal("********")));
        product.setDeductAmount(activityProduct.get(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, BigDecimal.class, BigDecimal.ZERO));
        product.setNeedPay(product.getOriginalAmount().subtract(product.getDeductAmount()));
        product.setGroupId(rewardRuleManager.getConsumerReceiverAccount(tenantId, sn.getSnId(), activityRewardRuleDAO.getByRelatedObject(tenantId, activity.getDescribeApiName(), activity.getId())));

        BigDateScanCode.Result result = new BigDateScanCode.Result();
        result.setProductInfo(product);
        return result;
    }

    @Override
    @WeChatSecurityApi
    public ConsumerScanInnerCode.Result consumerScanCode(ConsumerScanInnerCode.Arg arg) {
        if (arg.getCode().startsWith("NEW#")) {
            arg.setCode(arg.getCode().substring(4));
        }

        setTrace();
        String tenantId = redPacketService.getTopTenantId(arg.getTenantCode(), arg.getEnvironment());

        SerialNumberData sn = QRDecoderCenter.getDecoder(arg.getTenantCode()).decode(tenantId, arg.getCode());
        if (Objects.isNull(sn)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_0));
        }

        IObjectData skuObj = fmcgSerialNumberService.getProductObjFromSerialNumberObj(tenantId, sn.getSkuId());

        validateManufactureDate(skuObj, sn.getManufactureDate());

        IObjectData store = getStoreByStoreSign(tenantId, sn.getSnId(), true);

        boolean sold = store != null;
        String storeName = "--";
        String status = ConsumerScanCodeStatusEnum.NO_ACTIVITY.code();
        IObjectData fullActivity = null;
        String rewardMethod = RewardMethodEnum.RED_PACKET.code();
        ActivityInformation activityInformation = null;

        if (sold) {
            storeName = store.getName();
            IObjectData activity = findConsumerScanCodeActivity(tenantId, skuObj, store, sn.getData());
            if (Objects.nonNull(activity)) {
                fullActivity = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), activity.getId(), ApiNames.TPM_ACTIVITY_OBJ);
                rewardMethod = getRewardMethodByActivity(tenantId, fullActivity);
                activityInformation = ActivityInformation.builder()
                        .name(fullActivity.getName())
                        .rules(JSON.toJSONString(fullActivity.get("consumer_scan_code_activity_rules__c")))
                        .build();

                status = activityStatus(tenantId, arg, sn, fullActivity, rewardMethod);
            }
        }

        String rewardCode = encryptionService.sign(SnCode.builder()
                .manufacturerTenantId(tenantId)
                .type(sn.getType())
                .snId(sn.getSnId())
                .storeTenantId(Objects.isNull(store) ? "" : store.get(AccountFields.RELATED_ENTERPRISE_ID, String.class))
                .storeId(Objects.isNull(store) ? "" : store.getId())
                .activityId(Objects.isNull(fullActivity) ? "" : fullActivity.getId())
                .rewardMethod(rewardMethod)
                .realCode(sn.getRealCode())
                .build());

        String unlockQrCodeBase64 = "";
        if (Objects.equals(status, ConsumerScanCodeStatusEnum.LOCKED.code())) {
            unlockQrCodeBase64 = unlockOuterCodeService.initQrCodeWithParameters(tenantId, rewardCode);
        }

        SnInformation snInformation = SnInformation.builder()
                .productName(skuObj.getName())
                .productCode(skuObj.get(ProductFields.PRODUCT_CODE, String.class))
                .date(sn.getManufactureDate() == null ? "--" : SIMPLE_DATE_FORMAT_THREAD_LOCAL.get().format(new Date(sn.getManufactureDate())))
                .store(storeName)
                .code(String.format("NEW#%s", rewardCode))
                .unlockQrCodeBase64(unlockQrCodeBase64)
                .rewardMethod(rewardMethod)
                .sold(sold)
                .build();
        return ConsumerScanInnerCode.Result.builder().status(status).activityInformation(activityInformation).snInformation(snInformation).build();
    }

    @SuppressWarnings("unchecked")
    private String activityStatus(String tenantId, ConsumerScanInnerCode.Arg arg, SerialNumberData sn, IObjectData fullActivityObj, String rewardMethod) {

        if (SerialNumberData.OUTER_TYPE.equals(sn.getType()) && !Boolean.TRUE.equals(fullActivityObj.get(TPMActivityFields.IS_ALLOW_OUTER_CODE_SCAN, Boolean.class))) {
            log.info("activity not support outer code reward : {}", fullActivityObj.getName());
            return ConsumerScanCodeStatusEnum.NO_ACTIVITY.code();
        }

        if ("plugin1".equals(arg.getSource()) && !RewardMethodEnum.RED_PACKET.code().equals(rewardMethod)) {
            log.info("plugin only support red packet activity : {}", fullActivityObj.getName());
            return ConsumerScanCodeStatusEnum.NO_ACTIVITY.code();
        }

        List<String> limitedAreaIds = fullActivityObj.get("activity_sales_area__c", List.class);
        if (CollectionUtils.isNotEmpty(limitedAreaIds)) {
            if (Strings.isNullOrEmpty(arg.getLongitude()) || Strings.isNullOrEmpty(arg.getLatitude())) {
                log.info("activity limited area but no location parameter found : {}", fullActivityObj.getName());
                return ConsumerScanCodeStatusEnum.NO_ACTIVITY.code();
            } else {
                boolean areaValid = validateArea(tenantId, limitedAreaIds, arg.getLongitude(), arg.getLatitude());
                if (!areaValid) {
                    log.info("activity area validate failed : {}", fullActivityObj.getName());
                    return ConsumerScanCodeStatusEnum.NO_ACTIVITY.code();
                }
            }
        }

        if (Boolean.TRUE.equals(fullActivityObj.get(TPMActivityFields.IS_ALLOW_OUTER_CODE_SCAN, Boolean.class) && SerialNumberData.OUTER_TYPE.equals(sn.getType()) && unlockOuterCodeService.isOuterCodeLocked(tenantId, sn.getSnId()))) {
            log.info("sn code locked in current activity : {}.{}", fullActivityObj.getName(), sn.getName());
            return ConsumerScanCodeStatusEnum.LOCKED.code();
        }

        return ConsumerScanCodeStatusEnum.SUCCESS.code();
    }

    private boolean validateArea(String tenantId, List<String> areaIds, String longitude, String latitude) {
        long time = System.currentTimeMillis();

        String clientId = ConfigFactory.getConfig("gray-rel-fmcg").get("sales_mengniu_openapi_client_id");
        String sk = ConfigFactory.getConfig("gray-rel-fmcg").get("sales_mengniu_openapi_sk");
        String sign = SecretUtil.md5(clientId + sk + time).toUpperCase();

        try {
            log.info("location arg : {},{}", longitude, latitude);

            Regeo.Result location = mengNiuProxy.regeo(clientId, sign, String.valueOf(time), longitude + "," + latitude, null, null, null, null, null);

            log.info("location result : {}", JSON.toJSONString(location));

            List<String> areaCodes = fetchAreaCodes(location);
            if (CollectionUtils.isEmpty(areaCodes)) {
                return false;
            }

            List<IObjectData> areas = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, areaIds, "SalesAreaObj");
            for (IObjectData area : areas) {
                String areaCode = area.get("area_code", String.class);
                if (areaCodes.contains(areaCode)) {
                    return true;
                }
            }

            return false;

        } catch (Exception ex) {
            return false;
        }
    }

    @NotNull
    private static List<String> fetchAreaCodes(Regeo.Result location) {
        JSONObject address = location.getRegeocode().getAddressComponent();
        List<String> areaCodes = Lists.newArrayList();

        String cityCode = address.getString("citycode");
        if (!Strings.isNullOrEmpty(cityCode)) {
            areaCodes.add(cityCode);
        }

        String adCode = address.getString("adcode");
        if (!Strings.isNullOrEmpty(adCode)) {
            areaCodes.add(adCode);
        }

        String townCode = address.getString("towncode");
        if (!Strings.isNullOrEmpty(townCode)) {
            areaCodes.add(townCode);
        }
        return areaCodes;
    }

    @Override
    @WeChatSecurityApi
    public ConsumerScanCodeType.Result codeType(ConsumerScanCodeType.Arg arg) {
        String tenantId = redPacketService.getTopTenantId(arg.getTenantCode(), arg.getEnvironment());
        SerialNumberData sn = QRDecoderCenter.getDecoder(arg.getTenantCode()).decode(tenantId, arg.getCode());

        if (Objects.isNull(sn)) {
            throw new ValidateException(I18N.text(I18NKeys.CONSUMER_SCAN_CODE_REWARD_SERVICE_0));
        }

        if (SerialNumberData.OUTER_TYPE.equals(sn.getType())) {
            if (enableConsumerScanCodeActivity(tenantId, sn.getSnId())) {
                return ConsumerScanCodeType.Result.builder()
                        .codeType("2")
                        .code(String.format("NEW#%s", sn.getRealCode()))
                        .build();
            } else {
                return ConsumerScanCodeType.Result.builder().codeType("1").code(arg.getCode()).build();
            }
        } else {
            return ConsumerScanCodeType.Result.builder()
                    .codeType("2")
                    .code(String.format("NEW#%s", sn.getRealCode()))
                    .build();
        }
    }

    @Override
    @WeChatSecurityApi
    public CodeRewarded.Result rewarded(CodeRewarded.Arg arg) {
        String tenantId = redPacketService.getTopTenantId(arg.getTenantCode(), arg.getEnvironment());
        SerialNumberData sn = QRDecoderCenter.getDecoder(arg.getTenantCode()).decode(tenantId, arg.getCode());

        if (Objects.isNull(sn)) {
            return CodeRewarded.Result.builder().rewarded(false).build();
        }

        boolean rewarded = existRewardData(tenantId, sn.getSnId());
        return CodeRewarded.Result.builder().rewarded(rewarded).build();
    }

    private boolean existRewardData(String tenantId, String snId) {
        IFilter snIdFilter = new Filter();
        snIdFilter.setFieldName("serial_number_id");
        snIdFilter.setOperator(Operator.EQ);
        snIdFilter.setFieldValues(Lists.newArrayList(snId));

        SearchTemplateQuery query = QueryDataUtil.minimumFindOneQuery(snIdFilter);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, "TPMActivityRewardDetailObj", query, Lists.newArrayList("_id"));

        return CollectionUtils.isNotEmpty(data);
    }

    private void validateManufactureDate(IObjectData skuObj, Long manufactureDate) {
        if (Objects.isNull(manufactureDate) || manufactureDate == 0) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_1));
        }
        long day = skuObj.get(ProductFields.QUALITY_GUARANTEE_PERIOD, Long.class, 0L);
        if (day == 0) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_2));
        }
        if (manufactureDate + day * 24 * 60 * 60 * 1000 < System.currentTimeMillis()) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_3));
        }
    }

    private String getRewardMethodByActivity(String tenantId, IObjectData activity) {
        ActivityRewardRulePO activityRewardRulePO = activityRewardRuleDAO.getByRelatedObject(tenantId, activity.getDescribeApiName(), activity.getId());
        return activityRewardRulePO.getRewardDetails().get(activityRewardRulePO.getRewardDetails().size() - 1).getRewardStrategy().getRewardMethod();
    }

    public IObjectData findConsumerScanCodeActivity(String tenantId, String outerConsumerRewardRuleId, IObjectData store, IObjectData skuObj, IObjectData snObj) {
        List<IObjectData> activities = queryConsumerScanCodeActivity(tenantId, store, skuObj, snObj);
        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }

        List<ActivityRewardRulePO> configs = activityRewardRuleDAO.queryByRelatedObject(tenantId, ApiNames.TPM_ACTIVITY_OBJ, activities.stream().map(IObjectData::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(configs)) {
            return null;
        }

        Set<String> activityIds = new HashSet<>();
        for (ActivityRewardRulePO config : configs) {
            for (RewardDetailEntity rewardDetail : config.getRewardDetails()) {
                if (Objects.nonNull(rewardDetail.getOuterConsumerRewardStrategy()) && CollectionUtils.isNotEmpty(rewardDetail.getOuterConsumerRewardStrategy().getOuterConsumerRewardRules())) {
                    for (OuterConsumerRewardRuleEntity outerConsumerRewardRule : rewardDetail.getOuterConsumerRewardStrategy().getOuterConsumerRewardRules()) {
                        if (String.format("%s.%s", outerConsumerRewardRule.getPlatform(), outerConsumerRewardRule.getId()).equals(outerConsumerRewardRuleId)) {
                            activityIds.add(config.getRelatedObjectId());
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(activityIds)) {
            return null;
        }

        return activities.stream().filter(f -> activityIds.contains(f.getId())).max(Comparator.comparing(IObjectData::getCreateTime)).orElse(null);
    }

    public IObjectData findConsumerScanCodeActivity(String tenantId, IObjectData skuObj, IObjectData store, IObjectData snObj) {
        List<IObjectData> activities = queryConsumerScanCodeActivity(tenantId, store, skuObj, snObj);
        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }
        List<ActivityRewardRulePO> pos = activityRewardRuleDAO.queryByRelatedObjectAndAction(tenantId, ApiNames.TPM_ACTIVITY_OBJ, activities.stream().map(DBRecord::getId).collect(Collectors.toList()), fmcgSerialNumberService.getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.CONSUMER_GET_RED_PACKAGE));
        List<String> activityIds = pos.stream().map(ActivityRewardRulePO::getRelatedObjectId).collect(Collectors.toList());
        activities = activities.stream().filter(v -> activityIds.contains(v.getId())).collect(Collectors.toList());
        log.info("filter outer consumer activity. activitis:{}", JSON.toJSONString(activities));
        return activities.stream().max(Comparator.comparing(IObjectData::getCreateTime)).orElse(null);
    }

    private List<IObjectData> queryConsumerScanCodeActivity(String tenantId, IObjectData store, IObjectData sku, IObjectData sn) {
        List<String> types = activityTypeDAO.queryByTypeTemplateId(tenantId, Lists.newArrayList(ScanCodeActionConstants.CONSUMER_SCAN_INNER_CODE_ACTIVITY_TYPE_TEMPLATE_ID)).stream().map(v -> v.getId().toString()).collect(Collectors.toList());
        log.info("activity types : {}", types);

        List<String> departments = activityService.getDepartmentByStore(tenantId, store);
        log.info("departments : {}", departments);

        List<IObjectData> activities = activityService.findActivityByStore(tenantId, departments, store, Lists.newArrayList(), types);
        log.info("base activities : {}", activities.stream().map(IObjectData::getName).collect(Collectors.toList()));

        activities = filterByProductRange(tenantId, sku.getId(), activities, sn);
        log.info("filter by product activities : {}", activities.stream().map(IObjectData::getName).collect(Collectors.toList()));

        return activities;
    }

    private List<IObjectData> filterByProductRange(String tenantId, String id, List<IObjectData> baseActivities, IObjectData snObj) {
        List<IObjectData> activities = new ArrayList<>();

        Map<String, Boolean> fitMap = rangeFieldBusiness.judgeProductInActivitiesProductRange(tenantId, id, baseActivities, snObj);
        baseActivities.forEach(activity -> {
            if (Boolean.TRUE.equals(fitMap.get(activity.getId()))) {
                activities.add(activity);
            }
        });
        return activities;
    }

    private void setTrace() {
        TraceContext context = TraceContext.get();
        if (Strings.isNullOrEmpty(context.getTraceId())) {
            context.setTraceId(String.format("E-MN.%s", IdGenerator.get()));
        }
    }

    @Override
    public CloseBigDateWxOrder.Result closeBigDateWxOrder(CloseBigDateWxOrder.Arg arg) {
        BizCodePO bizCodePO = bizCodeDAO.getByBizCode(arg.getBizCode());
        if (bizCodePO != null && BizCodeStatusEnum.INIT.code() == bizCodePO.getStatus()) {
            CloseWXOrder.Result result = closeWxOrder(bizCodePO);
            if (TransferDetailStatusEnum.FAIL.codes().contains(result.getTransferDetail().getStatus()) || TransferDetailStatusEnum.CANCEL.codes().contains(result.getTransferDetail().getStatus())) {
                bizCodeDAO.setStatus(bizCodePO.getTenantId(), bizCodePO.getBizCode(), BizCodeStatusEnum.EXPIRED);
            }
        }
        return new CloseBigDateWxOrder.Result();
    }

    @Override
    @WeChatSecurityApi
    public ConsumerScanEnableReward.Result enableReward(ConsumerScanEnableReward.Arg arg) {
        String tenantId = redPacketService.getTopTenantId(arg.getTenantCode(), arg.getEnvironment());

        SerialNumberData sn = QRDecoderCenter.getDecoder(arg.getTenantCode()).decode(tenantId, arg.getCode());
        if (Objects.isNull(sn)) {
            return ConsumerScanEnableReward.Result.builder().status("0").code(arg.getCode()).build();
        }

        if (Boolean.FALSE.equals(arg.getEnableOuterCodeReward())) {
            if ((SerialNumberData.INNER_TYPE.equals(sn.getType()) || SerialNumberData.COUPON_TYPE.equals(sn.getType())) && enableConsumerScanCodeActivity(tenantId, sn.getSnId())) {
                return ConsumerScanEnableReward.Result.builder().status("1").code(String.format("NEW#%s", sn.getRealCode())).build();
            } else {
                return ConsumerScanEnableReward.Result.builder().status("0").code(arg.getCode()).build();
            }
        } else {
            if (enableConsumerScanCodeActivity(tenantId, sn.getSnId())) {
                return ConsumerScanEnableReward.Result.builder().status("1").code(String.format("NEW#%s", sn.getRealCode())).build();
            } else {
                return ConsumerScanEnableReward.Result.builder().status("0").code(arg.getCode()).build();
            }
        }
    }

    @Override
    @WeChatSecurityApi
    public ConsumerScanCheckLock.Result checkLock(ConsumerScanCheckLock.Arg arg) {
        if (arg.getCode().startsWith("NEW#") || arg.getCode().startsWith("OLD#")) {
            arg.setCode(arg.getCode().substring(4));
        }

        SnCode code;
        try {
            code = encryptionService.verify(arg.getCode());
        } catch (Exception ex) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_4));
        }

        MengNiuManufacturerInformation manufacturerConfig = tenantHierarchyService.findManufacturerByEnvironment(arg.getEnvironment());
        String tenantId = manufacturerConfig.getTenantId();

        if (SnCode.OUTER_TYPE.equals(code.getType()) && unlockOuterCodeService.isOuterCodeLocked(tenantId, code.getSnId())) {
            return ConsumerScanCheckLock.Result.builder().locked(true).build();
        } else {
            return ConsumerScanCheckLock.Result.builder().locked(false).build();
        }
    }

    @Override
    @WeChatSecurityApi
    public ConsumerScanUnlock.Result unlock(ConsumerScanUnlock.Arg arg) {
        String rewardCode = unlockOuterCodeService.loadParametersFromQrCode(arg.getQrCodeId());

        log.info("reward code : {}", rewardCode);
        if (rewardCode.startsWith("NEW#") || rewardCode.startsWith("OLD#")) {
            rewardCode = rewardCode.substring(4);
        }

        SnCode code;
        try {
            code = encryptionService.verify(rewardCode);
        } catch (Exception ex) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_5));
        }
        if (!SnCode.OUTER_TYPE.equals(code.getType())) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_6));
        }

        MengNiuManufacturerInformation manufacturerConfig = tenantHierarchyService.findManufacturerByEnvironment(arg.getEnvironment());
        String tenantId = manufacturerConfig.getTenantId();

        IObjectData sn = findSnObj(tenantId, code.getSnId());
        IObjectData sku = findSkuObjBySnObj(tenantId, sn);
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), code.getActivityId(), ApiNames.TPM_ACTIVITY_OBJ);
        Boolean isAllowOuterCodeScan = activity.get(TPMActivityFields.IS_ALLOW_OUTER_CODE_SCAN, Boolean.class, Boolean.TRUE);

        if (Boolean.FALSE.equals(isAllowOuterCodeScan)) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_SCAN_CODE_SERVICE_0));
        }

        if (!unlockOuterCodeService.isOuterCodeLocked(tenantId, code.getSnId())) {
            return ConsumerScanUnlock.Result.builder()
                    .productCode(sku.get(ProductFields.PRODUCT_CODE, String.class))
                    .productName(sku.get(ProductFields.NAME, String.class))
                    .build();
        }

        String userUnionId;
        if (!Strings.isNullOrEmpty(arg.getUnionId())) {
            userUnionId = arg.getUnionId();
        } else {
            try {
                GetOpenIdByToken.Result weChatAccount = convertToWeChatAccountInformation(tenantId, arg.getAppId(), arg.getToken());
                userUnionId = weChatAccount.getUnionId();
            } catch (Exception ex) {
                throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_7));
            }
        }

        if (Strings.isNullOrEmpty(userUnionId)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_8));
        }

        IObjectData user = findStoreOwnerOrAgentByUnionId(code.getStoreTenantId(), code.getStoreId(), userUnionId);

        if (Objects.isNull(user)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_9));
        }

        IObjectData unlockRecord = new ObjectData();

        unlockRecord.setTenantId(tenantId);
        unlockRecord.setOwner(Lists.newArrayList("-10000"));
        unlockRecord.setDescribeApiName("unlock_outer_code_record__c");
        unlockRecord.setRecordType("default__c");
        unlockRecord.set("product_id__c", sku.getId());
        unlockRecord.set("serial_number_id__c", sn.getId());
        unlockRecord.set("unlock_time__c", System.currentTimeMillis());
        unlockRecord.set("tenant_id__c", code.getStoreTenantId());
        unlockRecord.set("store_id__c", code.getStoreId());
        unlockRecord.set("name__c", user.getName());
        unlockRecord.set("unlock_user_api_name__c", user.getDescribeApiName());
        unlockRecord.set("unlock_user_id__c", user.getId());
        if (user.getDescribeApiName().equals("ContactObj")) {
            unlockRecord.set("unlock_user_type__c", "0");
        } else if (user.getDescribeApiName().equals("PersonnelObj")) {
            unlockRecord.set("unlock_user_type__c", "1");
        } else {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_10));
        }

        serviceFacade.saveObjectData(User.systemUser(tenantId), unlockRecord);

        return ConsumerScanUnlock.Result.builder()
                .productCode(sku.get(ProductFields.PRODUCT_CODE, String.class))
                .productName(sku.get(ProductFields.NAME, String.class))
                .build();
    }

    @Override
    public StoreScanCode.Result storeScanCode(StoreScanCode.Arg arg) {
        return new StoreScanCode.Result();
    }

    @Override
    public StoreReward.Result storeReward(StoreReward.Arg arg) {
        return new StoreReward.Result();
    }

    private IObjectData findStoreOwnerOrAgentByUnionId(String storeTenantId, String storeId, String userUnionId) {
        IObjectData storeEmployee = findStoreEmployee(storeTenantId, storeId, userUnionId);
        if (Objects.nonNull(storeEmployee)) {
            return storeEmployee;
        }
        IObjectData agent = findStoreAgent(storeTenantId, storeId, userUnionId);
        if (Objects.nonNull(agent)) {
            return agent;
        }
        return null;
    }

    private IObjectData findStoreAgent(String storeTenantId, String storeId, String userUnionId) {
        IFilter identityFilter = new Filter();
        identityFilter.setFieldName(PersonnelFields.MENGNIU_WX_UNION_ID);
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(userUnionId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(identityFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, storeTenantId, ApiNames.PERSONNEL_OBJ, stq, Lists.newArrayList(
                CommonFields.ID,
                CommonFields.TENANT_ID,
                CommonFields.OBJECT_DESCRIBE_API_NAME,
                CommonFields.NAME,
                PersonnelFields.MENGNIU_WX_UNION_ID
        ));

        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        IObjectData user = data.get(0);

        List<RelevantTeam> storeMembers;
        try {
            Map<String, List<String>> teamArg = Maps.newHashMap();
            teamArg.put(ApiNames.ACCOUNT_OBJ, Lists.newArrayList(storeId));
            Map<String, Map<String, List<RelevantTeam>>> members = serviceFacade.batchFindTeamMember(storeTenantId, teamArg);
            storeMembers = members.get(ApiNames.ACCOUNT_OBJ).get(storeId);
        } catch (Exception ex) {
            return null;
        }

        for (RelevantTeam storeMember : storeMembers) {
            if (Objects.equals(storeMember.getMemberId(), user.getId()) && Objects.equals(storeMember.getMemberType(), 0)) {
                return user;
            }
        }

        return null;
    }

    private IObjectData findStoreEmployee(String storeTenantId, String storeId, String userUnionId) {
        IFilter identityFilter = new Filter();
        identityFilter.setFieldName(ContactFields.ACCOUNT_ID);
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(storeId));

        IFilter contactFilter = new Filter();
        contactFilter.setFieldName(ContactFields.MENGNIU_WECHAT_UNION_ID);
        contactFilter.setOperator(Operator.EQ);
        contactFilter.setFieldValues(Lists.newArrayList(userUnionId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(identityFilter, contactFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, storeTenantId, "ContactObj", stq, Lists.newArrayList(
                CommonFields.ID,
                CommonFields.TENANT_ID,
                CommonFields.OBJECT_DESCRIBE_API_NAME,
                CommonFields.NAME,
                ContactFields.MENGNIU_WECHAT_UNION_ID
        ));

        if (!CollectionUtils.isEmpty(data)) {
            return data.get(0);
        }
        return null;
    }

    private boolean enableConsumerScanCodeActivity(String tenantId, String snId) {
        IObjectData sn = findSnObj(tenantId, snId);
        IObjectData sku = findSkuObjBySnObj(tenantId, sn);
        IObjectData store = null;
        try {
            store = getStoreByStoreSign(tenantId, snId, true);
        } catch (AppBusinessException ex) {
            log.info("getStoreByStoreSign error,", ex);
        }

        if (Objects.isNull(store)) {
            return false;
        }

        IObjectData activity = findConsumerScanCodeActivity(tenantId, sku, store, sn);
        return Objects.nonNull(activity);
    }

    protected GetOpenIdByToken.Result convertToWeChatAccountInformation(String tenantId, String appId, String token) {
        GetOpenIdByToken.Args arg = new GetOpenIdByToken.Args();
        arg.setTenantId(tenantId);
        arg.setToken(token);
        arg.setAppId(appId);

        log.info("convert to WeChat account information arg : {}", arg);

        GetOpenIdByToken.Result result = shopMMService.getOpenIdByToken(arg);

        if (result.getErrorCode() != 0) {
            log.warn("convert to WeChat account information error : {}.{}", result.getErrorCode(), result.getMessage());
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_13));
        }

        log.info("convert to WeChat account information result : {}", result);
        return result;
    }

    protected IObjectData findSkuObjBySnObj(String tenantId, IObjectData snObj) {
        String productId = snObj.get(FMCGSerialNumberFields.PRODUCT_ID, String.class);
        if (Strings.isNullOrEmpty(productId)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_14));
        }

        IFilter idFilter = new Filter();
        idFilter.setFieldName("_id");
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(productId));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(idFilter);
        query.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PRODUCT_OBJ,
                query,
                Lists.newArrayList("_id", ProductFields.NAME, ProductFields.PRODUCT_CODE)
        );

        if (CollectionUtils.isEmpty(data)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_15));
        }
        return data.get(0);
    }

    protected IObjectData findSnObj(String tenantId, String id) {
        try {
            return serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), id, "FMCGSerialNumberObj");
        } catch (Exception ex) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_16));
        }
    }

    private void validateCode(String tenantId, String code) {
        BizCodePO po = bizCodeDAO.getValidBizCode(tenantId, Lists.newArrayList(code));
        if (po != null) {
            if (po.getStatus() == BizCodeStatusEnum.USED.code()) {
                throw new RewardFmcgException("10011", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_8));
            } else {
                QueryTransferDetailForReceipts.Arg arg = new QueryTransferDetailForReceipts.Arg();
                arg.setBusinessId(po.getBizCode());
                arg.setTenantId(String.valueOf(po.getBizDataMap().get("receiverTenantId")));
                TransferDetail transfer = receiveMoneyService.queryTransferDetailForReceipts(arg).getTransferDetail();
                if (TransferDetailStatusEnum.SUCCESS.codes().contains(transfer.getStatus())) {
                    throw new ValidateException(I18N.text(I18NKeys.SCAN_CODE_SERVICE_2));
                }
            }
        }
    }

    private CloseWXOrder.Result closeWxOrder(BizCodePO bizCode) {
        CloseWXOrder.Arg closeWXOrderArg = new CloseWXOrder.Arg();
        closeWXOrderArg.setBusinessId(bizCode.getBizCode());
        Integer receiverTenantId = (Integer) bizCode.getBizDataMap().get("receiverTenantId");
        closeWXOrderArg.setTenantId(String.valueOf(receiverTenantId));
        return receiveMoneyService.closeWXOrder(closeWXOrderArg);
    }

    private RewardMethodEnum validateRewardMethod(List<ActivityRewardRulePO> rewardRulePOS) {
        String rewardMethodEnum = null;
        for (ActivityRewardRulePO activityRewardRulePO : rewardRulePOS) {
            String tempRewardMethodEnum = activityRewardRulePO.getRewardDetails().get(activityRewardRulePO.getRewardDetails().size() - 1).getRewardStrategy().getRewardMethod();
            if (rewardMethodEnum == null) {
                rewardMethodEnum = tempRewardMethodEnum;
            } else if (!rewardMethodEnum.equals(tempRewardMethodEnum)) {
                throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_9));
            }
        }
        return RewardMethodEnum.get(rewardMethodEnum);
    }


    private IObjectData findAgentSalesActivity(String tenantId, IObjectData skuObj, IObjectData snObj, IObjectData store) {
        List<String> departmentIds = activityService.getDepartmentByStore(tenantId, store);

        List<ActivityTypePO> types = activityTypeDAO.queryByTypeTemplateId(tenantId, ScanCodeActionConstants.BIG_DATE_ACTIVITY_TYPE_TEMPLATE_ID);
        List<String> typeIds = types.stream().map(MongoPO::getUniqueId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(store.getDataOwnDepartment())) {
            throw new RewardFmcgException("10011", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_10));
        }

        IObjectData activity = findAgentSalesActivity(tenantId, skuObj, snObj, store, TPMActivityProductRangeFields.MATCH_METHOD__BIG_DATE, departmentIds, typeIds);
        if (Objects.isNull(activity)) {
            activity = findAgentSalesActivity(tenantId, skuObj, snObj, store, TPMActivityProductRangeFields.MATCH_METHOD__NEW_GOODS, departmentIds, typeIds);
        }
        return activity;
    }

    private IObjectData findAgentSalesActivity(
            String tenantId,
            IObjectData skuObj,
            IObjectData snObj,
            IObjectData store,
            String matchMethod,
            List<String> departmentIds,
            List<String> typeIds) {

        if (CollectionUtils.isEmpty(store.getDataOwnDepartment())) {
            throw new RewardFmcgException("10009", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_11));
        }

        // 查询活动和产品范围的关系，Map<ActivityId,ProductRangeDetailId>
        Map<String, List<String>> relations = queryAgentSalesActivityProductRangeRelations(tenantId, skuObj, snObj, matchMethod);
        if (MapUtils.isEmpty(relations)) {
            log.info("no relation found : {}/{}/{}", skuObj.getName(), snObj.getName(), matchMethod);
            return null;
        }

        List<IObjectData> activities = activityService.findActivityByStore(tenantId, departmentIds, store, new ArrayList<>(relations.keySet()), typeIds);
        if (CollectionUtils.isEmpty(activities)) {
            log.info("no activities found : department:{},storeId:{},activities:{},types:{}", departmentIds, store.getId(), new ArrayList<>(relations.keySet()), typeIds);
            return null;
        }

        List<String> activityIds = activities.stream().map(IObjectData::getId).collect(Collectors.toList());
        Map<String, IObjectData> activityMap = activities.stream().collect(Collectors.toMap(DBRecord::getId, v -> v));

        log.info("agent sales activities : {}", activityIds);

        List<String> productRangeIds = relations.values().stream().flatMap(List::stream).collect(Collectors.toList());
        List<IObjectData> activityProducts = queryActivityProducts(tenantId, activityIds, productRangeIds);

        activityProducts.forEach(product -> {
            IObjectData activity = activityMap.get(product.get(TPMActivityProductRangeFields.ACTIVITY_ID, String.class));

            BigDecimal productDeductAmount = product.get(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal activityDeductAmount = activity.get(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            if (productDeductAmount.compareTo(activityDeductAmount) >= 0) {
                activity.set(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, productDeductAmount);
                activity.set("matched_product_range_id", product.getId());
            }
        });

        activities.sort((a, b) -> {
            BigDecimal deductAmountA = a.get(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal deductAmountB = b.get(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            if (deductAmountB.compareTo(deductAmountA) == 0) {
                long createTimeA = a.get(CommonFields.CREATE_TIME, Long.class, 0L);
                long createTimeB = b.get(CommonFields.CREATE_TIME, Long.class, 0L);
                return createTimeA < createTimeB ? 1 : -1;
            } else {
                return deductAmountB.compareTo(deductAmountA);
            }
        });

        return activities.isEmpty() ? null : activities.get(0);
    }

    private List<IObjectData> queryActivityProducts(String tenantId, List<String> activityIds, List<String> ids) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(CommonFields.ID, Operator.IN, ids),
                SearchQueryUtil.filter(TPMActivityProductRangeFields.ACTIVITY_ID, Operator.IN, activityIds)
        ));

        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID,
                        TPMActivityProductRangeFields.ACTIVITY_ID,
                        TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT,
                        TPMActivityProductRangeFields.CONSUMER_RETAIL_PRICE,
                        TPMActivityProductRangeFields.TO_BE_EXPIRED_DAYS,
                        TPMActivityProductRangeFields.PRODUCT_ID,
                        TPMActivityProductRangeFields.MATCH_METHOD)
        );

        data.sort((a, b) -> b.get(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, BigDecimal.class, BigDecimal.ZERO).compareTo(a.get(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, BigDecimal.class, BigDecimal.ZERO)));

        return data;
    }

    public IObjectData getStoreByStoreSign(String tenantId, String snCodeId, boolean enableSaleOutLogic) {
        IObjectData storeSignCodeStatusObj = fmcgSerialNumberService.getStoreSignSerialNumberStatusObj(tenantId, snCodeId, enableSaleOutLogic);
        String storeId = storeSignCodeStatusObj.get(FMCGSerialNumberStatusFields.ACCOUNT_ID, String.class);
        String currentTenantId = storeSignCodeStatusObj.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class);
        if (Strings.isNullOrEmpty(currentTenantId)) {
            log.info("storeSignCodeStatusObj:{}", storeSignCodeStatusObj);
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_12));
        }
        if (Strings.isNullOrEmpty(storeId)) {
            return null;
        }

        try {
            IObjectData store = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), storeId, ApiNames.ACCOUNT_OBJ);
            store.set(AccountFields.RELATED_ENTERPRISE_ID, currentTenantId);
            return store;
        } catch (ObjectDataNotFoundException exception) {
            log.info("data id:{}", storeId, exception);
            throw new ValidateException(I18N.text(I18NKeys.SCAN_CODE_SERVICE_3));
        }
    }

    private Map<String, List<String>> queryAgentSalesActivityProductRangeRelations(String tenantId, IObjectData sukObj, IObjectData snObj, String matchMethod) {
        // 产品保质期
        Integer qualityGuaranteePeriod = sukObj.get(ProductFields.QUALITY_GUARANTEE_PERIOD, Integer.class);
        if (Objects.isNull(qualityGuaranteePeriod)) {
            log.warn("missing quality guarantee period settings : {}", sukObj.getName());
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_13));
        }

        // 生产日期
        Long manufactureDate = snObj.get(FMCGSerialNumberFields.MANUFACTURE_DATE, Long.class);
        if (Objects.isNull(manufactureDate)) {
            log.warn("missing manufacture settings : {}/{}", sukObj.getName(), snObj.getName());
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_14));
        }

        // 生产日期 + 产品保质期 - 当前日期 = 剩余有效期
        Long remainingDays = (toDayStart(manufactureDate) + qualityGuaranteePeriod * 24 * 60 * 60 * 1000L - toDayStart(System.currentTimeMillis())) / (24 * 60 * 60 * 1000);
        log.info("product remaining quality guarantee days : {}", remainingDays);

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setOffset(0);
        query.setLimit(-1);
        StringBuilder pattern = new StringBuilder();
        int index = 1;

        Filter productIdFilter = new Filter();
        productIdFilter.setFieldName(TPMActivityProductRangeFields.PRODUCT_ID);
        productIdFilter.setOperator(Operator.EQ);
        productIdFilter.setFieldValues(Lists.newArrayList(sukObj.getId()));
        query.getFilters().add(productIdFilter);
        pattern.append(index++);

        Filter dateFilter = new Filter();
        dateFilter.setFieldName(TPMActivityProductRangeFields.TO_BE_EXPIRED_DAYS);
        if (matchMethod.equals(TPMActivityProductRangeFields.MATCH_METHOD__NEW_GOODS)) {
            dateFilter.setOperator(Operator.LTE);
        } else {
            dateFilter.setOperator(Operator.GTE);
        }
        dateFilter.setFieldValues(Lists.newArrayList(remainingDays.toString()));
        query.getFilters().add(dateFilter);

        if (describeCacheService.isExistField(tenantId, ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ, TPMActivityProductRangeFields.MANUFACTURE_DATE_START)) {
            Filter manufactureDateRangeStartFilter = SearchQueryUtil.filter(TPMActivityProductRangeFields.MANUFACTURE_DATE_START, Operator.LTE, Lists.newArrayList(String.valueOf(manufactureDate)));
            Filter manufactureDateRangeEndFilter = SearchQueryUtil.filter(TPMActivityProductRangeFields.MANUFACTURE_DATE_END, Operator.GTE, Lists.newArrayList(String.valueOf(manufactureDate)));
            query.getFilters().add(manufactureDateRangeStartFilter);
            query.getFilters().add(manufactureDateRangeEndFilter);
            pattern.append(" and (").append(index++).append(" or ").append(index++).append(" and ").append(index++).append(") ");
        } else {
            pattern.append(" and ").append(index++).append(" ");
        }

        Filter matchMethodFilter = new Filter();
        matchMethodFilter.setFieldName(TPMActivityProductRangeFields.MATCH_METHOD);
        matchMethodFilter.setOperator(Operator.EQ);
        matchMethodFilter.setFieldValues(Lists.newArrayList(matchMethod));
        query.getFilters().add(matchMethodFilter);

        if (matchMethod.equals(TPMActivityProductRangeFields.MATCH_METHOD__NEW_GOODS)) {
            pattern.append(" and ").append(index).append(" ");
        } else {
            Filter noMatchMethodFilter = new Filter();
            noMatchMethodFilter.setFieldName(TPMActivityProductRangeFields.MATCH_METHOD);
            noMatchMethodFilter.setOperator(Operator.IS);
            noMatchMethodFilter.setFieldValues(Lists.newArrayList());
            query.getFilters().add(noMatchMethodFilter);
            pattern.append(" and (").append(index++).append(" or ").append(index).append(") ");
        }
        query.setPattern(pattern.toString());


        Map<String, List<String>> relation = new HashMap<>();

        QueryDataUtil.findAndConsume(
                serviceFacade,
                User.systemUser(tenantId),
                ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ,
                query,
                Lists.newArrayList(CommonFields.ID, TPMActivityProductRangeFields.ACTIVITY_ID, TPMActivityProductRangeFields.MATCH_METHOD),
                data -> data.forEach(datum -> {
                    String activityId = datum.get(TPMActivityProductRangeFields.ACTIVITY_ID, String.class);
                    if (relation.containsKey(activityId)) {
                        relation.get(activityId).add(datum.getId());
                    } else {
                        relation.put(activityId, Lists.newArrayList(datum.getId()));
                    }
                })
        );

        return relation;
    }

    private long toDayStart(long time) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
        return localDateTime.toLocalDate().atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    private PaymentInfoDTO formPaymentInfoDTO(String tenantId, String bizCode, List<String> codeIds, BigDecimal amount, String appId, String openId, Integer receiverTenantId, Map<String, Object> bizDataMap) {

        judgeEaHasUnion(receiverTenantId);
        PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        BizCodePO bizCodePO = new BizCodePO();
        bizCodePO.setBizCode(bizCode);
        bizCodePO.setRelatedBizIds(codeIds);
        bizCodePO.setBizType(BizTypeEnum.DEDUCT_PAY.code());
        bizCodePO.setStatus(BizCodeStatusEnum.INIT.code());
        bizCodePO.setBizDataMap(bizDataMap);
        bizDataMap.put("receiverTenantId", receiverTenantId);
        FormWxPayMini.Arg formArg = new FormWxPayMini.Arg();
        formArg.setAmount(amount);
        FormWxPayMini.Payer payer = new FormWxPayMini.Payer();
        payer.setWxOpenId(openId);
        payer.setWxAppId(appId);
        payer.setDisplayName("消费者");//ignorei18n
        formArg.setPayer(payer);
        FormWxPayMini.Receiver receiver = new FormWxPayMini.Receiver();
        receiver.setReceiverUserId(1000L);
        receiver.setReceiverTenantId(receiverTenantId);
        receiver.setReceiverTenantAccount(eieaConverter.enterpriseIdToAccount(receiverTenantId));
        formArg.setReceiver(receiver);
        formArg.setBusinessId(bizCodePO.getBizCode());
        formArg.setDescribeTitle("产品扫码减价支付");//ignorei18n
        formArg.setDescribeContent("产品扫码减价支付");//ignorei18n
        FormWxPayMini.Result result = receiveMoneyService.formWxPayMini(formArg);
        bizCodeDAO.add(tenantId, -10000, bizCodePO);
        paymentInfo.setSign(result.getSign());
        paymentInfo.setPackages(result.getPackages());
        paymentInfo.setSignType(result.getSignType());
        paymentInfo.setTimeStamp(result.getTimeStamp());
        paymentInfo.setNonceStr(result.getNonceStr());
        paymentInfo.setBizCode(bizCodePO.getBizCode());
        return paymentInfo;
    }

    private PaymentInfoDTO dealFormWXOrder(String tenantId, String appId, String openId, List<String> codeIds, Map<String, String> code2activityMap, Map<String, ActivityRewardRulePO> activity2RewardRuleMap, Map<String, String> code2StoreMap, Map<String, List<String>> productRange2CodeMap) {
        String receiverTenantId = null;
        BigDecimal amount = BigDecimal.ZERO;
        for (Map.Entry<String, String> code2ActivityEntry : code2activityMap.entrySet()) {
            ActivityRewardRulePO activityRewardRulePO = activity2RewardRuleMap.get(code2ActivityEntry.getValue());
            String tempReceiver = rewardRuleManager.getConsumerReceiverAccount(tenantId, code2ActivityEntry.getKey(), activityRewardRulePO);
            if (receiverTenantId == null) {
                receiverTenantId = tempReceiver;
            } else if (!tempReceiver.equals(receiverTenantId)) {
                throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_17));
            }
        }

        if (receiverTenantId == null) {
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_18));
        }

        //List<IObjectData> snCodeObjs = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, codeIds, ApiNames.FMCG_SERIAL_NUMBER_OBJ);
        List<String> rangeIds = new ArrayList<>(productRange2CodeMap.keySet());
        List<IObjectData> productRangeDetails = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, rangeIds, ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ);
        if (productRangeDetails.isEmpty() || productRangeDetails.size() != rangeIds.size()) {
            log.info("rangeIds:{},productRangeDetails:{}", rangeIds, productRangeDetails);
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_SCAN_CODE_SERVICE_19));
        }
        List<Map<String, Object>> rewardDetails = new ArrayList<>();
        List<Map<String, Object>> salesRecords = new ArrayList<>();
        String bizCode = IdentityIdGenerator.formStorePromotionIdentityId();
        Map<String, IObjectData> storeMap = new HashMap<>();
        for (IObjectData productRangeDetail : productRangeDetails) {
            BigDecimal retail = productRangeDetail.get(TPMActivityProductRangeFields.CONSUMER_RETAIL_PRICE, BigDecimal.class, new BigDecimal("********"));
            BigDecimal deduct = productRangeDetail.get(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            String activityId = productRangeDetail.get(TPMActivityProductRangeFields.ACTIVITY_ID, String.class);
            String productId = productRangeDetail.get(TPMActivityProductRangeFields.PRODUCT_ID, String.class);
            for (String codeId : productRange2CodeMap.get(productRangeDetail.getId())) {
                amount = amount.add(retail).subtract(deduct);
                IObjectData status = fmcgSerialNumberService.getStoreSignSerialNumberStatusObj(tenantId, codeId, false);
                IObjectData saleRecord = formStorePromotionRecordObj(tenantId, productId, codeId, retail, deduct, retail.subtract(deduct), activityId, getStore(storeMap, code2StoreMap.get(codeId)), status.getDescribeApiName(), status.getId(), receiverTenantId, bizCode);
                IObjectData activityRewardDetail = formRewardDetail(tenantId, bizCode, deduct, activityId, appId + "." + openId, codeId, productId, saleRecord.getDescribeApiName(), saleRecord.getId(), TPMActivityRewardDetailFields.RewardType.DISCOUNT_PAY, getStore(storeMap, code2StoreMap.get(codeId)).getId());
                rewardDetails.add(ObjectDataExt.toMap(activityRewardDetail));
                salesRecords.add(ObjectDataExt.toMap(saleRecord));
            }
        }
        Map<String, Object> bizDataMap = new HashMap<>();
        bizDataMap.put("code2activityMap", code2activityMap);
        bizDataMap.put(ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, JSON.toJSONString(rewardDetails));
        bizDataMap.put(ApiNames.STORE_PROMOTION_RECORD_OBJ, JSON.toJSONString(salesRecords));
        bizDataMap.put("code2StoreMap", code2StoreMap);
        return formPaymentInfoDTO(tenantId, bizCode, codeIds, amount.setScale(2, RoundingMode.HALF_UP), appId, openId, Integer.valueOf(receiverTenantId), bizDataMap);

    }

    private IObjectData getStore(Map<String, IObjectData> storeMap, String storeCode) {
        if (storeMap.get(storeCode) == null) {
            String[] sp = storeCode.split("#");
            IObjectData store = serviceFacade.findObjectDataIgnoreAll(User.systemUser(sp[0]), sp[1], ApiNames.ACCOUNT_OBJ);
            storeMap.put(storeCode, store);
        }
        return storeMap.get(storeCode);
    }

    private IObjectData formRewardDetail(String tenantId, String businessId, BigDecimal rewardAmount, String activityId, String rewardPerson, String snCodeId, String productId, String relatedApiName, String relatedDataId, String rewardType, String accountId) {
        IObjectData rewardDetail = new ObjectData();
        rewardDetail.setTenantId(tenantId);
        rewardDetail.setId(IdGenerator.get());
        rewardDetail.setOwner(Lists.newArrayList("-10000"));
        rewardDetail.setDescribeApiName(ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ);
        rewardDetail.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
        rewardDetail.set(TPMActivityRewardDetailFields.BUSINESS_ID, businessId);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_VALUE, rewardAmount);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_TYPE, rewardType);
        rewardDetail.set(TPMActivityRewardDetailFields.ACTIVITY_ID, activityId);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_PART, "消费者");//ignorei18n
        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_PERSON_ID, rewardPerson);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARDED_PERSON, "消费者");//ignorei18n
        rewardDetail.set(TPMActivityRewardDetailFields.SERIAL_NUMBER_ID, snCodeId);
        rewardDetail.set(TPMActivityRewardDetailFields.PRODUCT_ID, productId);
        rewardDetail.set(TPMActivityRewardDetailFields.RELATED_OBJECT_API_NAME, relatedApiName);
        rewardDetail.set(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID, relatedDataId);
        rewardDetail.set(TPMActivityRewardDetailFields.STATUS, TPMActivityRewardDetailFields.Status.DONE);
        rewardDetail.set(TPMActivityRewardDetailFields.REWARD_TIME, System.currentTimeMillis());
        rewardDetail.set(TPMActivityRewardDetailFields.ACCOUNT_ID__C, accountId);
        return rewardDetail;
    }

    private IObjectData formStorePromotionRecordObj(String tenantId, String productId, String snCodeId, BigDecimal standardAmount, BigDecimal discount, BigDecimal realAmount, String activityId, IObjectData account, String relatedApiName, String relatedDataId, String receiveTenantId, String businessId) {
        IObjectData storePromotionRecordObj = new ObjectData();
        storePromotionRecordObj.setTenantId(tenantId);
        storePromotionRecordObj.setId(IdGenerator.get());
        storePromotionRecordObj.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
        storePromotionRecordObj.setOwner(Lists.newArrayList("-10000"));
        storePromotionRecordObj.setDescribeApiName(ApiNames.STORE_PROMOTION_RECORD_OBJ);
        storePromotionRecordObj.set(StorePromotionRecordFields.PRODUCT_ID, productId);
        storePromotionRecordObj.set(StorePromotionRecordFields.SERIAL_NUMBER_ID, snCodeId);
        storePromotionRecordObj.set(StorePromotionRecordFields.STANDARD_PRICE, standardAmount);
        storePromotionRecordObj.set(StorePromotionRecordFields.DISCOUNT_AMOUNT, discount);
        storePromotionRecordObj.set(StorePromotionRecordFields.ACTUAL_SALES_AMOUNT, realAmount);
        storePromotionRecordObj.set(StorePromotionRecordFields.ACTIVITY_ID, activityId);
        storePromotionRecordObj.set(StorePromotionRecordFields.ACCOUNT_ID, account.getId());
        storePromotionRecordObj.set(StorePromotionRecordFields.ACCOUNT_NAME, account.getName());
        storePromotionRecordObj.set(StorePromotionRecordFields.ACCOUNT_TENANT_ID, account.getTenantId());
        storePromotionRecordObj.set(StorePromotionRecordFields.SALE_DATE, System.currentTimeMillis());
        storePromotionRecordObj.set(StorePromotionRecordFields.RELATED_OBJECT_API_NAME, relatedApiName);
        storePromotionRecordObj.set(StorePromotionRecordFields.RELATED_OBJECT_DATA_ID, relatedDataId);
        storePromotionRecordObj.set(StorePromotionRecordFields.RECEIVE_TENANT_ID, receiveTenantId);
        storePromotionRecordObj.set(StorePromotionRecordFields.BUSINESS_ID, businessId);
        storePromotionRecordObj.set(StorePromotionRecordFields.RECEIVE_TENANT_NAME, getTenantName(receiveTenantId));
        return storePromotionRecordObj;
    }

    private String getTenantName(String tenantId) {
        GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        return enterpriseEditionService.getSimpleEnterprise(arg).getSimpleEnterprise().getEnterpriseName();
    }

    private void judgeEaHasUnion(Integer tenantId) {
        if (UNION_OPEN_CACHE.getIfPresent(tenantId) != null) {
            log.info("no cache for union open cache.tenantId:{}", tenantId);
            return;
        }

        QueryEnterpriseUnionISV.Arg arg = new QueryEnterpriseUnionISV.Arg();
        arg.setTenantId(tenantId.toString());
        QueryEnterpriseUnionISV.Result result = receiveMoneyService.queryEnterpriseUnionISV(arg);
        if (CollectionUtils.isEmpty(result.getIsvList())) {
            log.info("该企业未绑定银联账号。{}", tenantId);
            throw new ValidateException(I18N.text(I18NKeys.SCAN_CODE_SERVICE_4));
        } else {
            UNION_OPEN_CACHE.put(tenantId, true);
        }
    }


    private RLock tryLock(String key) {

        RLock lock = redissonCmd.getLock(key);

        log.info("try lock activity : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return lock;
        }
        try {
            if (lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.MILLISECONDS)) {
                return lock;
            } else {
                throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_17));
            }
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_SCAN_CODE_SERVICE_18));
        }
    }

    private void unlock(RLock lock) {
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
