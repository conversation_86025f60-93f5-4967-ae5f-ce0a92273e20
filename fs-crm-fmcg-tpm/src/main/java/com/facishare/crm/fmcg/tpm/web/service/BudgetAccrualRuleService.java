package com.facishare.crm.fmcg.tpm.web.service;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetAccrualRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetAccrualRulePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.StatusType;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.utils.ListParameterUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetAccrualRuleVO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetAccrualRuleManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IBudgetAccrualRuleService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/9 下午2:32
 */
@Slf4j
@Service
public class BudgetAccrualRuleService extends BaseService implements IBudgetAccrualRuleService {

    @Resource
    private BudgetAccrualRuleDAO budgetAccrualRuleDAO;

    @Resource
    private BudgetTypeDAO budgetTypeDAO;

    @Resource
    private IBudgetAccrualRuleManager budgetAccrualRuleManager;

    private static final String ACCRUAL_PLUGIN = "tpm_budget_accrual";
    private static final Set<String> VALIDATE_API_NAME = new HashSet<>();

    static {
        VALIDATE_API_NAME.add(ApiNames.TPM_ACTIVITY_OBJ);
        VALIDATE_API_NAME.add(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
    }

    @Override
    public AddBudgetAccrualRule.Result add(AddBudgetAccrualRule.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);
        String tenantId = context.getTenantId();
        //校验规则
        budgetAccrualRuleManager.accrualRuleInfoValidate(tenantId, arg.getBudgetAccrualRuleVO());
        //计提条件
        budgetAccrualRuleManager.enclosureConditionFilter(tenantId, context.getEmployeeId(), arg.getBudgetAccrualRuleVO());

        BudgetAccrualRulePO budgetAccrualRulePO = BudgetAccrualRulePO.fromVO(arg.getBudgetAccrualRuleVO());
        if (Objects.isNull(budgetAccrualRulePO)) {
            throw new ValidateException("Invalid parameter error. budgetAccrualRulePO can not be null or empty.");
        }
        String id = budgetAccrualRuleDAO.add(tenantId, context.getEmployeeId(), budgetAccrualRulePO);
        //更新计提对象的 计提对象字段值，
        budgetAccrualRuleManager.publishSyncAccrualRuleFieldTask(tenantId);
        //给要计提的对象 添加计提状态字段
        budgetAccrualRuleManager.syncAddTriggerRuleStatusField(tenantId, arg.getBudgetAccrualRuleVO().getApiName());
        if (StatusType.ENABLE.value().equals(budgetAccrualRulePO.getStatus())) {
            budgetAccrualRuleManager.bindObjectPluginInstance(tenantId, context.getEmployeeId(), budgetAccrualRulePO.getApiName(), ACCRUAL_PLUGIN);
        }
        // 埋点日志
        asyncAddBudgetAccrualRuleLog(context.getEmployeeId(), tenantId, budgetAccrualRulePO);
        return AddBudgetAccrualRule.Result.builder()
                .budgetAccrualRuleVO(loadBudgetAccrualRuleVO(tenantId, id))
                .build();
    }

    private void asyncAddBudgetAccrualRuleLog(Integer employeeId, String tenantId, BudgetAccrualRulePO budgetAccrualRulePO) {
        BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_ACCRUAL_RULE, BuryOperation.CREATE);
        if (budgetAccrualRulePO.getApiName().endsWith("__c")) {
            BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_ACCRUAL_RULE_CUSTOM_OBJ, BuryOperation.CREATE);
        } else {
            BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_ACCRUAL_RULE_PRESET_OBJ, BuryOperation.CREATE);
        }
        //状态为 启用
        if (StatusType.ENABLE.value().equals(budgetAccrualRulePO.getStatus())) {
            BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_ACCRUAL_RULE_ENABLE, BuryOperation.CREATE);
        } else {
            BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_ACCRUAL_RULE_DISABLE, BuryOperation.CREATE);
        }
        if (budgetAccrualRulePO.getAccrualRuleNodes().size() > 1) {
            BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_ACCRUAL_RULE_OBJ_COUNT, BuryOperation.CREATE);
        }
    }

    @Override
    public GetBudgetAccrualRule.Result get(GetBudgetAccrualRule.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        if (Strings.isNullOrEmpty(arg.getId())) {
            throw new ValidateException("Invalid parameter error. Parameter '_id' can not be null or empty.");
        }
        BudgetAccrualRuleVO budgetAccrualRuleVO = loadBudgetAccrualRuleVO(context.getTenantId(), arg.getId());
        return GetBudgetAccrualRule.Result.builder()
                .budgetAccrualRuleVO(budgetAccrualRuleVO)
                //查询是否有数据关联，
                .flowState(budgetAccrualRuleManager.isExistAccrualDataRelated(context.getTenantId(), budgetAccrualRuleVO.getApiName(), arg.getId()))
                .build();
    }

    @Override
    public ListBudgetAccrualRule.Result list(ListBudgetAccrualRule.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);
        ListParameterUtils.correctListParameter(arg);
        List<BudgetAccrualRulePO> accrualRulePOList = budgetAccrualRuleDAO.list(
                context.getTenantId(),
                arg.getKeyword(),
                arg.getObjectApiName(),
                arg.getLimit(), arg.getOffset());
        long count = budgetAccrualRuleDAO.countAll(context.getTenantId(), arg.getKeyword(), arg.getObjectApiName());
        return ListBudgetAccrualRule.Result.builder()
                .data(loadListBudgetAccrualRuleVO(context.getTenantId(), accrualRulePOList))
                .total(count)
                .build();
    }

    @Override
    public EditBudgetAccrualRule.Result edit(EditBudgetAccrualRule.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);
        String id = arg.getBudgetAccrualRuleVO().getId();
        BudgetAccrualRulePO oldPO = budgetAccrualRuleDAO.get(context.getTenantId(), id);
        if (oldPO == null) {
            throw new ValidateException("budgetAccrualRulePO is not found...");
        }
        // version 校验
        if (oldPO.getVersion() != arg.getBudgetAccrualRuleVO().getVersion()) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_VERSION_VALIDATION_ERROR));
        }
        // 状态为启用或者存在数据关联，不可编辑。
        if (StatusType.ENABLE.value().equals(arg.getBudgetAccrualRuleVO().getStatus()) ||
                budgetAccrualRuleManager.isExistAccrualDataRelated(context.getTenantId(),
                        arg.getBudgetAccrualRuleVO().getApiName(), id)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_STATUS_EDIT_ERROR));
        }
        // 规则校验
        budgetAccrualRuleManager.accrualRuleEditValidate(context.getTenantId(), arg.getBudgetAccrualRuleVO(), oldPO);
        //筛选条件配置
        budgetAccrualRuleManager.enclosureConditionFilter(context.getTenantId(), context.getEmployeeId(), arg.getBudgetAccrualRuleVO());
        BudgetAccrualRulePO budgetAccrualRulePO = BudgetAccrualRulePO.fromEditVO(oldPO, arg.getBudgetAccrualRuleVO());
        if (Objects.isNull(budgetAccrualRulePO)) {
            throw new ValidateException("Invalid parameter error. budgetAccrualRulePO can not be null or empty.");
        }
        budgetAccrualRuleDAO.edit(context.getTenantId(), context.getEmployeeId(), id, budgetAccrualRulePO);
        //更新计提对象的 计提对象字段，
        budgetAccrualRuleManager.publishSyncAccrualRuleFieldTask(context.getTenantId());
        return EditBudgetAccrualRule.Result.builder()
                .budgetAccrualRuleVO(loadBudgetAccrualRuleVO(context.getTenantId(), id))
                .build();
    }

    @Override
    public SetBudgetAccrualRuleStatus.Result setStatus(SetBudgetAccrualRuleStatus.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);
        BudgetAccrualRulePO budgetAccrualRulePO = budgetAccrualRuleDAO.get(context.getTenantId(), arg.getId());
        if (budgetAccrualRulePO == null) {
            throw new ValidateException("budgetAccrualRulePO is not found...");
        }
        //更改状态为启用，对象只可有一个启用规则
        if (StatusType.ENABLE.value().equals(arg.getStatus()) && !arg.isForceEnable()) {
            if (budgetAccrualRuleDAO.isExistsByApiNameWithRecordType(
                    context.getTenantId(),
                    budgetAccrualRulePO.getApiName(),
                    budgetAccrualRulePO.getRecordType())
            ) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_EXISTS_USED_RULE_ERROR));
            }
        }
        //更新状态
        budgetAccrualRuleDAO.setStatus(context.getTenantId(), context.getEmployeeId(), arg.getId(), arg.getStatus());
        // 如果可启用规则，则绑定对象插件
        if (StatusType.ENABLE.value().equals(arg.getStatus())) {
            budgetAccrualRuleManager.bindObjectPluginInstance(context.getTenantId(), context.getEmployeeId(), budgetAccrualRulePO.getApiName(), ACCRUAL_PLUGIN);
        }
        return SetBudgetAccrualRuleStatus.Result.builder()
                .budgetAccrualRuleVO(BudgetAccrualRulePO.toVO(budgetAccrualRuleDAO.get(context.getTenantId(), arg.getId())))
                .build();
    }

    @Override
    public DeleteBudgetAccrualRule.Result delete(DeleteBudgetAccrualRule.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        BudgetAccrualRulePO budgetAccrualRulePO = budgetAccrualRuleDAO.get(context.getTenantId(), arg.getId());
        if (budgetAccrualRulePO == null) {
            throw new ValidateException("budgetAccrualRulePO is not found...");
        }
        //是否关联业务判断，
        boolean flag = budgetAccrualRuleManager.isExistAccrualDataRelated(context.getTenantId(), budgetAccrualRulePO.getApiName(), arg.getId());
        if (budgetAccrualRulePO.getStatus().equals(StatusType.DISABLE.value()) && !flag) {
            budgetAccrualRuleDAO.delete(context.getTenantId(), context.getEmployeeId(), arg.getId());
            //删除绑定的插件
            budgetAccrualRuleManager.deleteObjectPluginInstance(
                    context.getTenantId(),
                    budgetAccrualRulePO.getApiName(),
                    budgetAccrualRulePO.getRecordType(),
                    ACCRUAL_PLUGIN);
            //更新计提对象的 计提对象字段，
            budgetAccrualRuleManager.publishSyncAccrualRuleFieldTask(context.getTenantId());
        } else {
            // 规则状态必须是为禁用且未被业务数据关联。
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_STATUS_DELETE_ERROR));
        }
        return DeleteBudgetAccrualRule.Result.builder().build();
    }

    @Override
    public ListBusinessObject.Result listBusinessObject(ListBusinessObject.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        return ListBusinessObject.Result.builder()
                .objectLabels(budgetAccrualRuleManager.findObjectsByTenantId(context.getTenantId()))
                .build();
    }

    @Override
    public GetAccrualObject.Result getAccrualObjects(GetAccrualObject.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        return GetAccrualObject.Result.builder()
                .accrualObjects(budgetAccrualRuleManager.getAccrualObjects(context.getTenantId()))
                .build();
    }


    private BudgetAccrualRuleVO loadBudgetAccrualRuleVO(String tenantId, String id) {
        BudgetAccrualRulePO budgetAccrualRulePO = budgetAccrualRuleDAO.get(tenantId, id);
        BudgetAccrualRuleVO budgetAccrualRuleVO = BudgetAccrualRulePO.toVO(budgetAccrualRulePO);

        fillBudgetAccrualRulePO(tenantId, budgetAccrualRuleVO);
        return budgetAccrualRuleVO;
    }

    private void fillBudgetAccrualRulePO(String tenantId, BudgetAccrualRuleVO budgetAccrualRuleVO) {
        if (Objects.isNull(budgetAccrualRuleVO)) {
            return;
        }

        IObjectDescribe describe = serviceFacade.findObject(tenantId, budgetAccrualRuleVO.getApiName());
        if (Objects.nonNull(describe)) {
            if (VALIDATE_API_NAME.contains(budgetAccrualRuleVO.getApiName())) {
                budgetAccrualRuleVO.setRecordTypeLabel(fillActivityTypeLabel(describe, budgetAccrualRuleVO.getRecordType()));
            } else {
                budgetAccrualRuleVO.setRecordTypeLabel(fillRecordTypeLabel(describe, budgetAccrualRuleVO.getRecordType()));
            }
        }
    }

    private String fillRecordTypeLabel(IObjectDescribe describe, String recordType) {
        // 业务对象 的业务类型，label
        IFieldDescribe fieldDescribe = describe.getFieldDescribe("record_type");
        List<Map<String, String>> options = (List<Map<String, String>>) fieldDescribe.get("options");
        if (!CollectionUtils.isEmpty(options)) {
            return options.stream()
                    .filter(v -> recordType.equals(v.get("api_name")))
                    .map(v -> v.get("label"))
                    .findFirst().orElse("");
        }
        return null;
    }

    private String fillActivityTypeLabel(IObjectDescribe describe, String activityType) {
        IFieldDescribe fieldDescribe = describe.getFieldDescribe("activity_type");
        List<Map<String, String>> options = (List<Map<String, String>>) fieldDescribe.get("options");
        if (!CollectionUtils.isEmpty(options)) {
            return options.stream()
                    .filter(v -> activityType.equals(v.get("value")))
                    .map(v -> v.get("label"))
                    .findFirst().orElse("");
        }
        return null;
    }

    private List<BudgetAccrualRuleVO> loadListBudgetAccrualRuleVO(String tenantId, List<BudgetAccrualRulePO> data) {
        //业务类型 label
        Set<String> apiNames = data.stream().map(BudgetAccrualRulePO::getApiName).collect(Collectors.toSet());
        Map<String, IObjectDescribe> describes = serviceFacade.findObjects(tenantId, apiNames);

        List<BudgetAccrualRuleVO> budgetAccrualRuleVOS = new ArrayList<>();
        for (BudgetAccrualRulePO po : data) {
            BudgetAccrualRuleVO budgetAccrualRuleVO = BudgetAccrualRulePO.toVO(po);
            //填充业务类型 label
            IObjectDescribe objectDescribe = describes.get(po.getApiName());
            if (objectDescribe != null && Objects.nonNull(budgetAccrualRuleVO)) {
                if (VALIDATE_API_NAME.contains(budgetAccrualRuleVO.getApiName())) {
                    budgetAccrualRuleVO.setRecordTypeLabel(fillActivityTypeLabel(objectDescribe, budgetAccrualRuleVO.getRecordType()));
                } else {
                    budgetAccrualRuleVO.setRecordTypeLabel(fillRecordTypeLabel(objectDescribe, budgetAccrualRuleVO.getRecordType()));
                }
            }

            budgetAccrualRuleVOS.add(budgetAccrualRuleVO);
        }
        return budgetAccrualRuleVOS;
    }

}
