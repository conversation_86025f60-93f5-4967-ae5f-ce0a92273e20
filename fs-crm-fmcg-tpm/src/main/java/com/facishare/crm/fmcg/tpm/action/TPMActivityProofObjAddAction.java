package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDetailDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofImageDTO;
import com.facishare.crm.fmcg.tpm.api.visit.VisitActionDataDTO;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.TPMDisplayReportService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IProofService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.cache.ActivityItemCache;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.AuditModeType;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.CheckinService;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.*;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.fmcg.tpm.controller.TPMActivityAgreementObjRelatedListController.YINLU_FILTER_TIME;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/12 2:47 PM
 */
@SuppressWarnings("Duplicates,unused")
@Slf4j
public class TPMActivityProofObjAddAction extends StandardAddAction implements TransactionService<BaseObjectSaveAction.Arg, BaseObjectSaveAction.Result> {

    private final IActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(IActivityTypeManager.class);
    private final IProofService proofService = SpringUtil.getContext().getBean(IProofService.class);
    private final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);
    private final CheckinService checkinService = SpringUtil.getContext().getBean(CheckinService.class);
    private final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private final ActivityItemCache activityItemCache = SpringUtil.getContext().getBean(ActivityItemCache.class);
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);
    private final IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);
    private final IActivityService activityService = SpringUtil.getContext().getBean(IActivityService.class);
    private final TPMDisplayReportService tpmDisplayReportService = SpringUtil.getContext().getBean(TPMDisplayReportService.class);

    private String auditMode = "0";


    private IObjectData dealer = null;
    private IObjectData activity = null;
    private IObjectData store = null;
    private IObjectData agreement = null;
    private ActivityTypeExt activityType = null;

    private boolean forbidCustomer = false;
    private boolean enableAi = false;

    @Override
    protected void before(Arg arg) {

        // 外勤幂等校验 - 同一个外勤的同一个动作下的同一个活动方案只能进行一次举证
        this.visitInformationIdempotentValidate();
        stopWatch.lap("visitInformationIdempotentValidate");


        // 获取活动信息
        this.activity = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), (String) arg.getObjectData().get(TPMActivityProofFields.ACTIVITY_ID), ApiNames.TPM_ACTIVITY_OBJ);
        stopWatch.lap("findActivity");

        this.activityType = activityTypeManager.findByActivityId(actionContext.getTenantId(), this.activity.getId());
        stopWatch.lap("findActivityType");
        this.forbidCustomer = Boolean.TRUE.equals(this.activityType.get().getForbidRelateCustomer());

        // 是否购买 ai license 并且 活动类型的举证节点中 Ai 启用
        enableAi = activityType.proofConfig().getAiConfig() != null && Boolean.TRUE.equals(activityType.proofConfig().getAiConfig().getEnableAiDisplayRecognition());
        // 获取门店以及活动信息
        this.store = getStore();
        stopWatch.lap("findStore");

        // 获取协议信息
        this.agreement = this.findAgreement();
        stopWatch.lap("findAgreement");

        // 获取经销商
        this.dealer = this.findAndSetDealerId();
        stopWatch.lap("findAndSetDealerId");

        // 校验举证日期以及协议明细 - 校验举证日期，且当协议不为空时，校验协议明细
        this.dateAndAgreementValidate();
        stopWatch.lap("dateValidate");

        // 校验举证频率设置
        this.validateProofFrequency();
        stopWatch.lap("validateProofFrequency");

        // 举证门店数校验
        this.validateCustomAllowStoreCount();
        stopWatch.lap("validateCustomAllowStoreCount");

        // 举证费用校验
        this.validateCustomCost();
        stopWatch.lap("validateCustomCost");

        // 校验活动方案
        this.activityValidate();
        stopWatch.lap("activityValidate");

        // 开启ai后举证校验
        this.openAIProofValidate();
        stopWatch.lap("openAIProofValidate");

        // 设置默认审核状态
        this.setAuditStatus();
        stopWatch.lap("setAuditStatus");

        // 设置默认值 - 如果是 TPM 2.0 企业，且举证项目名称赋默认值
        this.setProofItemDefaultValue(arg);
        stopWatch.lap("setProofItemDefaultValue");

        this.setProofDisplayImgDefaultValue();
        stopWatch.lap("setProofDisplayImgDefaultValue");

        super.before(arg);
        stopWatch.lap("super.before");
    }

    private void setProofDisplayImgDefaultValue() {
        tpmDisplayReportService.setProofDisplayImgDefaultValue(actionContext.getTenantId(), arg, enableAi);
    }

    private void openAIProofValidate() {
        if (!enableAi) {
            return;
        }
        try {
            tpmDisplayReportService.addProofValidation(actionContext.getTenantId(), arg);
        } catch (ValidateException ex) {
            throw ex;
        } catch (Exception e) {
            throw new ValidateException(e.getMessage() != null ? e.getMessage() : "System Validate Error.");
        }
    }

    private IObjectData getStore() {
        if (forbidCustomer) {
            return null;
        } else {
            String storeId = (String) arg.getObjectData().get(TPMActivityProofFields.STORE_ID);
            if (Strings.isNullOrEmpty(storeId)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_OBJ_ADD_ACTION_0));
            }
            return serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), storeId, ApiNames.ACCOUNT_OBJ);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result inner = super.after(arg, result);
        stopWatch.lap("super.after");

        if (!TPMGrayUtils.isSkipSaveCheckinProofData(actionContext.getTenantId())) {
            if (TPMGrayUtils.isAsyncSaveCheckinProofData(actionContext.getTenantId())) {
                ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> this.saveCheckinsAction(result))).run();
            } else {
                this.saveCheckinsAction(result);
            }
        }
        stopWatch.lap("saveCheckinsAction");

        this.processProofDisplayImgByAi(result);
        stopWatch.lap("processProofDisplayImgByAi");

        this.asyncAddTpmLog(result);
        stopWatch.lap("asyncAddTpmLog");

        return inner;
    }

    private void processProofDisplayImgByAi(Result result) {
        if (enableAi) {
            tpmDisplayReportService.asyncProcessProofDisplayImgAi(
                    actionContext.getTenantId()
                    , actionContext.getUser().getUpstreamOwnerIdOrUserId()
                    , activityType, result.getObjectData().getId()
            );
        }
    }

    private void asyncAddTpmLog(Result result) {
        try {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF, BuryOperation.CREATE, true);
            BigDecimal actualTotal = result.getObjectData().toObjectData().get(TPMActivityProofFields.ACTUAL_TOTAL, BigDecimal.class, BigDecimal.ZERO);
            BuryService.asyncTpmAmountLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), BuryModule.TPM_AMOUNT.ACTUAL_TOTAL, BuryOperation.CREATE, actualTotal.doubleValue());
        } catch (Exception ex) {
            log.warn("async add TPM log error : {}", actionContext.getTenantId(), ex);
        }
    }

    /**
     * 如果这里要改的话，请连通CheckinsManager.saveCheckinsAction一同更改
     */
    private void saveCheckinsAction(Result result) {
        String visitId = (String) result.getObjectData().get(TPMActivityProofFields.VISIT_ID);
        String actionId = (String) result.getObjectData().get(TPMActivityProofFields.ACTION_ID);

        if (Strings.isNullOrEmpty(visitId) || Strings.isNullOrEmpty(actionId)) {
            return;
        }

        String storeId = (String) result.getObjectData().get(TPMActivityProofFields.STORE_ID);
        VisitActionDataDTO data = new VisitActionDataDTO();

        List<IObjectData> masterList = queryProof(actionContext, storeId, visitId, actionId);
        List<String> masterIds = masterList.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<String> activityIds = masterList.stream().map(master -> (String) master.get(TPMActivityProofFields.ACTIVITY_ID)).distinct().collect(Collectors.toList());
        Map<String, IObjectData> activityMap = queryActivity(actionContext, activityIds).stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (oldOne, newOne) -> oldOne));
        Map<String, List<IObjectData>> detailsMap = queryActivityProofDetails(actionContext, masterIds).stream().collect(Collectors.groupingBy(detail -> (String) detail.get(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID)));
        Map<String, String> itemNameMap = queryActivityItem();
        data.setActivityProofList(Lists.newArrayList());

        for (IObjectData master : masterList) {
            ActivityProofDTO datum = new ActivityProofDTO();
            datum.setProofId(master.getId());
            datum.setRemark((String) master.get(TPMActivityProofFields.REMARK));
            String activityId = (String) master.get(TPMActivityProofFields.ACTIVITY_ID);
            if (activityMap.containsKey(activityId)) {
                datum.setActivityName(activityMap.get(activityId).getName());
            }
            datum.setImages(JSON.parseArray(JSON.toJSONString(master.get(TPMActivityProofFields.PROOF_IMAGES)), ActivityProofImageDTO.class));
            datum.setImagesTotalCount(CollectionUtils.isEmpty(datum.getImages()) ? 0 : datum.getImages().size());
            datum.setDetails(Lists.newArrayList());
            if (detailsMap.containsKey(master.getId())) {
                List<IObjectData> details = detailsMap.get(master.getId());
                for (IObjectData detail : details) {
                    ActivityProofDetailDTO detailDatum = new ActivityProofDetailDTO();
                    String activityItemId = (String) detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID);
                    detailDatum.setName(itemNameMap.getOrDefault(activityItemId, "--"));
                    detailDatum.setAmount((String) detail.get(TPMActivityProofDetailFields.AMOUNT));
                    datum.getDetails().add(detailDatum);
                }
            }
            data.getActivityProofList().add(datum);
        }

        data.setActivityProofListSize(data.getActivityProofList().size());
        String updateActionResult = checkinService.updateProofAction(actionContext.getUser(), visitId, actionId, data);
        result.getObjectData().put("__update_action_result", updateActionResult);
    }

    private List<IObjectData> queryProof(ActionContext context, String storeId, String visitId, String actionId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(6);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        if (!Strings.isNullOrEmpty(storeId)) {
            Filter storeIdFilter = new Filter();
            storeIdFilter.setFieldName(TPMActivityProofFields.STORE_ID);
            storeIdFilter.setOperator(Operator.EQ);
            storeIdFilter.setFieldValues(Lists.newArrayList(storeId));
            query.getFilters().add(storeIdFilter);
        }


        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));
        query.getFilters().add(visitIdFilter);

        Filter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(TPMActivityProofFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));
        query.getFilters().add(actionIdFilter);

        return QueryDataUtil.find(serviceFacade, context.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query, Lists.newArrayList(CommonFields.ID, TPMActivityProofFields.ACTIVITY_ID, TPMActivityProofFields.REMARK, TPMActivityProofFields.PROOF_IMAGES));
    }

    private List<IObjectData> queryActivity(ActionContext context, List<String> ids) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(ids.size());
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(idFilter));
        return QueryDataUtil.find(serviceFacade, context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME));
    }

    private List<IObjectData> queryActivityProofDetails(ActionContext context, List<String> masterIds) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        masterFilter.setOperator(Operator.IN);
        masterFilter.setFieldValues(masterIds);

        stq.setFilters(Lists.newArrayList(masterFilter));
        return QueryDataUtil.find(
                serviceFacade,
                context.getTenantId(),
                ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        TPMActivityProofDetailFields.ACTIVITY_PROOF_ID,
                        TPMActivityProofDetailFields.ACTIVITY_ITEM_ID,
                        TPMActivityProofDetailFields.AMOUNT
                ));
    }

    private Map<String, String> queryActivityItem() {
        return activityItemCache.get(actionContext.getTenantId());
    }

    @Override
    protected void finallyDo() {
        super.finallyDo();
        this.stopWatch.logSlow(500L);
    }

    private void visitInformationIdempotentValidate() {
        String visitId = (String) arg.getObjectData().get(TPMActivityProofFields.VISIT_ID);
        String actionId = (String) arg.getObjectData().get(TPMActivityProofFields.ACTION_ID);
        String activityId = (String) arg.getObjectData().get(TPMActivityProofFields.ACTIVITY_ID);

        if (!Strings.isNullOrEmpty(visitId) && !Strings.isNullOrEmpty(actionId)) {
            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setNeedReturnCountNum(true);
            query.setFindExplicitTotalNum(true);
            query.setNeedReturnQuote(false);
            query.setLimit(1);
            query.setOffset(0);
            query.setSearchSource("db");

            Filter activityFilter = new Filter();
            activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
            activityFilter.setOperator(Operator.EQ);
            activityFilter.setFieldValues(Lists.newArrayList(activityId));

            Filter visitIdFilter = new Filter();
            visitIdFilter.setFieldName(TPMActivityProofFields.VISIT_ID);
            visitIdFilter.setOperator(Operator.EQ);
            visitIdFilter.setFieldValues(Lists.newArrayList(visitId));

            Filter actionIdFilter = new Filter();
            actionIdFilter.setFieldName(TPMActivityProofFields.ACTION_ID);
            actionIdFilter.setOperator(Operator.EQ);
            actionIdFilter.setFieldValues(Lists.newArrayList(actionId));

            query.setFilters(Lists.newArrayList(activityFilter, visitIdFilter, actionIdFilter));

            int count = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getTotalNumber();
            if (count > 0) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_OBJ_ADD_ACTION_1));
            }
        }
    }

    private IObjectData findAgreement() {
        if (Objects.nonNull(this.activityType.agreementNode())) {
            String agreementId = (String) arg.getObjectData().get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID);
            if (Strings.isNullOrEmpty(agreementId)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WITCH_IS_PROTOCOL_SHOULD_CHECK_UP_AGREEMENT_OPTION));
            }
            IObjectData innerAgreement = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), agreementId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            if (innerAgreement == null) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WITCH_IS_PROTOCOL_SHOULD_CHECK_UP_AGREEMENT_OPTION));
            }
            if ("void".equals(innerAgreement.get(TPMActivityAgreementFields.RIO_PROTOCOL_STATUS))) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_OBJ_ADD_ACTION_2));
            }
            return innerAgreement;
        }
        return null;
    }

    private IObjectData findAndSetDealerId() {
        String dealerId = null;
        if (TPMGrayUtils.useAgreementDealerIdOnProofAddAction(actionContext.getTenantId()) && this.agreement != null) {
            dealerId = this.agreement.get(TPMActivityAgreementFields.DEALER_ID, String.class);
        } else if (store != null) {
            dealerId = storeBusiness.findDealerId(actionContext.getTenantId(), this.store);

            String dealerIdInParameters = (String) arg.getObjectData().get(TPMActivityProofFields.DEALER_ID);

            if ((!Strings.isNullOrEmpty(dealerId) || !Strings.isNullOrEmpty(dealerIdInParameters)) && !Objects.equals(dealerIdInParameters, dealerId)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_OBJ_ADD_ACTION_3));
            }
        }

        if (!Strings.isNullOrEmpty(dealerId)) {
            IObjectData innerDealer = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
            if (innerDealer == null) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_OBJ_ADD_ACTION_4));
            }
            arg.getObjectData().put(TPMActivityProofFields.DEALER_ID, innerDealer.getId());
            return innerDealer;
        }

        return null;
    }

    private void dateAndAgreementValidate() {
        if (this.agreement != null) {
            String lifeStatus = this.agreement.get(CommonFields.LIFE_STATUS, String.class);
            if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
                throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_STATUS_IS_ABNORMAL));
            }

            long begin = this.agreement.get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
            long end = this.agreement.get(TPMActivityAgreementFields.END_DATE, Long.class);

            long now = System.currentTimeMillis();
            if (now < begin || now > end) {
                throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_HAS_NOT_TAKEN_EFFECT));
            }

            if (!this.store.getId().equals(this.agreement.get(TPMActivityAgreementFields.STORE_ID, String.class))) {
                throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_IS_NOT_FIT_THIS_STORE));
            }

            if (arg.getDetails().containsKey(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ)) {
                for (ObjectDataDocument proofDetail : arg.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ)) {
                    if (Strings.isNullOrEmpty((String) proofDetail.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID))) {
                        throw new ValidateException(I18N.text(I18NKeys.THIS_IS_A_PROTOCOL_ACTIVITY_SO_PROOF_ITEM_SHOULD_RELATED_WITH_AGREEMENT_ITEM));
                    }
                }
            }
        } else {
            if (actionContext.getAttribute("fromPOC") != null && "true".equals(actionContext.getAttribute("fromPOC"))) {
                return;
            }
            long begin = this.activity.get(TPMActivityFields.BEGIN_DATE, Long.class);
            long end = this.activity.get(TPMActivityFields.END_DATE, Long.class);

            long now = System.currentTimeMillis();
            if (now < begin || now > end) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_IS_NOT_ACTIVE_OR_HAS_EXPIRED));
            }
        }
    }

    private void validateProofFrequency() {
        if (actionContext.getAttribute("fromPOC") != null && "true".equals(actionContext.getAttribute("fromPOC"))) {
            return;
        }
        proofService.validateProofTimeAndFrequent(actionContext.getTenantId(), this.store == null ? null : this.store.getId(), this.activity, this.agreement, 1);
    }

    private void validateCustomAllowStoreCount() {
        if (forbidCustomer) {
            log.info("validateCustomAllowStoreCount forbidCustomer");
            return;
        }
        if (!TPMGrayUtils.customAllowStoreCountValidate(actionContext.getTenantId())) {
            return;
        }

        String allowStoreCountStr = this.activity.get("allow_store_count__c", String.class);
        if (Strings.isNullOrEmpty(allowStoreCountStr)) {
            return;
        }

        BigDecimal allowStoreCount = new BigDecimal(allowStoreCountStr);

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(this.activity.getId()));

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityProofFields.STORE_ID);
        storeFilter.setOperator(Operator.NEQ);
        storeFilter.setFieldValues(Lists.newArrayList(this.store.getId()));

        query.setFilters(Lists.newArrayList(activityFilter, storeFilter));

        AggFunctionArg functionArg = AggFunctionArg.builder().aggFunction("count").aggField("id").build();

        List<IObjectData> aggregateResult = serviceFacade.aggregateFindBySearchQueryWithGroupFields(User.systemUser(actionContext.getTenantId()), query, ApiNames.TPM_ACTIVITY_PROOF_OBJ, Lists.newArrayList(TPMActivityProofFields.STORE_ID), Lists.newArrayList(functionArg));

        BigDecimal totalStoreCount = BigDecimal.valueOf(aggregateResult.size());

        if (totalStoreCount.compareTo(allowStoreCount) > -1) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_MAX_STORE_NUMBER_VALIDATE),
                    this.activity.getName(),
                    allowStoreCount));
        }
    }

    private void validateCustomCost() {
        if (!TPMGrayUtils.customCostValidate(actionContext.getTenantId())) {
            return;
        }

        BigDecimal activityTotal = this.activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class, new BigDecimal("0"));
        BigDecimal currentAmount = arg.getObjectData().toObjectData().get(TPMActivityProofFields.ACTUAL_TOTAL, BigDecimal.class, new BigDecimal("0"));

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(activityIdFilter, deletedFilter));

        List<String> aggList = Lists.newArrayList(TPMActivityProofFields.ACTIVITY_ID);

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(User.systemUser(actionContext.getTenantId()), query, ApiNames.TPM_ACTIVITY_PROOF_OBJ, aggList, "sum", TPMActivityProofFields.ACTUAL_TOTAL);

        BigDecimal total;
        if (!CollectionUtils.isEmpty(data)) {
            String key = String.format("sum_%s", TPMActivityProofFields.ACTUAL_TOTAL);
            total = data.get(0).get(key, BigDecimal.class);
        } else {
            total = new BigDecimal("0");
        }

        log.info("validate custom cost : total - {}, current amount - {}, activity total : {}", total, currentAmount, activityTotal);

        total = total.add(currentAmount);
        if (total.compareTo(activityTotal) > 0) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_OBJ_ADD_ACTION_5));
        }
    }

    private void activityValidate() {
        if (TPMGrayUtils.isYinLu(actionContext.getTenantId())) {
            //创建时间大于 5.28号的才可以被举证
            Long activityCreateTime = activity.get(CommonFields.CREATE_TIME, Long.class);
            Long effectiveTime = Long.parseLong(YINLU_FILTER_TIME);
            if (activityCreateTime <= effectiveTime) {
                throw new ValidateException("proof not allow");
            }
        }
        String lifeStatus = this.activity.get(CommonFields.LIFE_STATUS, String.class);
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_OBJ_ADD_ACTION_6));
        }

        String closedStatus = (String) this.activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_HAS_CLOSED_CAN_NOT_CREATE_PROOF));
        }

        String activityStatus = (String) this.activity.get(TPMActivityFields.ACTIVITY_STATUS);
        if (TPMActivityFields.ACTIVITY_STATUS__CLOSED.equals(activityStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_OBJ_ADD_ACTION_7));
        }

        activityService.ifAllowCreateDataDueToOnceWriteOff(actionContext.getTenantId(), this.activity);
        String dealerId = this.activity.get(TPMActivityFields.DEALER_ID, String.class);
        String storeId = forbidCustomer ? null : store.getId();

        log.info("store id : {}, dealer id : {}", storeId, dealerId);

        if (!Strings.isNullOrEmpty(dealerId) && !dealerId.equals(this.store.getId()) && !TPMGrayUtils.isYuanQi(actionContext.getTenantId())) {
            if (this.dealer == null || !dealerId.equals(this.dealer.getId())) {
                throw new ValidateException(I18N.text(I18NKeys.STORE_DEALER_CAN_NOT_MATCH_ACTIVITY_DEALER));
            }
        }

        List<String> departmentRange = this.activity.get(TPMActivityFields.DEPARTMENT_RANGE) == null ? new ArrayList<>() : CommonUtils.cast(this.activity.get(TPMActivityFields.DEPARTMENT_RANGE), String.class);
        if (this.activity.get(TPMActivityFields.MULTI_DEPARTMENT_RANGE) != null) {
            departmentRange.addAll(CommonUtils.cast(this.activity.get(TPMActivityFields.MULTI_DEPARTMENT_RANGE), String.class));
        }

        //小程序 无需也没有办法校验部门
        if (!"-10000".equals(actionContext.getUser().getUserId()) && !TPMGrayUtils.denyDepartmentFilterOnActivity(actionContext.getTenantId()) && !actionContext.getUser().isOutUser() && !organizationService.employeeInRange(actionContext.getUser().getTenantIdInt(), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), departmentRange.stream().map(Integer::valueOf).collect(Collectors.toList()))) {
            throw new ValidateException(I18N.text(I18NKeys.EMPLOYEE_IS_NOT_WITHIN_THE_SCOPE_OF_ACTIVITY));
        }

        //validateStore
        Map<String, Boolean> validateResultMap = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(actionContext.getTenantId(), storeId, this.dealer == null ? null : this.dealer.getId(), Lists.newArrayList(activity), false, true);

        if (Boolean.FALSE.equals(validateResultMap.getOrDefault(activity.getId(), false))) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_OBJ_ADD_ACTION_8));
        }
    }

    private void setAuditStatus() {
        this.auditMode = loadAuditMode();
        if ("1".equals(this.auditMode)) {
            arg.getObjectData().put(TPMActivityProofFields.AUDIT_STATUS, TPMActivityProofFields.AUDIT_STATUS__PASS);
            arg.getObjectData().put(TPMActivityProofFields.RANDOM_AUDIT_STATUS, "unchecked");
        } else {
            arg.getObjectData().put(TPMActivityProofFields.AUDIT_STATUS, TPMActivityProofFields.AUDIT_STATUS__SCHEDULE);
        }
    }

    private void setProofItemDefaultValue(Arg arg) {
        if (enableAi) {
            log.info("setProofItemDefaultValue open ai");
            return;
        }
        List<ObjectDataDocument> activityProofDetails = arg.getDetails().getOrDefault(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, new ArrayList<>());
        List<ObjectDataDocument> endProofDetails = activityProofDetails.stream().filter(proof -> Strings.isNullOrEmpty((String) proof.get(TPMActivityProofDetailFields.PROOF_ITEM)) && !Strings.isNullOrEmpty((String) proof.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID))).collect(Collectors.toList());
        List<String> activityItemIds = endProofDetails.stream().map(proof -> (String) proof.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(activityItemIds)) {
            List<IObjectData> activityItems = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), activityItemIds, ApiNames.TPM_ACTIVITY_ITEM_OBJ);
            Map<String, String> activityItemIdNameMap = activityItems.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));

            activityProofDetails.forEach(detail -> {
                String proofItem = (String) detail.get(TPMActivityProofDetailFields.PROOF_ITEM);
                String activityItemId = (String) detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID);
                if (Strings.isNullOrEmpty(proofItem) && !Strings.isNullOrEmpty(activityItemId)) {
                    detail.put(TPMActivityProofDetailFields.PROOF_ITEM, activityItemIdNameMap.getOrDefault(activityItemId, ""));
                }

                String systemJudgmentResult = tpmDisplayReportService.getProofDetailSystemJudgmentResult(detail);
                if (!Strings.isNullOrEmpty(systemJudgmentResult)) {
                    detail.put(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, systemJudgmentResult);
                }
            });

            arg.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, activityProofDetails);
        }
    }

    private String loadAuditMode() {
        if (!Objects.isNull(this.activityType.auditModeConfig())) {
            String innerAuditMode = this.activityType.auditModeConfig().getAuditMode();
            if (AuditModeType.RANDOM.value().equals(innerAuditMode)) {
                return "1";
            }
        }
        return "10086";
    }

    @Override
    protected Result doAct(Arg arg) {
        stopWatch.lap("start doAct");
        try {
            //执行事务
            if (TPMGrayUtils.skipProofAddTransaction(actionContext.getTenantId())) {
                return doActTransaction(arg);
            } else {
                return packTransactionProxy.packAct(this, arg);
            }
        } catch (Exception e) {
            budgetService.rmSaveIdempotent(actionContext);
            throw e;
        } finally {
            stopWatch.lap("finish doAct");
        }
    }

    @Override
    public Result doActTransaction(Arg arg) {
        Result result = super.doAct(arg);
        // 自动检核
        autoAuditAction(result);
        return result;
    }

    private void autoAuditAction(Result result) {
        long now = System.currentTimeMillis();
        if ("1".equals(this.auditMode)) {
            IObjectData masterData = new ObjectData();
            masterData.setDescribeApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
            masterData.setTenantId(actionContext.getTenantId());
            masterData.setOwner(Lists.newArrayList("-10000"));
            masterData.set(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID, result.getObjectData().getId());
            masterData.set(TPMActivityProofAuditFields.PROOF_IMAGES, result.getObjectData().get(TPMActivityProofFields.PROOF_IMAGES));
            masterData.set(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID, result.getObjectData().get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID));
            masterData.set(TPMActivityProofAuditFields.DEALER_ACTIVITY, result.getObjectData().get(TPMActivityProofFields.DEALER_ACTIVITY));
            masterData.set(TPMActivityProofAuditFields.STORE_ID, result.getObjectData().get(TPMActivityProofFields.STORE_ID));
            masterData.set(TPMActivityProofAuditFields.DEALER_ID, result.getObjectData().get(TPMActivityProofFields.DEALER_ID));
            masterData.set(TPMActivityProofAuditFields.ACTIVITY_ID, result.getObjectData().get(TPMActivityProofFields.ACTIVITY_ID));
            masterData.set(TPMActivityProofAuditFields.AUDIT_TIME, now);
            masterData.set(TPMActivityProofAuditFields.AUDIT_TOTAL, result.getObjectData().get(TPMActivityProofFields.ACTUAL_TOTAL) == null ? result.getObjectData().get(TPMActivityProofFields.TOTAL) : result.getObjectData().get(TPMActivityProofFields.ACTUAL_TOTAL));
            masterData.set(TPMActivityProofAuditFields.OPINION, "系统检核：合格");//ignorei18n
            masterData.set(TPMActivityProofAuditFields.AUDITOR, Lists.newArrayList("-10000"));
            masterData.set(TPMActivityProofAuditFields.AUDIT_STATUS, TPMActivityProofFields.AUDIT_STATUS__PASS);
            masterData.set(TPMActivityProofAuditFields.RANDOM_AUDIT_STATUS, "unchecked");
            masterData.setRecordType(this.activityType.auditNode().getObjectRecordType());

            Map<String, List<IObjectData>> detailsData = new HashMap<>();
            List<IObjectData> auditDetails = Lists.newArrayList();
            if (result.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ) != null) {
                for (ObjectDataDocument proofDetail : result.getDetails().get(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ)) {
                    IObjectData auditDetail = new ObjectData();
                    auditDetail.set(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_DETAIL_ID, proofDetail.getId());
                    auditDetail.set(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_ID, proofDetail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID));
                    auditDetail.set(TPMActivityProofAuditDetailFields.ACTIVITY_DETAIL_ID, proofDetail.get(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID));
                    auditDetail.set(TPMActivityProofAuditDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID, proofDetail.get(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID));
                    auditDetail.set(TPMActivityProofAuditDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID, proofDetail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_COST_STANDARD_ID));
                    auditDetail.set(TPMActivityProofAuditDetailFields.AUDIT_SUBTOTAL, proofDetail.get(TPMActivityProofDetailFields.SUBTOTAL));
                    auditDetail.set(TPMActivityProofAuditDetailFields.AUDIT_AMOUNT, proofDetail.get(TPMActivityProofDetailFields.AMOUNT));
                    auditDetail.setTenantId(actionContext.getTenantId());
                    auditDetail.setOwner(Lists.newArrayList("-10000"));
                    auditDetail.setDescribeApiName(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ);
                    auditDetail.setRecordType("default__c");
                    auditDetail.set(TPMActivityProofAuditDetailFields.AUDIT_ITEM, proofDetail.get("name"));
                    masterData.set(TPMActivityProofAuditFields.TOTAL, result.getObjectData().get(TPMActivityProofFields.ACTUAL_TOTAL) == null ? result.getObjectData().get(TPMActivityProofFields.TOTAL) : result.getObjectData().get(TPMActivityProofFields.ACTUAL_TOTAL));
                    auditDetails.add(auditDetail);
                }
            }
            detailsData.put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, auditDetails);

            Map<String, IObjectDescribe> describeMap = new HashMap<>();
            describeMap.put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, serviceFacade.findObject(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ));

            SaveMasterAndDetailData.Arg saveArg = SaveMasterAndDetailData.Arg.builder().masterObjectData(masterData).detailObjectData(detailsData).objectDescribes(describeMap).build();

            try {
                serviceFacade.saveMasterAndDetailData(User.systemUser(actionContext.getTenantId()), saveArg);
                BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.valueOf(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF_AUDIT, BuryOperation.AUTO_CREATE, false);
            } catch (Exception ex) {
                log.error("auto create proof audit data error : ", ex);
                throw new ValidateException(ex.getMessage());
            }
        }
    }
}
