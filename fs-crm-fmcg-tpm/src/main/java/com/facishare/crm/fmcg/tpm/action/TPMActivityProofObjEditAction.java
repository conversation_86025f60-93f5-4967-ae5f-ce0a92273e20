package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDetailDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofImageDTO;
import com.facishare.crm.fmcg.tpm.api.visit.VisitActionDataDTO;
import com.facishare.crm.fmcg.tpm.business.TPMDisplayReportService;
import com.facishare.crm.fmcg.tpm.cache.ActivityItemCache;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofDetailDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityProofImageDTO;
import com.facishare.crm.fmcg.tpm.api.visit.VisitActionDataDTO;
import com.facishare.crm.fmcg.tpm.cache.ActivityItemCache;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.CheckinService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/12 2:47 PM
 */
@Slf4j
@SuppressWarnings("Duplicates,unused")
public class TPMActivityProofObjEditAction extends StandardEditAction {

    private static final CheckinService checkinService = SpringUtil.getContext().getBean(CheckinService.class);
    private static final ActivityItemCache activityItemCache = SpringUtil.getContext().getBean(ActivityItemCache.class);
    private final TPMDisplayReportService tpmDisplayReportService = SpringUtil.getContext().getBean(TPMDisplayReportService.class);

    private final IActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(IActivityTypeManager.class);

    private static final List<String> ALLOW_EDIT_FIELD_TYPE = Lists.newArrayList("image");

    private ActivityTypeExt activityType = null;

    private Boolean enableAi = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        stopWatch.lap("super.before");

        IObjectData oldData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        if (!TPMActivityProofFields.AUDIT_STATUS__SCHEDULE.equals(oldData.get(TPMActivityProofFields.AUDIT_STATUS))) {
            throw new ValidateException(I18N.text(I18NKeys.AUDITED_PROOF_CAN_NOT_EDIT));
        }
        String activityId = (String) arg.getObjectData().get(TPMActivityProofFields.ACTIVITY_ID);
        this.activityType = activityTypeManager.findByActivityId(actionContext.getTenantId(), activityId);
        enableAi = activityType.proofConfig().getAiConfig() != null && Boolean.TRUE.equals(activityType.proofConfig().getAiConfig().getEnableAiDisplayRecognition());

        if (this.isIncrementUpdate()) {
            //提前计算差额 保证可编辑字段校验
            super.diffObjectDataWithDbData();
            stopWatch.lap("diffObjectDataWithDbData");
        }

        this.validateAllowEditField();
        stopWatch.lap("validateAllowEditField");

        this.validateProofCheckinAction();
        stopWatch.lap("validateProofCheckinAction");

        this.setProofItemDefaultValue(arg);
        stopWatch.lap("setProofItemDefaultValue");

        this.setProofDisplayImgDefaultValue();
        stopWatch.lap("setProofDisplayImgDefaultValue");
    }

    private void setProofDisplayImgDefaultValue() {
        tpmDisplayReportService.setProofDisplayImgDefaultValue(actionContext.getTenantId(), arg, enableAi);
    }

    private void setProofItemDefaultValue(Arg arg) {
        if (enableAi) {
            log.info("setProofItemDefaultValue open ai");
            return;
        }
        List<ObjectDataDocument> activityProofDetails = arg.getDetails().getOrDefault(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, new ArrayList<>());
        activityProofDetails.forEach(detail -> {
            String systemJudgmentResult = tpmDisplayReportService.getProofDetailSystemJudgmentResult(detail);
            if (!Strings.isNullOrEmpty(systemJudgmentResult)) {
                detail.put(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, systemJudgmentResult);
            }
        });
        arg.getDetails().put(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, activityProofDetails);
    }

    private void validateProofCheckinAction() {
        // 是否启用着 ai 配置。
        if (enableAi) {
            // 如果有关联外勤，判断外勤是否执行完成了，完成不允许编辑。
            String visitId = objectData.get(TPMActivityProofFields.VISIT_ID, String.class);
            if (!Strings.isNullOrEmpty(visitId)) {
                IObjectData checkinsObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), visitId, ApiNames.CHECKINS_OBJ);
                if (checkinsObj != null) {
                    Boolean checkinStatus = checkinsObj.get(CheckinsObjFields.CHECKIN_STATUS, Boolean.class);
                    if (Boolean.TRUE.equals(checkinStatus)) {
                        throw new ValidateException(I18N.text(I18NKeys.CAN_NOT_EDIT_NON_IMAGE_FIELD));
                    }
                }
            }
        }
    }

    void validateAllowEditField() {
        if (!this.detailChangeMap.isEmpty()) {
            Boolean allowEditField = TPMGrayUtils.isAllowProofDetailEditCustomField(actionContext.getTenantId());
            this.detailChangeMap.forEach((k, v) -> {
                if (allowEditField && ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ.equals(k)) {
                    validateDetailEditField(ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, null);
                } else if (ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ.equals(k)) {
                    validateDetailEditField(ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ, TPMActivityProofDisplayImgFields.IMAGE);
                } else if (!k.endsWith("__c")) {
                    throw new ValidateException(I18N.text(I18NKeys.CAN_NOT_EDIT_PROOF_DETAIL));
                }
            });
        }

        IObjectDescribe describe = serviceFacade.findObject(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        Set<String> allowField = Sets.newHashSet();

        describe.getFieldDescribeMap().forEach((k, field) -> {
            String apiName = (String) field.get("api_name");
            String type = (String) field.get("type");
            String defineType = (String) field.get("define_type");

            if ("custom".equals(defineType)) {
                allowField.add(apiName);
            }
            if (ALLOW_EDIT_FIELD_TYPE.contains(type)) {
                allowField.add(apiName);
            }
            if (TPMGrayUtils.isAllowProofEditField(actionContext.getTenantId())){
                // 允许编辑 备注、举证申报费用  字段
                allowField.addAll(Lists.newArrayList(TPMActivityProofFields.REMARK, TPMActivityProofFields.ACTUAL_TOTAL));
            }
        });

        this.updatedFieldMap.keySet().forEach(v -> {
            if (!allowField.contains(v)) {
                throw new ValidateException(I18N.text(I18NKeys.CAN_NOT_EDIT_NON_IMAGE_FIELD));
            }
        });
    }

    private void validateDetailEditField(String apiName, String field) {
        Map<String, Object> displayImgChangeMap = detailChangeMap.get(apiName);
        @SuppressWarnings("unchecked")
        Map<String, Object> editChangeMap = (Map<String, Object>) displayImgChangeMap.get("Edit");
        if (!editChangeMap.isEmpty()) {
            editChangeMap.values().forEach(o -> {
                @SuppressWarnings("unchecked")
                Map<String, Object> detailFilesMap = (Map<String, Object>) (o);
                if (!detailFilesMap.isEmpty()) {
                    for (String fieldUpdate : detailFilesMap.keySet()) {
                        if (fieldUpdate.equals(field) || fieldUpdate.endsWith("__c")) {
                            continue;
                        }
                        throw new ValidateException(I18N.text(I18NKeys.CAN_NOT_EDIT_NON_IMAGE_FIELD));
                    }
                }
            });
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (!TPMGrayUtils.isSkipSaveCheckinProofData(actionContext.getTenantId())) {
            if (TPMGrayUtils.isAsyncSaveCheckinProofData(actionContext.getTenantId())) {
                ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> this.saveCheckinsAction(result))).run();
            } else {
                this.saveCheckinsAction(result);
            }
        }

        if (enableAi) {

            // 编辑，重新执行 AI识别
            tpmDisplayReportService.asyncProcessProofDisplayImgAi(actionContext.getTenantId(), actionContext.getUser().getUserId(), activityType, arg.getObjectData().getId());
        }


        // 埋点
        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_PROOF, BuryOperation.UPDATE, false);
        return super.after(arg, result);
    }

    private void saveCheckinsAction(Result result) {
        String visitId = (String) result.getObjectData().get(TPMActivityProofFields.VISIT_ID);
        String actionId = (String) result.getObjectData().get(TPMActivityProofFields.ACTION_ID);

        if (!Strings.isNullOrEmpty(visitId) && !Strings.isNullOrEmpty(actionId)) {

            String storeId = (String) result.getObjectData().get(TPMActivityProofFields.STORE_ID);
            VisitActionDataDTO data = new VisitActionDataDTO();

            List<IObjectData> masterList = queryProof(actionContext, storeId, visitId, actionId);
            List<String> masterIds = masterList.stream().map(DBRecord::getId).collect(Collectors.toList());
            List<String> activityIds = masterList.stream().map(master -> (String) master.get(TPMActivityProofFields.ACTIVITY_ID)).distinct().collect(Collectors.toList());
            Map<String, IObjectData> activityMap = queryActivity(actionContext, activityIds).stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (oldOne, newOne) -> oldOne));
            Map<String, List<IObjectData>> detailsMap = queryActivityProofDetails(actionContext, masterIds).stream().collect(Collectors.groupingBy(detail -> (String) detail.get(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID)));
            Map<String, String> itemNameMap = queryActivityItem();
            data.setActivityProofList(Lists.newArrayList());

            for (IObjectData master : masterList) {
                ActivityProofDTO datum = new ActivityProofDTO();
                datum.setProofId(master.getId());
                datum.setRemark((String) master.get(TPMActivityProofFields.REMARK));
                String activityId = (String) master.get(TPMActivityProofFields.ACTIVITY_ID);
                if (activityMap.containsKey(activityId)) {
                    datum.setActivityName(activityMap.get(activityId).getName());
                }
                datum.setImages(JSON.parseArray(JSON.toJSONString(master.get(TPMActivityProofFields.PROOF_IMAGES)), ActivityProofImageDTO.class));
                datum.setImagesTotalCount(CollectionUtils.isEmpty(datum.getImages()) ? 0 : datum.getImages().size());
                datum.setDetails(Lists.newArrayList());
                if (detailsMap.containsKey(master.getId())) {
                    List<IObjectData> details = detailsMap.get(master.getId());
                    for (IObjectData detail : details) {
                        ActivityProofDetailDTO detailDatum = new ActivityProofDetailDTO();
                        String activityItemId = (String) detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID);
                        detailDatum.setName(itemNameMap.getOrDefault(activityItemId, "--"));
                        detailDatum.setAmount((String) detail.get(TPMActivityProofDetailFields.AMOUNT));
                        datum.getDetails().add(detailDatum);
                    }
                }
                data.getActivityProofList().add(datum);
            }

            data.setActivityProofListSize(data.getActivityProofList().size());
            String updateActionResult = checkinService.updateProofAction(actionContext.getUser(), visitId, actionId, data);
            result.getObjectData().put("__update_action_result", updateActionResult);
        }
    }

    private Map<String, String> queryActivityItem() {
        return activityItemCache.get(actionContext.getTenantId());
    }

    private List<IObjectData> queryActivityProofDetails(ActionContext context, List<String> masterIds) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        masterFilter.setOperator(Operator.IN);
        masterFilter.setFieldValues(masterIds);

        stq.setFilters(Lists.newArrayList(masterFilter));
        return QueryDataUtil.find(
                serviceFacade,
                context.getTenantId(),
                ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        TPMActivityProofDetailFields.ACTIVITY_PROOF_ID,
                        TPMActivityProofDetailFields.ACTIVITY_ITEM_ID,
                        TPMActivityProofDetailFields.AMOUNT
                ));
    }

    private List<IObjectData> queryActivity(ActionContext context, List<String> ids) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(ids.size());
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(idFilter));
        return QueryDataUtil.find(serviceFacade, context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME));
    }

    private List<IObjectData> queryProof(ActionContext context, String storeId, String visitId, String actionId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(6);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);

        if (!Strings.isNullOrEmpty(storeId)) {
            Filter storeIdFilter = new Filter();
            storeIdFilter.setFieldName(TPMActivityProofFields.STORE_ID);
            storeIdFilter.setOperator(Operator.EQ);
            storeIdFilter.setFieldValues(Lists.newArrayList(storeId));
            query.getFilters().add(storeIdFilter);
        }


        Filter visitIdFilter = new Filter();
        visitIdFilter.setFieldName(TPMActivityProofFields.VISIT_ID);
        visitIdFilter.setOperator(Operator.EQ);
        visitIdFilter.setFieldValues(Lists.newArrayList(visitId));
        query.getFilters().add(visitIdFilter);

        Filter actionIdFilter = new Filter();
        actionIdFilter.setFieldName(TPMActivityProofFields.ACTION_ID);
        actionIdFilter.setOperator(Operator.EQ);
        actionIdFilter.setFieldValues(Lists.newArrayList(actionId));
        query.getFilters().add(actionIdFilter);

        return QueryDataUtil.find(serviceFacade, context.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query, Lists.newArrayList(CommonFields.ID, TPMActivityProofFields.ACTIVITY_ID, TPMActivityProofFields.REMARK, TPMActivityProofFields.PROOF_IMAGES));
    }
}