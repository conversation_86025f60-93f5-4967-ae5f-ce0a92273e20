package com.facishare.crm.fmcg.tpm.web.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.impl.EIEAConverterImpl;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.DBRouterService;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetStatisticTableService;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityNodeTemplateDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.TransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.IWithdrawRecordService;
import com.facishare.crm.fmcg.tpm.service.abstraction.PluginInstanceService;
import com.facishare.crm.fmcg.tpm.service.abstraction.TPMEnterpriseService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.FormatUtil;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IConfigService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.ITPMFieldService;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.IDevToolService;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.IModuleInitializationService;
import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.dto.FieldResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fmcg.framework.http.FmcgServiceProxy;
import com.fmcg.framework.http.contract.fmcgservice.GetLicense;
import com.fmcg.framework.http.contract.fmcgservice.license.Add;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGetField;
import com.fxiaoke.common.SqlEscaper;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 后台工具服务
 * <p>
 * create by @yangqf
 * create time 2023/2/7 10:28
 */
//IgnoreI18nFile
@Slf4j
@SuppressWarnings("Duplicates")
@Component
public class DevToolService implements IDevToolService {

    public static final String TPM_CODE = "FMCG.TPM.2";

    public static final String DETAIL_LAYOUT_TYPE = "detail";

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private IBudgetStatisticTableService budgetStatisticTableService;

    @Resource
    private TPM2Service tpm2Service;

    @Resource
    private TPMEnterpriseService tpmEnterpriseService;

    @Resource
    private IConfigService tpmConfigService;

    @Resource
    private ActivityTypeDAO activityTypeDAO;

    @Resource
    private ActivityNodeTemplateDAO nodeTemplateDAO;

    @Resource
    private IActivityTypeManager activityTypeManager;

    @Resource
    private FmcgServiceProxy fmcgServiceProxy;

    @Resource
    private SpecialTableMapper specialTableMapper;

    @Resource
    private PluginInstanceService pluginService;

    @Resource
    private EIEAConverterImpl eieaConverter;

    @Resource
    private IModuleInitializationService moduleInitializationService;

    @Resource
    private IWithdrawRecordService withdrawRecordService;

    @Resource
    private TransactionProxy transactionProxy;

    @Resource
    private ITPMFieldService tpmFieldService;

    @Resource
    private DBRouterService dbRouterService;

    @Resource
    private EnterpriseEditionService enterpriseEditionService;


    protected static final List<String> DELETE_SQLS = Lists.newArrayList("delete from mt_field where  (tenant_id, describe_id ) in (select tenant_id, describe_id from mt_describe where tenant_id = '%s' and describe_api_name = '%s')", "delete from mt_action where  (tenant_id, describe_id ) in (select tenant_id, describe_id from mt_describe where tenant_id = '%s' and describe_api_name = '%s')", "delete from mt_cluster where tenant_id = '%s' and describe_api_name = '%s'", "delete from mt_udef_action where tenant_id = '%s' and describe_api_name = '%s'", "delete from mt_udef_button where tenant_id = '%s' and describe_api_name = '%s'", "delete from mt_ui_component where tenant_id = '%s' and ref_object_api_name = '%s'", "delete from mt_layout_rule where tenant_id = '%s' and object_describe_api_name = '%s'", "delete from mt_rules where tenant_id = '%s' and describe_api_name = '%s'", "delete from mt_custom_search_template where tenant_id = '%s' and describe_api_name = '%s'", "delete from mt_ui_event where tenant_id = '%s' and describe_api_name = '%s'", "delete from mt_unique_rule where  tenant_id = '%s' and describe_api_name = '%s'", "delete from mt_validate_rule where  (tenant_id, describe_id ) in (select tenant_id, describe_id from mt_describe where tenant_id = '%s' and describe_api_name = '%s')", "delete from mt_describe  where tenant_id = '%s' and describe_api_name = '%s'");

    protected static final List<String> DEFAULT_DELETE_API_NAMES = Lists.newArrayList("TPMActivityItemObj", "TPMActivityItemCostStandardObj", "TPMActivityUnifiedCaseObj", "TPMActivityDealerScopeObj", "TPMActivityCashingProductScopeObj", "TPMActivityObj", "TPMActivityDetailObj", "TPMActivityStoreObj", "TPMActivityAgreementObj", "TPMActivityAgreementDetailObj", "TPMDealerActivityCostObj", "TPMStoreWriteOffObj", "TPMActivityProofObj", "TPMActivityProofDetailObj", "TPMActivityProofAuditObj", "TPMActivityProofAuditDetailObj", "TPMActivityCashingProductObj", "TPMActivityAgreementCashingProductObj", "TPMStoreWriteOffCashingProductObj", "TPMDealerActivityCashingProductObj", "TPMActivityUnifiedCaseProductRangeObj", "TPMActivityProductRangeObj", "TPMActivityVenueObj", "TPMActivityMaterialObj");

    static OkHttpClient client = new OkHttpClient().newBuilder().callTimeout(50, TimeUnit.SECONDS).readTimeout(500, TimeUnit.SECONDS).connectTimeout(50, TimeUnit.SECONDS).build();
    static String GLOBAL_URL = "http://api.fxiaokeglobal.local/fs-crm-fmcg-service";
    static String CESHI_URL = "https://**************:35206/fs-crm-fmcg-service";
    static String LOCAL_URL = "http://localhost:8083";

    private static String DEV_URL = "";

    @Override
    public void updateBudgetStatisticData(String tenantIds) {
        List<String> all = Lists.newArrayList();
        if (tenantIds.contains(",")) {
            Lists.newArrayList(tenantIds.split(","));
        } else {
            all.add(tenantIds);
        }
        for (String tenantId : all) {
            // ❗️note : 刷库脚本不需要过度封装，但是尽量把独立的一整块逻辑封装到一个方法里，这样可读性更好些 🐶

            // step 1. 查询所有预算表
            List<IObjectData> accounts = queryAllAccount(tenantId);

            // ⚠️ note : CollectionUtils 尽量统一使用 apache.commons 下的 org.apache.commons.collections4.CollectionUtils
            if (CollectionUtils.isEmpty(accounts)) {
                continue;
            }

            // step 2. 清除无用的统计表
            clearUselessStatistics(tenantId, accounts);

            // step 3. 重算没有关联统计表的预算表
            recalculateStatisticsData(tenantId, accounts);
        }
    }

    @Override
    public void updateBudgetStatisticUsedOrFrozen(String tenantIds) {
        List<String> all = Lists.newArrayList();
        if (tenantIds.contains(",")) {
            Lists.newArrayList(tenantIds.split(","));
        } else {
            all.add(tenantIds);
        }

        for (String tenantId : all) {
            // 查询所有预算表
            List<IObjectData> accounts = queryAllNeedRefreshAccount(tenantId);

            if (CollectionUtils.isEmpty(accounts)) {
                continue;
            }

            //重算没有关联统计表的已用金额和冻结金额
            refreshStatisticsUsedOrFrozenData(tenantId, accounts);
        }
    }

    @Override
    public void updateBudgetStatisticDescribeUsedOrFrozen(String tenantIds) {

        List<String> all = getAllTenantIds(tenantIds);
        if (CollectionUtils.isEmpty(all)) {
            return;
        }

        log.info("all tenantId = {} ", JSON.toJSONString(all));
        //判断企业是正常
        Map<String, Boolean> tenantIdActiveMap = tpmEnterpriseService.batchIsActiveEnterprise(all.stream().map(Integer::parseInt).collect(Collectors.toList()));

        if (!tenantIdActiveMap.isEmpty()) {
            log.info("all tenantIdActiveMap = {} ", JSON.toJSONString(tenantIdActiveMap));
            all = all.stream().filter(tenantIdActiveMap::get).distinct().collect(Collectors.toList());
        }

        //更新已用金额和冻结金额字段
        doUpdateBudgetStatisticDescribeUsedOrFrozen(all);
    }

    @Override
    public void preConfig() {
        Map<String, JSONObject> dealerConfig = getDealerConfig();
        if (dealerConfig.isEmpty()) {
            return;
        }

        //同步门店所属经销商
        JSONObject dealerIdFields = dealerConfig.get("TPM_DEALER_ID_FIELD");
        if (dealerIdFields != null && !dealerIdFields.isEmpty()) {
            for (Map.Entry<String, Object> dealerIdField : dealerIdFields.entrySet()) {

                ConfigVO vo = new ConfigVO();
                vo.setKey(ConfigEnum.STORE_DEALER.key());
                vo.setValue((String) dealerIdField.getValue());

                String tenantId = dealerIdField.getKey();
                doSaveConfig(tenantId, vo);
            }
        }

        //同步经销商业务类型
        JSONObject dealerRecordTypes = dealerConfig.get("TPM_DEALER_RECORD_TYPE");
        if (dealerRecordTypes != null && !dealerRecordTypes.isEmpty()) {
            for (Map.Entry<String, Object> dealerRecordType : dealerRecordTypes.entrySet()) {

                ConfigVO vo = new ConfigVO();
                vo.setKey(ConfigEnum.DEALER_RECORD_TYPE.key());
                vo.setValue((String) dealerRecordType.getValue());

                String tenantId = dealerRecordType.getKey();
                doSaveConfig(tenantId, vo);
            }
        }
    }

    @Override
    public String grayManyAbstractLayoutFilterConfig() {
        String objListStr = "TPMActivityItemObj,TPMActivityItemCostStandardObj,TPMActivityUnifiedCaseObj,TPMActivityDealerScopeObj,TPMActivityCashingProductScopeObj,TPMActivityObj," + "TPMActivityDetailObj,TPMActivityAgreementDetailObj,TPMActivityStoreObj,TPMActivityAgreementObj,TPMDealerActivityCostObj,TPMActivityProofObj,TPMActivityProofDetailObj," + "TPMStoreWriteOffObj,TPMActivityProofAuditObj,TPMActivityProofAuditDetailObj,TPMActivityAgreementCashingProductObj,TPMActivityCashingProductObj,TPMStoreWriteOffCashingProductObj," + "TPMDealerActivityCashingProductObj,TPMActivityUnifiedCaseProductRangeObj,TPMActivityProductRangeObj,TPMActivityVenueObj,TPMActivityMaterialObj,TPMBudgetBusinessSubjectObj," + "TPMBudgetStatisticTableObj,TPMBudgetAccountObj,TPMBudgetAccountDetailObj,TPMBudgetTransferDetailObj,TPMBudgetOccupationDetailObj," + "TPMBudgetCarryForwardObj,TPMBudgetCarryForwardDetailObj,TPMBudgetDisassemblyObj,TPMBudgetDisassemblyNewDetailObj,TPMBudgetDisassemblyExistsDetailObj," + "TPMBudgetAccrualObj,TPMBudgetAccrualDetailObj";

        String[] strings = objListStr.split(",");
        System.out.println(strings.length);
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < strings.length; i++) {

            //"TPMActivityItemObj":"white:*",
            if (i == 0) {
                stringBuilder.append("\"").append(strings[i]).append("\"").append(":\"white:*\"");
            } else {
                stringBuilder.append(",").append("\"").append(strings[i]).append("\"").append(":\"white:*\"");
            }
        }
        log.info("grayManyAbstractLayoutFilterConfig : {}", stringBuilder);
        return stringBuilder.toString();
    }

    @Override
    public String fsPaasFilterConfigFmcg(String fileStr) {
        String fmcgConfigJSONStr = ConfigFactory.getConfig("fs-paas-filter-config-fmcg").getProfile();
        if (StringUtils.isNotEmpty(fileStr)) {
            fmcgConfigJSONStr = fileStr;
        }
        String objListStr = "TPMActivityItemObj,TPMActivityItemCostStandardObj,TPMActivityUnifiedCaseObj,TPMActivityDealerScopeObj,TPMActivityCashingProductScopeObj,TPMActivityObj," + "TPMActivityDetailObj,TPMActivityAgreementDetailObj,TPMActivityStoreObj,TPMActivityAgreementObj,TPMDealerActivityCostObj,TPMActivityProofObj,TPMActivityProofDetailObj," + "TPMStoreWriteOffObj,TPMActivityProofAuditObj,TPMActivityProofAuditDetailObj,TPMActivityAgreementCashingProductObj,TPMActivityCashingProductObj,TPMStoreWriteOffCashingProductObj," + "TPMDealerActivityCashingProductObj,TPMActivityUnifiedCaseProductRangeObj,TPMActivityProductRangeObj,TPMActivityVenueObj,TPMActivityMaterialObj,TPMBudgetBusinessSubjectObj," + "TPMBudgetStatisticTableObj,TPMBudgetAccountObj,TPMBudgetAccountDetailObj,TPMBudgetTransferDetailObj,TPMBudgetOccupationDetailObj," + "TPMBudgetCarryForwardObj,TPMBudgetCarryForwardDetailObj,TPMBudgetDisassemblyObj,TPMBudgetDisassemblyNewDetailObj,TPMBudgetDisassemblyExistsDetailObj," + "TPMBudgetAccrualObj,TPMBudgetAccrualDetailObj";

        JSONObject jsonObject = JSON.parseObject(fmcgConfigJSONStr);
        JSONObject businessFilterConfig = jsonObject.getJSONObject("businessFilterConfig");

        //处理之前的
        for (Map.Entry<String, Object> entry : businessFilterConfig.entrySet()) {
            String key = entry.getKey();
            if (!key.contains("TPM")) {
                continue;
            }

            JSONObject value = JSON.parseObject(entry.getValue().toString());
            JSONArray business = value.getJSONArray("business");

            JSONObject newValue = new JSONObject();
            JSONArray newBusiness = new JSONArray();
            boolean isDefault = false;
            for (int i = 0; i < business.size(); i++) {
                JSONObject businessObj = business.getJSONObject(i);
                businessObj.getJSONObject("data").put("summary_template_layout", 1);
                JSONArray tenants = businessObj.getJSONArray("tenants");
                String group = businessObj.getString("group");
                if (tenants.isEmpty() && "default".equals(group)) {
                    isDefault = true;
                }
                newBusiness.add(businessObj);
            }
            if (!isDefault) {
                newBusiness.add(getDefaultJSONObj());
            }
            newValue.put("business", newBusiness);
            businessFilterConfig.put(key, newValue);
        }


        //处理新加的
        List<String> objList = Lists.newArrayList(objListStr.split(","));
        List<String> needAddObjList = objList.stream().filter(o -> !businessFilterConfig.containsKey(o)).collect(Collectors.toList());

        for (String obj : needAddObjList) {
            JSONArray business = new JSONArray();
            business.add(getDefaultJSONObj());

            JSONObject businessObj = new JSONObject();
            businessObj.put("business", business);

            businessFilterConfig.put(obj, businessObj);
        }

        JSONObject newBusinessFilterConfig = new JSONObject();
        newBusinessFilterConfig.put("businessFilterConfig", businessFilterConfig);
        log.info("fsPaasFilterConfigFmcg : {}", newBusinessFilterConfig.toJSONString());

        return newBusinessFilterConfig.toJSONString();
    }

    @Override
    public void deleteAllActivityTypeOption(String tenantId) {
        if (!TPMGrayUtils.isDirectDeleteActivityType(tenantId)) {
            return;
        }
        //删除状态为删除的接口
        activityTypeDAO.deleteAllIsDelete(tenantId);
        activityTypeManager.publishSyncActivityTypeFieldTask(tenantId);
    }

    @Override
    public void updateActivityUnifiedCaseLayoutWithDetail(String tenantId, String apiName, List<String> headerNames) {
        headerNames = Lists.newArrayList("参与方案经销商范围", "方案兑付产品范围");
        List<ILayout> layoutByObjectApiName = serviceFacade.getLayoutLogicService().findLayoutByObjectApiName(tenantId, apiName).stream().filter(v -> Objects.equals(v.getLayoutType(), "detail")).collect(Collectors.toList());
        // 更新布局的组件
        if (CollectionUtils.isEmpty(layoutByObjectApiName)) {
            return;
        }
        try {
            for (ILayout layout : layoutByObjectApiName) {
                List<IComponent> components = layout.getComponents();
                if (CollectionUtils.isNotEmpty(components)) {
                    List<String> finalHeaderNames = headerNames;
                    List<IComponent> componentList = components.stream().filter(v -> !finalHeaderNames.contains(v.getHeader())).collect(Collectors.toList());
                    layout.setComponents(componentList);
                }
                serviceFacade.getLayoutLogicService().updateLayout(User.systemUser(tenantId), layout);
            }
        } catch (MetadataServiceException exception) {
            log.info("updateLayout fail ex :", exception);
        }
    }

    @Override
    public String addPGTableField(String tenantId, String tableName, String fieldName, String type) {
        String sql = "alter table %s add column %s %s";
        sql = String.format(sql, SqlEscaper.pg_escape(tableName), SqlEscaper.pg_escape(fieldName), SqlEscaper.pg_escape(type));
        try {
            specialTableMapper.setTenantId(tenantId).insertBySql(sql);
        } catch (Exception e) {
            log.info("addPGTableField err: tenantId={}, tableName={}, fieldName={}, type={}", tenantId, tableName, fieldName, type, e);
            return "fail";
        }
        return "success";
    }

    @Override
    public String deleteObjByApiName(String tenantId, List<String> apiNameList) {
        try {
            if (CollectionUtils.isEmpty(apiNameList)) {
                apiNameList = DEFAULT_DELETE_API_NAMES;
            }
            for (String apiName : apiNameList) {
                for (String sql : DELETE_SQLS) {
                    specialTableMapper.setTenantId(tenantId).deleteBySql(String.format(sql, tenantId, apiName));
                }
            }
        } catch (Exception ex) {
            log.error("delete BySql exception tenantId :{},  ex : ", tenantId, ex);
        }
        return "SUCCESS";
    }

    @Override
    public String deleteTPMObjAndData(String tenantId, List<String> apiNameList, int maxSize, int pageSize) {
        this.deleteObjByApiName(tenantId, apiNameList);
        if (maxSize == 0) {
            maxSize = 500;
        }
        if (pageSize == 0) {
            pageSize = 100;
        }
        activityTypeDAO.deleteALl(tenantId, maxSize, pageSize);
        nodeTemplateDAO.deleteALl(tenantId, maxSize, pageSize);
        return "SUCCESS";
    }

    @Override
    public String deleteTPMObjData(String tenantId) {
        List<String> sqlFromResources = loadSqlFromResource("clear_data.txt");
        log.info("deleteTPMObjData sqlFromResource：{}", sqlFromResources);
        try {
            for (String sql : sqlFromResources) {
                String replaceSQLs = sql.replace(";", "");
                String replaceSQL = replaceSQLs.replace("\r\n", "");
                log.info("deleteTPMObjData replaceSQL：{}", replaceSQL);
                if (replaceSQL.startsWith("delete")) {
                    specialTableMapper.setTenantId(tenantId).deleteBySql(String.format(replaceSQL, tenantId));
                }
                if (replaceSQL.startsWith("update")) {
                    specialTableMapper.setTenantId(tenantId).batchUpdateBySql(String.format(replaceSQL, tenantId));
                }
            }
        } catch (Exception ex) {
            log.error("delete BySql exception tenantId :{},  ex : ", tenantId, ex);
        }
        return "SUCCESS";
    }

    @Override
    public String addTpmStoreWriteOffPlugin(List<String> tenantIds, String config) {
        List<String> tenantIdList;
        if (Strings.isNullOrEmpty(config)) {
            tenantIdList = tenantIds;
        } else {
            JSONObject tenantIdJson = loadTenantIdsFromResource(config);
            tenantIdList = tenantIdJson.getJSONArray("tenantIdList").toJavaList(String.class);
        }
        log.info("tenantIdList size is {}", tenantIdList.size());
        for (String tenantIdValue : tenantIdList) {

            //查询对象是否已绑定插件, 否，则add
            if (!pluginService.findPluginUnit(tenantIdValue, "TPMActivityProofAuditObj", "tpm_store_write_off")) {
                try {
                    pluginService.addPluginUnit(Integer.valueOf(tenantIdValue), -10000, "TPMActivityProofAuditObj", "tpm_store_write_off");
                } catch (Exception e) {
                    log.error("add storeWriteOff plugin fail, e:", e);
                }
            }
        }
        return "SUCCESS";
    }

    @Override
    public String deleteObjectPlugin(List<String> tenantIdList, String apiName, String pluginName) {
        if (CollectionUtils.isEmpty(tenantIdList)) {
            return "空企业";
        }
        log.info("del plugin tenantIdList is {}", tenantIdList);
        int delCount = 0;
        for (String tenantId : tenantIdList) {
            //查询对象是否已绑定插件, 否，则add
            if (pluginService.findPluginUnit(tenantId, apiName, pluginName)) {
                try {
                    pluginService.deletePluginUnit(tenantId, apiName, pluginName);
                    delCount++;
                } catch (Exception e) {
                    log.error("add storeWriteOff plugin fail, e:", e);
                }
            }
        }
        log.info("del plugin tenantId size is {}", delCount);
        return "SUCCESS";
    }

    @Override
    public void copyActivityType(CopyActivityType.Arg arg) {

        bulidMengniuCopyActivityType(arg);

        ActivityTypePO fromActivityTypePO = activityTypeDAO.get(arg.getFromTenantId(), arg.getActivityTypeId());
        List<String> toTenantIds = arg.getToTenantIds();

        log.info("copyActivityType toTenantIds size : {}", toTenantIds.size());
        for (String toTenantId : toTenantIds) {
            log.info("toActivityTypePO toTenantId : {}", toTenantId);
            ActivityTypePO toActivityTypePO = activityTypeDAO.get(toTenantId, fromActivityTypePO.getUniqueId());
            if (Objects.isNull(toActivityTypePO)) {
                log.info("copyActivityType toActivityTypePO no equals uniqueId toTenantId  : {}", toTenantId);
                toActivityTypePO = activityTypeDAO.findActivityTypeByApiName(toTenantId, fromActivityTypePO.getApiName());
            }
            if (Objects.isNull(toActivityTypePO)) {
                log.info("toActivityTypePO isEmpty , toTenantId = {}", toTenantId);
                fromActivityTypePO.setId(null);
                fromActivityTypePO.setTenantId(toTenantId);
                activityTypeDAO.add(toTenantId, -10000, fromActivityTypePO);
                continue;
            }

            List<ActivityNodeTemplatePO> toActivityNodeTemplatePOS = nodeTemplateDAO.sysTemList(toTenantId);
            if (CollectionUtils.isEmpty(toActivityNodeTemplatePOS)) {
                log.info("toActivityNodeTemplatePOS isEmpty , toTenantId = {}", toTenantId);
                continue;
            }

            fromActivityTypePO.setId(toActivityTypePO.getOriginalId());
            fromActivityTypePO.setTenantId(toTenantId);

            ActivityTypeExt toTypeExt = ActivityTypeExt.of(toActivityTypePO);
            fromActivityTypePO.getActivityNodes().forEach(activityNodeEntity -> {
                NodeType nodeType = NodeType.of(activityNodeEntity.getType());
                ActivityNodeEntity toNode = toTypeExt.node(nodeType);

                String nodeTemplateId = Objects.isNull(toNode) ? getNodeTemplateId(toActivityNodeTemplatePOS, nodeType) : toNode.getTemplateId();
                activityNodeEntity.setTemplateId(nodeTemplateId);

                if (NodeType.AUDIT.equals(nodeType)) {
                    String templateId = Objects.isNull(toNode) ? getNodeTemplateId(toActivityNodeTemplatePOS, NodeType.PROOF) : toNode.getActivityProofAuditConfig().getAuditSourceConfig().getTemplateId();
                    activityNodeEntity.getActivityProofAuditConfig().getAuditSourceConfig().setTemplateId(templateId);
                }

                if (NodeType.STORE_WRITE_OFF.equals(nodeType)) {
                    String templateId = Objects.isNull(toNode) ? getNodeTemplateId(toActivityNodeTemplatePOS, NodeType.AUDIT) : toNode.getActivityStoreWriteOffConfig().getStoreWriteOffSourceConfig().getTemplateId();
                    activityNodeEntity.getActivityStoreWriteOffConfig().getStoreWriteOffSourceConfig().setTemplateId(templateId);
                }

                if (NodeType.WRITE_OFF.equals(nodeType)) {
                    String templateId = Objects.isNull(toNode) ? getNodeTemplateId(toActivityNodeTemplatePOS, NodeType.AUDIT) : toNode.getActivityWriteOffConfig().getWriteOffSourceConfig().getTemplateId();
                    activityNodeEntity.getActivityWriteOffConfig().getWriteOffSourceConfig().setTemplateId(templateId);
                }
            });
            activityTypeDAO.edit(toTenantId, -10000, toActivityTypePO.getId().toString(), fromActivityTypePO);
        }
    }

    @Override
    public void copyActivityTypeByMengNiu(CopyActivityType.Arg arg) {
        bulidMengniuCopyActivityType(arg);

        ActivityTypePO fromPo = activityTypeDAO.get(arg.getFromTenantId(), arg.getActivityTypeId());
        List<String> toTenantIds = arg.getToTenantIds();
        log.info("copyActivityType toTenantIds size : {}", toTenantIds.size());
        for (String toTenantId : toTenantIds) {
            log.info("toActivityTypePO toTenantId : {}", toTenantId);
            ActivityTypePO toPo = activityTypeDAO.get(toTenantId, fromPo.getUniqueId());
            if (Objects.isNull(toPo)) {
                log.info("copyActivityType toActivityTypePO no equals uniqueId toTenantId  : {}", toTenantId);
                toPo = activityTypeDAO.findActivityTypeByApiName(toTenantId, fromPo.getApiName());
            }
            if (Objects.isNull(toPo)) {
                log.info("toActivityTypePO isEmpty , toTenantId = {}", toTenantId);
                continue;
            }
            // 蒙牛的更新 启用状态 和 使用范围，处理更改集导致的两个字段未同步的问题。
            toPo.setStatus(fromPo.getStatus());
            toPo.setDepartmentIds(fromPo.getDepartmentIds());

            activityTypeDAO.edit(toTenantId, -10000, toPo.getId().toString(), toPo);
        }
    }

    @Override
    public void batchDeleteObjField(DeleteObjField.Arg arg) {
        bulidMengniuDeleteArg(arg);

        List<String> toTenantIds = arg.getToTenantIds();
        log.info("batchDeleteObjField toTenantIds size : {} , objectApiName : {} , fields : {}", toTenantIds.size(), arg.getObjectApiName(), arg.getFields().toString());
        for (String toTenantId : toTenantIds) {
            log.info("batchDeleteObjField toTenantId : {}", toTenantId);
            moduleInitializationService.disableDeleteObjFields(toTenantId, arg.getObjectApiName(), arg.getFields().toArray(new String[]{}));
        }
    }

    @Override
    public void refreshWithdrawStatus(String tenantId, List<String> withdrawIds) {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(tenantId, withdrawIds, ApiNames.WITHDRAW_RECORD_OBJ);
        objectDataList.forEach(data -> withdrawRecordService.refreshWithdrawStatus(data));
    }

    @Override
    public void batchUpdateLicenseCode(UpdateLicenseCode.Arg arg) {
        buildMengniuUpdateLicenseCodeArg(arg);
        List<String> toTenantIds = arg.getToTenantIds();

        for (String tenantId : toTenantIds) {
            if (!tpm2Service.existTPMLicenseTenant(Integer.valueOf(tenantId))) {
                continue;
            }
            GetLicense.Arg getArg = new GetLicense.Arg();
            getArg.setAppCode(TPM_CODE);
            GetLicense.Result license = fmcgServiceProxy.getLicense(Integer.valueOf(tenantId), -10000, getArg);
            if (Objects.nonNull(license.getLicense())) {
                continue;
            }

            try {
                Add.Arg addArg = new Add.Arg();
                addArg.setAppCode(TPM_CODE);
                fmcgServiceProxy.addLicense(Integer.valueOf(tenantId), -10000, addArg);
            } catch (Exception e) {
                log.info("batchUpdateLicenseCode err : tenantId = {},e", tenantId);
            }
        }
    }

    @Override
    public void batchUpdateActivityTypeUniqueId(List<String> tenantIdList, String uniqueId) {
        if (CollectionUtils.isEmpty(tenantIdList)) {
            tenantIdList = Lists.newArrayList("778347", "778346", "40160035", "40160055", "40160056", "40160057", "40160058", "40160059", "40160060", "40160061", "40160062", "40160063");
        }

        if (Strings.isNullOrEmpty(uniqueId)) {
            uniqueId = "6498ffd1f3e1de0001afaea6";
        }

        for (String tenantId : tenantIdList) {
            ActivityTypePO type = activityTypeDAO.findActivityTypeByApiName(tenantId, PreActivityType.TYPE_ACTIVITY.apiName());
            if (Objects.isNull(type)) {
                log.info("type is empty. tenantId : {}", tenantId);
                continue;
            }
            activityTypeDAO.editUniqueId(tenantId, -10000, type.getOriginalId().toString(), uniqueId, type);
        }
    }

    @Override
    public String fixProofDetail(FixProofDetail.Arg arg) {

        List<String> tenantIds = arg.getTenantIds();

        List<String> fixTenantIds = new ArrayList<>();

        for (String tenantId : tenantIds) {
            // 1. 查询活动举证下从对象跟协议从对象对应不上的问题数据
//            String sql = String.format("select A.id " +
//                    "from fmcg_tpm_activity_proof as A\n" +
//                    "where tenant_id = '%s' and\n" +
//                    "exists (\n" +
//                    "select activity_agreement_id " +
//                    "from fmcg_tpm_activity_agreement_detail as B\n" +
//                    "where is_deleted = 0 and tenant_id = '%s'\n" +
//                    "and A.activity_agreement_id = B.activity_agreement_id\n" +
//                    "group by activity_agreement_id, activity_detail_id \n" +
//                    "having count(activity_detail_id) > 1)", tenantId, tenantId);

            String sql1 = "select distinct activity_agreement_id \n" + "\tfrom fmcg_tpm_activity_agreement_detail\n" + "\twhere is_deleted = 0 and tenant_id = '#{tenant_id}'\n" + "\tand activity_agreement_id > '#{activity_agreement_id}'\n" + "\tgroup by activity_agreement_id, activity_detail_id \n" + "\thaving count(activity_detail_id) > 1" + "\torder by activity_agreement_id " + "\tlimit 200";

            String sql2 = "select id \n" + "\tfrom fmcg_tpm_activity_proof\n" + "\twhere is_deleted = 0 and tenant_id = '#{tenant_id}'\n" + "\tand activity_agreement_id in #{ids}";

            log.info("start tenantId is {}", tenantId);
            try {
                List<String> agreementIds;
                String activityAgreementId = "0";
                // 最多循环次数
                int maxCount = 500;
                do {
                    List<Map> queryList = specialTableMapper.setTenantId(tenantId).findBySql(sql1.replace("#{tenant_id}", tenantId).replace("#{activity_agreement_id}", activityAgreementId));
                    if (CollectionUtils.isEmpty(queryList)) {
                        break;
                    }

                    // 问题举证的ids
                    agreementIds = queryList.stream().map(m -> (String) m.get("activity_agreement_id")).distinct().collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(agreementIds)) {
                        break;
                    }
                    activityAgreementId = agreementIds.get(agreementIds.size() - 1);
                    log.info("activityAgreementId next is {}", activityAgreementId);
                    String ids = FormatUtil.formSqlArray(agreementIds.stream().map(SqlEscaper::pg_escape).collect(Collectors.toList()));
                    List<Map> dbResultList = specialTableMapper.setTenantId(tenantId).findBySql(sql2.replace("#{tenant_id}", tenantId).replace("#{ids}", ids));
                    if (CollectionUtils.isEmpty(dbResultList)) {
                        continue;
                    }
                    List<String> proofIds = dbResultList.stream().map(m -> (String) m.get("id")).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(proofIds)) {
                        continue;
                    }
                    log.info("fix proofIds size is {}", proofIds.size());
                    if (!fixTenantIds.contains(tenantId)) {
                        fixTenantIds.add(tenantId);

                        // 加举证自定义字段占位
                        try {
                            addCustomField(tenantId, tpmFieldService.loadFieldDescribeJsonFromResource(ApiNames.TPM_ACTIVITY_PROOF_OBJ, "refresh_data_sign__c"));
                        } catch (Exception exception) {
                            log.info("exception :", exception);
                        }
                    }

                    doFix(tenantId, proofIds);
                    maxCount--;

                } while (agreementIds.size() >= 200 && maxCount > 0);

            } catch (Exception exception) {
                log.info("exception :", exception);
                throw exception;
            }

        }

        log.info("fix tenant size num is {}", fixTenantIds.size());
        log.info("fix tenantId list is {}", fixTenantIds);
        return "SUCCESS";
    }

    @Override
    public String queryDataBySql(QueryDataBySql.Arg arg) {

        List<String> tenantIds = arg.getTenantIds();
        String sql = arg.getSql();

        List<String> resultTenantIds = new ArrayList<>();
        for (String tenantId : tenantIds) {
            try {

                List<Map> queryList = specialTableMapper.setTenantId(tenantId).findBySql(sql.replace("#{tenant_id}", tenantId));
                if (CollectionUtils.isEmpty(queryList)) {
                    break;
                }
                log.info("queryList size is {}", queryList.size());
                resultTenantIds.add(tenantId);

            } catch (Exception exception) {
                log.info("exception :", exception);
                throw exception;
            }
        }
        log.info("fix tenant size num is {}", resultTenantIds.size());
        log.info("fix tenantId list is {}", resultTenantIds);
        return "SUCCESS";
    }

    /**
     * 根据活动协议【TPMActivityAgreementObj】查询活动举证【TPMActivityProofObj】：
     * 1、如果有活动举证且活动举证的“检核结果【audit_status】”是未检核【schedule】，则将活动协议“状态”【field_Gvb1m__c】更新为已举证【2】；
     * 2、如果有活动举证且活动举证已经检核，只要有一条数据“检核结果【audit_status】的检核结果是合格【pass】”，
     * 将活动协议“状态”【field_Gvb1m__c】更新为检核通过【3】；全部检核结果【audit_status】都不合格【reject】，则将活动协议“状态”【field_Gvb1m__c】更新为检核不通过【4】；
     * 3、如果未查询到活动举证，则保持不变
     *
     * @param arg
     * @return
     */
    @Override
    public String fixAgreementStatus(FixProofDetail.Arg arg) {

        List<String> tenantIds = arg.getTenantIds();

        List<String> fixTenantIds = new ArrayList<>();
        for (String tenantId : tenantIds) {
            try {

                Map<String, List<String>> agreementMap = new HashMap<>();
                User user = User.systemUser(tenantId);
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.setLimit(-1);
                query.setOffset(0);
                List<String> fieldList = Lists.newArrayList(CommonFields.ID, TPMActivityProofFields.AUDIT_STATUS, TPMActivityProofFields.ACTIVITY_AGREEMENT_ID);
                QueryDataUtil.findAndConsume(serviceFacade, user, ApiNames.TPM_ACTIVITY_PROOF_OBJ, query, fieldList, dataList -> {
                    for (IObjectData objectData : dataList) {
                        String auditStatus = objectData.get(TPMActivityProofAuditFields.AUDIT_STATUS, String.class);
                        String agreementId = objectData.get(TPMActivityProofAuditFields.ACTIVITY_AGREEMENT_ID, String.class);
                        if (!agreementMap.containsKey(agreementId)) {
                            agreementMap.put(agreementId, Lists.newArrayList(auditStatus));
                        } else {
                            List<String> list = agreementMap.get(agreementId);
                            if (!list.contains(auditStatus)) {
                                list.add(auditStatus);
                                agreementMap.put(agreementId, list);
                            }
                        }
                    }
                    log.info("dataList size is {}", dataList.size());
                });

                List<String> scheduleList = new ArrayList<>();
                List<String> passList = new ArrayList<>();
                List<String> rejectList = new ArrayList<>();
                log.info("agreementMap key size is {}", agreementMap.keySet().size());
                for (String agreementId : agreementMap.keySet()) {
                    if (Strings.isNullOrEmpty(agreementId)) {
                        continue;
                    }

                    List<String> statusList = agreementMap.get(agreementId);
                    if (statusList.contains(TPMActivityProofFields.AUDIT_STATUS__PASS)) {
                        passList.add(agreementId);
                        continue;
                    }

                    if (statusList.stream().allMatch(TPMActivityProofFields.AUDIT_STATUS__REJECT::equals)) {
                        rejectList.add(agreementId);
                        continue;
                    }
                    if (statusList.stream().allMatch(TPMActivityProofFields.AUDIT_STATUS__SCHEDULE::equals)) {
                        scheduleList.add(agreementId);
                    }
                }
                doFixAgreementStatus(tenantId, scheduleList, passList, rejectList);
                fixTenantIds.add(tenantId);
            } catch (Exception exception) {
                log.info("exception :", exception);
                throw exception;
            }
        }
        log.info("fixTenantIds size is {}", fixTenantIds.size());
        return "SUCCESS";
    }

    @Override
    public void batchInvalidUpStreamData(String tenantId) {
        log.info(" batchInvalidUpStreamData start,tenantId = {} , time = {} ", tenantId, System.currentTimeMillis());
        List<IObjectData> costApproveList = queryCostApprove(tenantId);
        if (CollectionUtils.isEmpty(costApproveList)) {
            log.info(" costApproveList is empty");
            return;
        }
        for (IObjectData costApprove : costApproveList) {
            log.info(" costApprove id = {} ,time = {} ", costApprove.getId(), System.currentTimeMillis());

            List<IObjectData> activityDetailLit = queryActivityDetailByCostApproveId(tenantId, costApprove.getId());
            if (CollectionUtils.isNotEmpty(activityDetailLit)) {
                String outTenantId = costApprove.get("out_tenant_id", String.class);
                String downTenantId = getDownTenantId(tenantId, outTenantId);

                log.info(" activityDetailLit size = {},downTenantId = {} ", activityDetailLit.size(), downTenantId);
                List<String> activityDetailIds = activityDetailLit.stream().map(IObjectData::getId).collect(Collectors.toList());
                List<IObjectData> invalidAgreementList = querDownStreamInvalidAgreementList(downTenantId, activityDetailIds);

                if (CollectionUtils.isNotEmpty(invalidAgreementList)) {
                    log.info(" invalidAgreementList size = {},downTenantId = {} ", invalidAgreementList.size(), downTenantId);
                    List<String> invalidAgreementId = invalidAgreementList.stream().map(IObjectData::getId).collect(Collectors.toList());
                    List<IObjectData> needInvalidActvityDataList = activityDetailLit.stream().filter(iObjectData -> invalidAgreementId.contains(iObjectData.getId())).collect(Collectors.toList());
                    serviceFacade.bulkInvalid(needInvalidActvityDataList, User.systemUser(tenantId));
                }
            }
        }

        log.info(" batchInvalidUpStreamData end, time = {} ", System.currentTimeMillis());
    }

    @Override
    public String updateDataDescribe(UpdateDataDescribe.Arg arg) {
        List<String> tenantIds = arg.getTenantIds();
        String fieldName = arg.getFieldName();
        for (String tenantId : tenantIds) {
            IObjectDescribe orderDescribe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_OBJ);
            IFieldDescribe fieldDescribe = orderDescribe.getFieldDescribe(fieldName);
            fieldDescribe.set(arg.getUpdateAttr(), arg.getParam());
            serviceFacade.updateFieldDescribe(orderDescribe, Lists.newArrayList(fieldDescribe));
        }
        return "SUCCESS";
    }

    @Override
    public String update900DataDescribeAndButton(JSONObject arg) {

        String cookie = arg.getString("cookie");
        String code = arg.getString("code");
        String dev = arg.getString("dev");
        switch (dev) {
            case "local":
                DEV_URL = LOCAL_URL;
                break;
            case "112":
                DEV_URL = CESHI_URL;
                break;
            case "global":
            default:
                DEV_URL = GLOBAL_URL;
        }
        List<String> tenantIds = arg.getObject("tenant_ids", new TypeReference<List<String>>() {
        });
        if (CollectionUtils.isEmpty(tenantIds)) {
            tenantIds = queryAllTPMTenantByLicenseCode(cookie, code, dev);
        }

        log.info("tenantIds size is {}", tenantIds.size());
        for (String tenantId : tenantIds) {
            //支持跨云接口
            postUrl(tenantId);
        }

        return "SUCCESS";
    }

    private void postUrl(String tenantId) {
        try {
            doAddDescribeField(tenantId);
            doUpdateDescribeField(tenantId);
            doCreateInitCloseActivityAgreementButton(tenantId);
        } catch (Exception e) {
            log.error("postUrl error tenantId = {} ", tenantId, e);
        }

    }

    @SneakyThrows
    private void doCreateInitCloseActivityAgreementButton(String tenantId) {
        JSONObject arg = new JSONObject();
        arg.put("module", "init_close_activity_agreement_button");
        arg.put("tenantIds", Lists.newArrayList(tenantId));
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=UTF-8"), arg.toJSONString());

        Request.Builder post = new Request.Builder().url(DEV_URL + "/API/v1/rest/object/TPMActivityObj/controller/Script").get().method("POST", requestBody).addHeader("x-fs-ei", tenantId).addHeader("x-fs-userInfo", "-10000").addHeader("content-type", "application/json; charset=UTF-8");

        Response response = client.newCall(post.build()).execute();
        if (response.code() == 200) {
            log.info("创建终止协议按钮,tenantId={} , response={}", tenantId, response.body().string());
        } else {
            log.info("创建终止协议按钮失败! tenantId={} , message={}", tenantId, response.message());
        }
        response.close();
    }

    @SneakyThrows
    private void doAddDescribeField(String tenantId) {

        String agrStr = "{\n" + "    \"tenant_ids\": [\n" + "    ],\n" + "    \"field_describe_map\": {\n" + "        \"TPMActivityAgreementObj\": {\n" + "            \"close_time\": {\n" + "                \"describe\": {\n" + "                    \"describe_api_name\": \"TPMActivityAgreementObj\",\n" + "                    \"default_is_expression\": false,\n" + "                    \"is_index\": true,\n" + "                    \"is_active\": true,\n" + "                    \"is_encrypted\": false,\n" + "                    \"auto_adapt_places\": false,\n" + "                    \"description\": \"\",\n" + "                    \"is_unique\": false,\n" + "                    \"default_value\": \"\",\n" + "                    \"label\": \"协议终止时间\",\n" + "                    \"type\": \"date_time\",\n" + "                    \"time_zone\": \"GMT+8\",\n" + "                    \"default_to_zero\": false,\n" + "                    \"is_required\": false,\n" + "                    \"api_name\": \"close_time\",\n" + "                    \"define_type\": \"package\",\n" + "                    \"date_format\": \"yyyy-MM-dd HH:mm\",\n" + "                    \"is_single\": false,\n" + "                    \"is_index_field\": false,\n" + "                    \"index_name\": \"l_3\",\n" + "                    \"help_text\": \"\",\n" + "                    \"status\": \"new\"\n" + "                },\n" + "                \"layoutList\": []\n" + "            }\n" + "        }\n" + "    }\n" + "}";
        JSONObject arg = JSONObject.parseObject(agrStr);
        arg.put("tenant_ids", Lists.newArrayList(tenantId));
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=UTF-8"), arg.toJSONString());

        Request.Builder post = new Request.Builder().url(DEV_URL + "/FMCG/InnerAPI/TPM/DevTool/addDescribeField").get().method("POST", requestBody).addHeader("x-fs-ei", tenantId).addHeader("x-fs-userInfo", "-10000").addHeader("content-type", "application/json; charset=UTF-8");

        Response response = client.newCall(post.build()).execute();
        if (response.code() == 200) {
            log.info("新增终止协议日期,tenantId={} , response={}", tenantId, response.body().string());
        } else {
            log.info("新增终止协议日期! tenantId={} , message={}", tenantId, response.message());
        }
        response.close();
    }

    @SneakyThrows
    private void doUpdateDescribeField(String tenantId) {
        String agrStr = "{\n" + "    \"tenant_ids\": [\n" + "    ],\n" + "    \"field_describe_map\": {\n" + "        \"TPMActivityAgreementObj\": {\n" + "            \"agreement_status\": {\n" + "                \"options\": [\n" + "                    {\n" + "                        \"font_color\": \"#FF522A\",\n" + "                        \"label\": \"未生效\",\n" + "                        \"value\": \"schedule\"\n" + "                    },\n" + "                    {\n" + "                        \"font_color\": \"#30C776\",\n" + "                        \"label\": \"已生效\",\n" + "                        \"value\": \"in_progress\"\n" + "                    },\n" + "                    {\n" + "                        \"font_color\": \"#91959E\",\n" + "                        \"label\": \"已过期\",\n" + "                        \"value\": \"end\"\n" + "                    },\n" + "                    {\n" + "                        \"font_color\": \"#ff522a\",\n" + "                        \"label\": \"已终止\",\n" + "                        \"value\": \"close\"\n" + "                    },\n" + "                    {\n" + "                        \"not_usable\": true,\n" + "                        \"label\": \"其他\",\n" + "                        \"value\": \"other\"\n" + "                    }\n" + "                ]\n" + "            }\n" + "        }\n" + "    }\n" + "}";
        JSONObject arg = JSONObject.parseObject(agrStr);
        arg.put("tenant_ids", Lists.newArrayList(tenantId));
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=UTF-8"), arg.toJSONString());

        Request.Builder post = new Request.Builder().url(DEV_URL + "/FMCG/InnerAPI/TPM/DevTool/updateFieldDescribe").get().method("POST", requestBody).addHeader("x-fs-ei", tenantId).addHeader("x-fs-userInfo", "-10000").addHeader("content-type", "application/json; charset=UTF-8");

        Response response = client.newCall(post.build()).execute();
        if (response.code() == 200) {
            log.info("新增协议终止状态,tenantId={} , response={}", tenantId, response.body().string());
        } else {
            log.info("新增协议终止状态! tenantId={} , message={}", tenantId, response.message());
        }
        response.close();
    }

    @SneakyThrows
    @Override
    public List<String> queryAllTPMTenantByLicenseCode(String cookie, String code, String dev) {
        String url = "";
        switch (dev) {
            case "local":
            case "112":
                url = "https://oss.firstshare.cn/paas-console/license/product-boughts?";
                break;
            case "global":
                url = "https://oss.foneshare.cn/paas-console/license/product-boughts?";
                break;
            default:
                break;
        }
        if (Strings.isNullOrEmpty(url)) {
            log.info("queryAllTPMTenantByLicenseCode url is null");
            return Lists.newArrayList();
        }

        Request.Builder post = new Request.Builder().url(url + "dataObject={\"productVersion\":\"trade_promotion_management_app\",\"appId\":\"CRM\",\"expired\":\"true\"}&_=1712922018020")
                .get().method("GET", null);
        post.header("Cookie", cookie);
        Response response = client.newCall(post.build()).execute();
        if (response.code() == 200) {
            assert response.body() != null;
            String string = response.body().string();
            JSONObject jsonObject = JSONObject.parseObject(string);
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            return jsonArray.stream().map(o -> {
                JSONArray jsonObject1 = (JSONArray) o;
                return jsonObject1.getString(0);
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public String updateRedPacket(String tenantId, String field, List<String> dataIds) {

        if (CollectionUtils.isNotEmpty(dataIds)) {
            log.info("red packet dataIds size is {}", dataIds.size());
            List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds(tenantId, dataIds, ApiNames.RED_PACKET_RECORD_OBJ);
            for (IObjectData objectDataById : objectDataByIds) {
                objectDataById.set(RedPacketRecordObjFields.REWARD_PART_CODE, "1");
            }
            serviceFacade.batchUpdateByFields(User.systemUser(tenantId), objectDataByIds, Lists.newArrayList(RedPacketRecordObjFields.REWARD_PART_CODE));

        } else {
            List<String> fields = Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID,
                    CommonFields.NAME, CommonFields.OBJECT_DESCRIBE_API_NAME, RedPacketRecordObjFields.REWARD_PART_CODE);
            AtomicReference<Integer> count = new AtomicReference<>(0);
            QueryDataUtil.findAndConsume(serviceFacade, User.systemUser(tenantId), ApiNames.RED_PACKET_RECORD_OBJ, QueryDataUtil.minimumQuery(), fields, dataList -> {
                log.info("red packet data list size is {}", dataList.size());
                List<List<IObjectData>> partition = Lists.partition(dataList, 200);
                for (List<IObjectData> objectDataList : partition) {
                    for (IObjectData objectDatum : objectDataList) {
                        String partCode = objectDatum.get(RedPacketRecordObjFields.REWARD_PART_CODE, String.class);
                        if (partCode != null) {
                            String[] split = partCode.split("\\.");
                            if (split.length > 1 && "1".equals(split[1])) {
                                objectDatum.set(RedPacketRecordObjFields.REWARD_PART_CODE, "1");
                                count.getAndSet(count.get() + 1);
                            }
                        }
                    }
                    serviceFacade.batchUpdateByFields(User.systemUser(tenantId), objectDataList, Lists.newArrayList(RedPacketRecordObjFields.REWARD_PART_CODE));
                }
            });
            log.info("red packet update count size is {}", count.get());
        }


        return "success";
    }

    @Override
    public String testGray(String tenantId, String type) {

        boolean isAllow = TPMGrayUtils.allowMengNiuRedPacketPublishGrayTenant(tenantId);
        log.info("allowMengNiuRedPacketPublishGrayTenant isAllow is {}", isAllow);

        boolean flag = TPMGrayUtils.allowMengNiuRedPacketPublishV2(tenantId, type);
        log.info("allowMengNiuRedPacketPublishV2 flag is {}", flag);

        boolean logFlag = TPMGrayUtils.allowAgreementAuditAddLogCustomMessage(tenantId);
        log.info("allowAgreementAuditAddLogCustomMessage flag is {}", logFlag);

        return "result : " + flag;
    }

    @Override
    public void batchUpdateActivityNodeUniqueId(List<String> tenantIdList, String templateTenantId) {

        if (CollectionUtils.isEmpty(tenantIdList)) {
            tenantIdList = Lists.newArrayList("778347", "778346", "40160035", "40160055", "40160056", "40160057", "40160058", "40160059", "40160060", "40160061", "40160062", "40160063");
        }

        List<ActivityNodeTemplatePO> templateNodes = nodeTemplateDAO.all(templateTenantId, false);
        Map<String, String> nodeNameIdMap = templateNodes.stream().collect(Collectors.toMap(ActivityNodeTemplatePO::getName, o -> o.getId().toString()));

        for (String tenantId : tenantIdList) {
            List<ActivityNodeTemplatePO> nodes = nodeTemplateDAO.all(tenantId, false);

            Map<String, String> nodeIdUniqueIdMap = Maps.newHashMap();
            for (ActivityNodeTemplatePO node : nodes) {
                String nodeId = node.getId().toHexString();
                String name = node.getName();
                String uniqueId = nodeNameIdMap.get(name);
                if (uniqueId != null) {
                    node.setUniqueId(uniqueId);
                    nodeTemplateDAO.editUniqueId(tenantId, -10000, node.getOriginalId().toString(), uniqueId, node);
                    nodeIdUniqueIdMap.put(nodeId, uniqueId);
                }
            }
            if (nodeIdUniqueIdMap.isEmpty()) {
                continue;
            }

            List<ActivityTypePO> types = activityTypeDAO.all(tenantId, false);
            for (ActivityTypePO type : types) {
                type.getActivityNodes().forEach(activityNodeEntity -> {
                    NodeType nodeType = NodeType.of(activityNodeEntity.getType());

                    String nodeTemplateId = activityNodeEntity.getTemplateId();
                    activityNodeEntity.setTemplateId(nodeIdUniqueIdMap.getOrDefault(nodeTemplateId, nodeTemplateId));

                    if (NodeType.AUDIT.equals(nodeType)) {
                        String innerTemplateId = activityNodeEntity.getActivityProofAuditConfig().getAuditSourceConfig().getTemplateId();
                        activityNodeEntity.getActivityProofAuditConfig().getAuditSourceConfig().setTemplateId(nodeIdUniqueIdMap.getOrDefault(innerTemplateId, innerTemplateId));
                    }

                    if (NodeType.STORE_WRITE_OFF.equals(nodeType)) {
                        String innerTemplateId = activityNodeEntity.getActivityStoreWriteOffConfig().getStoreWriteOffSourceConfig().getTemplateId();
                        activityNodeEntity.getActivityStoreWriteOffConfig().getStoreWriteOffSourceConfig().setTemplateId(nodeIdUniqueIdMap.getOrDefault(innerTemplateId, innerTemplateId));
                    }

                    if (NodeType.WRITE_OFF.equals(nodeType)) {
                        String innerTemplateId = activityNodeEntity.getActivityWriteOffConfig().getWriteOffSourceConfig().getTemplateId();
                        activityNodeEntity.getActivityWriteOffConfig().getWriteOffSourceConfig().setTemplateId(nodeIdUniqueIdMap.getOrDefault(innerTemplateId, innerTemplateId));
                    }
                });
                activityTypeDAO.edit(tenantId, -10000, type.getId().toString(), type);
            }
        }
    }

    @NotNull
    private FieldLayoutPojo getDefaultLayout(User user, String detailLayoutType, String objectApiName, JSONObject field, boolean show) {
        FieldLayoutPojo fieldLayout = new FieldLayoutPojo();
        ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(user, detailLayoutType, objectApiName);

        fieldLayout.setApiName(layout.getName());
        fieldLayout.setLabel(field.getString("label"));
        fieldLayout.setRenderType(field.getString("type"));
        fieldLayout.setReadonly(Boolean.TRUE.equals(field.getBoolean("is_readonly")));
        fieldLayout.setRequired(false);
        fieldLayout.setShow(show);
        fieldLayout.setLayoutType("detail");
        return fieldLayout;
    }


    private boolean isActiveEnterprise(Integer tenantId) {
        try {
            GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
            arg.setEnterpriseId(tenantId);
            GetSimpleEnterpriseResult result = enterpriseEditionService.getSimpleEnterprise(arg);
            return result.getSimpleEnterprise() != null && result.getSimpleEnterprise().getRunStatus() == 2;
        } catch (Exception e) {
            log.info("get tenant info err.{}", tenantId, e);
            return false;
        }
    }

    private String getDownTenantId(String tenantId, String outTenantId) {
        IObjectData enterpriseRelation = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(outTenantId), ApiNames.ENTERPRISE_RELATION_OBJ).get(0);
        String enterpriseAccount = enterpriseRelation.get("enterprise_account", String.class);
        int downTenantId = eieaConverter.enterpriseAccountToId(enterpriseAccount);
        return String.valueOf(downTenantId);
    }

    /**
     * 查作废的活动协议数据
     */
    private List<IObjectData> querDownStreamInvalidAgreementList(String downTenantId, List<String> ids) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(Lists.newArrayList(ids));

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.IN);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__INVALID));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter, lifeStatusFilter);

        return QueryDataUtil.find(serviceFacade, downTenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, stq, Lists.newArrayList(CommonFields.ID));
    }

    private List<IObjectData> queryActivityDetailByCostApproveId(String tenantId, String id) {

        IFilter idFilter = new Filter();
        idFilter.setFieldName("field_LP9lc__c");
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        ArrayList<String> returnFields = Lists.newArrayList(CommonFields.ID, "tenant_id", "object_describe_api_name");
        return QueryDataUtil.find(serviceFacade, tenantId, "object_Jk6CW__c", stq, returnFields);
    }

    private List<IObjectData> queryCostApprove(String tenantId) {
        String beginDateTime = getDateTime(1, 0, 0, 0);
        String endDateTime = getDateTime(31, 23, 59, 59);
        //开始日期
        IFilter beginTime = new Filter();
        beginTime.setFieldName("field_E79qh__c");
        beginTime.setOperator(Operator.BETWEEN);
        beginTime.setFieldValues(Lists.newArrayList(beginDateTime, endDateTime));

        //结束日期
        IFilter endTime = new Filter();
        endTime.setFieldName("field_WlIqo__c");
        endTime.setOperator(Operator.BETWEEN);
        endTime.setFieldValues(Lists.newArrayList(beginDateTime, endDateTime));

        //数据是否错误
        IFilter dataIsError = new Filter();
        dataIsError.setFieldName("field_3uwnv__c");
        dataIsError.setOperator(Operator.EQ);
        dataIsError.setFieldValues(Lists.newArrayList("true"));

        //提交状态
        IFilter submitStatus = new Filter();
        submitStatus.setFieldName("field_CBoh9__c");
        submitStatus.setOperator(Operator.EQ);
        submitStatus.setFieldValues(Lists.newArrayList("2"));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(beginTime, endTime, dataIsError, submitStatus);

        return QueryDataUtil.find(serviceFacade, tenantId, "CostApprove__c", stq, Lists.newArrayList(CommonFields.ID, "out_tenant_id"));
    }

    private String getDateTime(int day, int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(2024, Calendar.MARCH, day, hour, minute, second);
        return String.valueOf(calendar.getTimeInMillis());
    }

    private void doFixAgreementStatus(String tenantId, List<String> scheduleList, List<String> passList, List<String> rejectList) {

        if (CollectionUtils.isNotEmpty(scheduleList)) {
            updateAgreementStatus(tenantId, scheduleList, "2");
        }
        if (CollectionUtils.isNotEmpty(passList)) {
            updateAgreementStatus(tenantId, passList, "3");
        }
        if (CollectionUtils.isNotEmpty(rejectList)) {
            updateAgreementStatus(tenantId, rejectList, "4");
        }
    }

    private void updateAgreementStatus(String tenantId, List<String> dataList, String statusCode) {
        List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds(tenantId, dataList, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        objectDataByIds.forEach(data -> data.set("field_Gvb1m__c", statusCode));
        serviceFacade.batchUpdateByFields(User.systemUser(tenantId), objectDataByIds, Lists.newArrayList("field_Gvb1m__c"));
        log.info("updateAgreementStatus tenantId is {}, statusCode is {}, dataList size is {}", tenantId, statusCode, dataList.size());
    }

    private void addCustomField(String tenantId, String fieldDescribe) {

        User superUser = User.systemUser(tenantId);

        JSONObject field = JSON.parseObject(fieldDescribe);

        FieldLayoutPojo fieldLayout = new FieldLayoutPojo();
        ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(superUser, "detail", ApiNames.TPM_ACTIVITY_PROOF_OBJ);

        fieldLayout.setApiName(layout.getName());
        fieldLayout.setLabel(field.getString("label"));
        fieldLayout.setRenderType(field.getString("type"));
        fieldLayout.setReadonly(Boolean.TRUE.equals(field.getBoolean("is_readonly")));
        fieldLayout.setRequired(false);
        fieldLayout.setShow(false);
        fieldLayout.setLayoutType("detail");

        serviceFacade.addDescribeCustomField(superUser, ApiNames.TPM_ACTIVITY_PROOF_OBJ, fieldDescribe, Lists.newArrayList(fieldLayout), Lists.newArrayList());

    }

    private void doFix(String tenantId, List<String> proofIds) {
        // 查询活动举证对象
        CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_OBJ, buildTemplateQueryByIds(proofIds), proofObj -> {
            // 补充缺失的 举证项目从对象数据，并回滚举证的对象状态未检核。
            handleProofDetailWithUpdateProof(tenantId, proofObj);
        });
    }

    private SearchTemplateQuery buildTemplateQueryByIds(List<String> proofIds) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(200);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        IFilter proofIdFilter = new Filter();
        proofIdFilter.setFieldName(CommonFields.ID);
        proofIdFilter.setOperator(Operator.IN);
        proofIdFilter.setFieldValues(proofIds);

        query.setFilters(Lists.newArrayList(proofIdFilter));
        return query;
    }

    private void handleProofDetailWithUpdateProof(String tenantId, List<IObjectData> proofObj) {

        List<String> proofIds = proofObj.stream().map(DBRecord::getId).collect(Collectors.toList());
        // 查询举证项目从对象
        List<IObjectData> proofDetailObj = findProofDetailObjByProofIds(tenantId, proofIds);
        if (proofDetailObj == null) return;

        // 举证项目从对象按举证id分组
        Map<String, List<IObjectData>> proofDetailMap = proofDetailObj.stream().collect(Collectors.groupingBy(v -> v.get(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID, String.class)));

        List<String> agreementId = proofObj.stream().map(v -> v.get("activity_agreement_id", String.class)).collect(Collectors.toList());
        // 查询协议项目从对象
        List<IObjectData> agreementDetailObj = findAgreementDetailByAgreementId(tenantId, agreementId);
        if (agreementDetailObj == null) return;

        // 协议项目从对象按协议id分组
        Map<String, List<IObjectData>> agreementDetailMap = agreementDetailObj.stream().collect(Collectors.groupingBy(v -> v.get(TPMActivityAgreementDetailFields.ACTIVITY_AGREEMENT_ID, String.class)));

        FieldResult fieldDescribe = serviceFacade.findCustomFieldDescribe(tenantId, ApiNames.TPM_ACTIVITY_PROOF_OBJ, TPMActivityProofFields.TOTAL);
        CountFieldDescribe totalDesc = new CountFieldDescribe(ObjectFieldDescribeDocument.of(fieldDescribe.getField()));
        List<IObjectData> auditProofDataList = new ArrayList<>();
        List<IObjectData> newObjectDataList = new ArrayList<>();
        List<IObjectData> masterObjectDataList = new ArrayList<>();
        for (IObjectData objectData : proofObj) {
            String activityAgreementId = objectData.get("activity_agreement_id", String.class);

            List<IObjectData> dataList = agreementDetailMap.get(activityAgreementId);
            if (CollectionUtils.isEmpty(dataList)) {
                continue;
            }
            List<IObjectData> detailList = proofDetailMap.get(objectData.getId());
            // 数量一致过滤
            if (dataList.size() == detailList.size()) {
                continue;
            }
            if (!objectData.get("audit_status", String.class).equals("schedule")) {
                auditProofDataList.add(objectData);
            }
            List<IObjectData> lossAgreementDetail = getLossAgreementDetail(dataList, detailList);
            if (CollectionUtils.isNotEmpty(lossAgreementDetail)) {
                List<String> activityDetailIds = lossAgreementDetail.stream().map(v -> v.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID, String.class)).collect(Collectors.toList());
                Map<String, IObjectData> activityDetailMap = serviceFacade.findObjectDataByIds(tenantId, activityDetailIds, ApiNames.TPM_ACTIVITY_DETAIL_OBJ).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
                for (IObjectData data : lossAgreementDetail) {
                    newObjectDataList.add(suppleProofDetail(tenantId, data, activityDetailMap, objectData));
                }
            }

            masterObjectDataList.add(objectData);
        }
        transactionProxy.run(() -> {

            // 删除关联的检核数据、并处理门店费用核销
            deleteAuditDataByProofIds(tenantId, auditProofDataList);

            User sys = User.systemUser(tenantId);
            Lists.partition(newObjectDataList, 200).forEach(list -> serviceFacade.bulkSaveObjectData(list, sys));

            for (IObjectData objectData : masterObjectDataList) {
                double ratio = objectData.get("cost_conversion_ratio", Double.class);
                calculateCount(tenantId, totalDesc, objectData, ratio);
                objectData.set("refresh_data_sign__c", "1");
            }

            serviceFacade.batchUpdate(masterObjectDataList, sys);
        });
    }

    private void calculateCount(String tenantId, CountFieldDescribe totalDesc, IObjectData objectData, double ratio) {
        // 触发举证的统计，，total
        //  total 统计的 subtotal ==  $activity_agreement_detail_id__r.subtotal$
        Map<String, Object> calculateMap = serviceFacade.calculateCountField(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_OBJ, objectData.getId(), Lists.newArrayList(totalDesc));
        BigDecimal subtotal = new BigDecimal(String.valueOf(calculateMap.getOrDefault(TPMActivityProofFields.TOTAL, 0)));
        // actual_total 举证申报费用(元)
        objectData.set(TPMActivityProofFields.TOTAL, subtotal);
        objectData.set(TPMActivityProofFields.ACTUAL_TOTAL, subtotal.multiply(BigDecimal.valueOf(ratio)));
        // 状态更新未检核。
        objectData.set(TPMActivityProofFields.AUDIT_STATUS, TPMActivityProofFields.AUDIT_STATUS__SCHEDULE);
    }

    private List<IObjectData> getLossAgreementDetail(List<IObjectData> dataList, List<IObjectData> detailList) {
        List<String> ids = dataList.stream().map(DBRecord::getId).collect(Collectors.toList());

        List<String> agreementDetailId = detailList.stream().map(v -> v.get("activity_agreement_detail_id", String.class)).collect(Collectors.toList());

        ids.removeAll(agreementDetailId);

        return dataList.stream().filter(v -> ids.contains(v.getId())).collect(Collectors.toList());
    }

    private IObjectData suppleProofDetail(String tenantId, IObjectData agreementDetail, Map<String, IObjectData> activityDetailMap, IObjectData proof) {
        String activityDetailId = agreementDetail.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID, String.class);
        IObjectData activityDetail = activityDetailMap.get(activityDetailId);

//        PreAdd.ProofDetailDataVO detail = new PreAdd.ProofDetailDataVO();
//        extracted(agreementDetail, activityDetail, detail);

        IObjectData detailObject = new ObjectData();
        detailObject.set("amount_standard_check", String.valueOf(activityDetail.get(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK)));
        detailObject.set("amount_standard_check__v", (Boolean) activityDetail.get(TPMActivityDetailFields.AMOUNT_STANDARD_CHECK__V));
        detailObject.set("calculate_pattern", (String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN));
        detailObject.set("calculate_pattern__v", (String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN__V));
        detailObject.set("calculate_pattern__r", (String) activityDetail.get(TPMActivityDetailFields.CALCULATE_PATTERN__R));
        detailObject.set("activity_item_id", (String) activityDetail.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID));
        detailObject.set("activity_detail_id", activityDetail.getId());
        detailObject.set("activity_detail_id__r", activityDetail.getName());
        detailObject.set("type", (String) activityDetail.get(TPMActivityDetailFields.TYPE));
        detailObject.set("activity_agreement_detail_id", agreementDetail.getId());
        detailObject.set("activity_agreement_detail_id__r", agreementDetail.getName());
        detailObject.set("amount_standard", agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD, Double.class, 0D));
        detailObject.set("activity_cost_standard", agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_COST_STANDARD, Double.class, 0D));
        detailObject.set("agreement_amount_standard", agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD, Double.class, 0D));
        detailObject.set("proof_detail_amount_standard", agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_AMOUNT_STANDARD, Double.class, 0D));
        detailObject.set("proof_detail_cost_standard", agreementDetail.get(TPMActivityAgreementDetailFields.AGREEMENT_COST_STANDARD, Double.class, 0D));
        Boolean isReportItemQuantity = Boolean.valueOf(agreementDetail.get(TPMActivityAgreementDetailFields.IS_REPORT_ITEM_QUANTITY, Boolean.class, false).toString());
        detailObject.set("is_report_item_quantity", isReportItemQuantity);

        if (isReportItemQuantity) {
            detailObject.set("amount", 1);
        } else {
            detailObject.set("amount", detailObject.get("amount_standard"));
        }

        if (TPMGrayUtils.proofDataTypeAllUseExistOrNot(tenantId)) {
            detailObject.set("proof_data_type", TPMActivityItemFields.PROOF_DATA_TYPE__EXIST_OR_NOT);
        } else {
            detailObject.set("proof_data_type", TPMActivityItemFields.PROOF_DATA_TYPE__NUMBER);
        }

        // 举证项目名称，字符串，暂取。
        detailObject.set("proof_item", activityDetail.getName());
        detailObject.set("subtotal", agreementDetail.get(TPMActivityAgreementDetailFields.SUBTOTAL, Double.class, 0D));
        detailObject.set(CommonFields.OBJECT_DESCRIBE_API_NAME, ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ);
        detailObject.set(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID, proof.getId());
        detailObject.set("data_own_department", proof.getDataOwnDepartment());
        detailObject.setRecordType("default__c");
        detailObject.setOwner(proof.getOwner());
        detailObject.setTenantId(proof.getTenantId());

        return detailObject;
    }

    @Nullable
    private List<IObjectData> findProofDetailObjByProofIds(String tenantId, List<String> proofIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        IFilter agreementIdFilter = new Filter();
        agreementIdFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        agreementIdFilter.setOperator(Operator.IN);
        agreementIdFilter.setFieldValues(proofIds);

        query.setFilters(Lists.newArrayList(agreementIdFilter));
        List<IObjectData> agreementDetailObj = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(agreementDetailObj)) {
            return null;
        }
        return agreementDetailObj;
    }

    @Nullable
    private List<IObjectData> findAgreementDetailByAgreementId(String tenantId, List<String> agreementId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        IFilter agreementIdFilter = new Filter();
        agreementIdFilter.setFieldName(TPMActivityAgreementDetailFields.ACTIVITY_AGREEMENT_ID);
        agreementIdFilter.setOperator(Operator.IN);
        agreementIdFilter.setFieldValues(agreementId);

        query.setFilters(Lists.newArrayList(agreementIdFilter));
        List<IObjectData> agreementDetailObj = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(agreementDetailObj)) {
            return null;
        }
        return agreementDetailObj;
    }

    private void deleteAuditDataByProofIds(String tenantId, List<IObjectData> auditProofObj) {
        if (CollectionUtils.isEmpty(auditProofObj)) return;

        List<String> proofIds = auditProofObj.stream().map(DBRecord::getId).collect(Collectors.toList());


        List<IObjectData> proofAuditObj = findProofAuditByProofIds(tenantId, proofIds);
        if (proofAuditObj == null) return;

        List<String> proofAuditIds = proofAuditObj.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<IObjectData> proofAuditDetailObj = findProofAuditDetailByProofIds(tenantId, proofAuditIds);
        if (proofAuditDetailObj == null) return;

        List<String> storeWriteOffIds = proofAuditObj.stream().map(v -> v.get(TPMActivityProofAuditFields.STORE_WRITE_OFF_ID, String.class)).distinct().collect(Collectors.toList());

        List<List<IObjectData>> partitions = Lists.partition(proofAuditObj, 50);

        // 举证检核的从对象。
        List<List<IObjectData>> detailPartitions = Lists.partition(proofAuditDetailObj, 50);

        User sys = User.systemUser(tenantId);
        // 批量循环去作废删除，检核数据。 todo:是否把从对象也删除了？ -- 不会连带从对象一起
        transactionProxy.run(() -> {
            for (List<IObjectData> partition : partitions) {
                serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(partition, sys);
            }

            for (List<IObjectData> partition : detailPartitions) {
                serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(partition, sys);
            }

            List<IObjectData> storeWriteOffObj = findStoreWriteOffObjByIds(tenantId, storeWriteOffIds);

            FieldResult passField = serviceFacade.findCustomFieldDescribe(tenantId, ApiNames.TPM_STORE_WRITE_OFF_OBJ, "activity_audit_pass");
            CountFieldDescribe passDesc = new CountFieldDescribe(ObjectFieldDescribeDocument.of(passField.getField()));

            FieldResult rejectField = serviceFacade.findCustomFieldDescribe(tenantId, ApiNames.TPM_STORE_WRITE_OFF_OBJ, "activity_audit_reject");
            CountFieldDescribe rejectDesc = new CountFieldDescribe(ObjectFieldDescribeDocument.of(rejectField.getField()));

            List<Count> countFieldDescribeList = Lists.newArrayList(passDesc, rejectDesc);
            // 更新门店费用核销状态。
            for (IObjectData storeWriteOffId : storeWriteOffObj) {
                updateStoreWriteOff(tenantId, storeWriteOffId, countFieldDescribeList);
            }
            Lists.partition(storeWriteOffObj, 200).forEach(list -> serviceFacade.batchUpdate(list, sys));
        });

    }

    private void updateStoreWriteOff(String tenantId, IObjectData storeWriteOff, List<Count> countFieldDescribeList) {

        Map<String, Object> calculateMap = serviceFacade.calculateCountField(User.systemUser(tenantId), ApiNames.TPM_STORE_WRITE_OFF_OBJ, storeWriteOff.getId(), countFieldDescribeList);
        BigDecimal activityAuditPass = new BigDecimal(String.valueOf(calculateMap.getOrDefault("activity_audit_pass", 0)));
        BigDecimal activityAuditReject = new BigDecimal(String.valueOf(calculateMap.getOrDefault("activity_audit_reject", 0)));

        storeWriteOff.set("activity_audit_pass", activityAuditPass);
        storeWriteOff.set("activity_audit_reject", activityAuditReject);
        storeWriteOff.set("activity_audit_total", activityAuditPass.add(activityAuditReject));
        //更新为 未进行手动核销
        storeWriteOff.set(TPMStoreWriteOffFields.WRITE_OFF_STATUS, TPMStoreWriteOffFields.WriteOffStatus.AUTOMATIC_WRITE_OFF);
    }

    private BigDecimal calculateStoreAmount(String tenantId, String objectId, String apiName, String costFieldApiName, String storeWriteOffFieldApiName, String type) {
        //求和
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(200);
        query.setOffset(0);

        //关联门店费用核销的 id
        Filter storeFilter = new Filter();
        storeFilter.setFieldName(storeWriteOffFieldApiName);
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(objectId));
        query.setFilters(Lists.newArrayList(storeFilter));

        //如果依据对象是 活动举证检核的，，条件上加 检核结果 = pass 通过
        if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(apiName)) {
            Filter statusFilter = new Filter();
            statusFilter.setFieldName(TPMActivityProofAuditFields.AUDIT_STATUS);
            statusFilter.setOperator(Operator.EQ);
            statusFilter.setFieldValues(Lists.newArrayList("pass"));
            query.getFilters().add(statusFilter);
        }

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(User.systemUser(tenantId), query, apiName, Lists.newArrayList(storeWriteOffFieldApiName), type, costFieldApiName);

        BigDecimal amount;
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(data)) {
            String key = String.format("%s_%s", type, costFieldApiName);
            amount = data.get(0).get(key, BigDecimal.class);
            log.info("aggregate search date size is {}, amount is {}", data.size(), amount);
        } else {
            amount = new BigDecimal("0");
        }

        return amount.setScale(2, RoundingMode.HALF_UP);
    }


    private List<IObjectData> findStoreWriteOffObjByIds(String tenantId, List<String> ids) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        IFilter proofIdFilter = new Filter();
        proofIdFilter.setFieldName(CommonFields.ID);
        proofIdFilter.setOperator(Operator.IN);
        proofIdFilter.setFieldValues(ids);

        query.setFilters(Lists.newArrayList(proofIdFilter));
        List<IObjectData> storeWriteOffObj = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_STORE_WRITE_OFF_OBJ, query);
        if (CollectionUtils.isEmpty(storeWriteOffObj)) {
            return new ArrayList<>();
        }
        return storeWriteOffObj;
    }

    @Nullable
    private List<IObjectData> findProofAuditDetailByProofIds(String tenantId, List<String> proofAuditIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        IFilter proofAuditIdFilter = new Filter();
        proofAuditIdFilter.setFieldName(TPMActivityProofAuditDetailFields.ACTIVITY_PROOF_AUDIT_ID);
        proofAuditIdFilter.setOperator(Operator.IN);
        proofAuditIdFilter.setFieldValues(proofAuditIds);

        query.setFilters(Lists.newArrayList(proofAuditIdFilter));
        List<IObjectData> proofAuditObj = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(proofAuditObj)) {
            return null;
        }
        return proofAuditObj;
    }

    @Nullable
    private List<IObjectData> findProofAuditByProofIds(String tenantId, List<String> proofIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        IFilter proofIdFilter = new Filter();
        proofIdFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
        proofIdFilter.setOperator(Operator.IN);
        proofIdFilter.setFieldValues(proofIds);

        query.setFilters(Lists.newArrayList(proofIdFilter));
        List<IObjectData> proofAuditObj = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query);
        if (CollectionUtils.isEmpty(proofAuditObj)) {
            return null;
        }
        return proofAuditObj;
    }

    @NotNull
    private String getNodeTemplateId(List<ActivityNodeTemplatePO> toActivityNodeTemplatePOS, NodeType nodeType) {
        for (ActivityNodeTemplatePO template : toActivityNodeTemplatePOS) {
            if (nodeType.value().equals(template.getType())) {
                return template.getUniqueId();
            }
        }
        return "";
    }

    private void bulidMengniuCopyActivityType(CopyActivityType.Arg arg) {
        String mengniuTenantId = arg.getMengniuTenantId();
        if (Strings.isNullOrEmpty(mengniuTenantId) || CollectionUtils.isNotEmpty(arg.getToTenantIds())) {
            return;
        }
        String fromTenantId = arg.getFromTenantId();
        String templateDa = eieaConverter.enterpriseIdToAccount(Integer.parseInt(fromTenantId));
        List<IObjectData> dataList = queryEnterpriseRelationObj(mengniuTenantId, templateDa);
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<String> enterpriseAccount = dataList.stream().map(data -> data.get("enterprise_account", String.class)).collect(Collectors.toList());
            Map<String, Integer> enterpriseAccountToIdMap = eieaConverter.enterpriseAccountToId(enterpriseAccount);
            arg.setToTenantIds(enterpriseAccountToIdMap.values().stream().map(String::valueOf).distinct().collect(Collectors.toList()));
        }
    }

    private void bulidMengniuDeleteArg(DeleteObjField.Arg arg) {
        String mengniuTenantId = arg.getMengniuTenantId();
        if (Strings.isNullOrEmpty(mengniuTenantId) || CollectionUtils.isNotEmpty(arg.getToTenantIds())) {
            return;
        }
        String fromTenantId = arg.getFromTenantId();
        String templateDa = eieaConverter.enterpriseIdToAccount(Integer.parseInt(fromTenantId));
        List<IObjectData> dataList = queryEnterpriseRelationObj(mengniuTenantId, templateDa);
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<String> enterpriseAccount = dataList.stream().map(data -> data.get("enterprise_account", String.class)).collect(Collectors.toList());
            Map<String, Integer> enterpriseAccountToIdMap = eieaConverter.enterpriseAccountToId(enterpriseAccount);
            arg.setToTenantIds(enterpriseAccountToIdMap.values().stream().map(String::valueOf).distinct().collect(Collectors.toList()));
        }
    }

    private void buildMengniuUpdateLicenseCodeArg(UpdateLicenseCode.Arg arg) {
        String mengniuTenantId = arg.getMengniuTenantId();
        if (Strings.isNullOrEmpty(mengniuTenantId) || CollectionUtils.isNotEmpty(arg.getToTenantIds())) {
            return;
        }
        String fromTenantId = arg.getTemplateTenantId();
        String templateDa = eieaConverter.enterpriseIdToAccount(Integer.parseInt(fromTenantId));
        List<IObjectData> dataList = queryEnterpriseRelationObj(mengniuTenantId, templateDa);
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<String> enterpriseAccount = dataList.stream().map(data -> data.get("enterprise_account", String.class)).collect(Collectors.toList());
            Map<String, Integer> enterpriseAccountToIdMap = eieaConverter.enterpriseAccountToId(enterpriseAccount);
            arg.setToTenantIds(enterpriseAccountToIdMap.values().stream().map(String::valueOf).distinct().collect(Collectors.toList()));
        }
    }

    private JSONObject loadTenantIdsFromResource(String config) {
        String json;
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/mn/%s.json", config));
            json = new String(Files.readAllBytes(file.toPath()));
        } catch (IOException ex) {
            throw new MetaDataBusinessException("simple layout can not found.");
        }
        return JSON.parseObject(json);
    }

    public JSONObject getDefaultJSONObj() {
        JSONObject businessObj = new JSONObject();
        businessObj.put("tenants", Lists.newArrayList());
        businessObj.put("data", JSON.parseObject("{\"summary_template_layout\":1}"));
        businessObj.put("group", "default");
        return businessObj;
    }

    private void doSaveConfig(String tenantId, ConfigVO vo) {
        ApiContextManager.setContext(ApiContext.builder().tenantId(tenantId).tenantAccount(tenantId).employeeId(-10000).appId("FMCG_TPM").build());

        AddConfig.Arg arg = new AddConfig.Arg();
        arg.setConfigs(Lists.newArrayList(vo));
        try {
            tpmConfigService.save(arg);
        } catch (Exception e) {
            log.error("doSaveConfig err: arg={}", JSON.toJSONString(arg), e);
        }
    }

    private Map<String, JSONObject> getDealerConfig() {
        Map<String, JSONObject> config = Maps.newHashMap();
        ConfigFactory.getConfig("gray-rel-fmcg", conf -> {
            String dealerIdField = conf.get("TPM_DEALER_ID_FIELD");
            if (!Strings.isNullOrEmpty(dealerIdField)) {
                config.put("TPM_DEALER_ID_FIELD", JSON.parseObject(dealerIdField));
            }
            String tpmDealerRecordType = conf.get("TPM_DEALER_RECORD_TYPE");
            if (!Strings.isNullOrEmpty(tpmDealerRecordType)) {
                config.put("TPM_DEALER_RECORD_TYPE", JSON.parseObject(tpmDealerRecordType));
            }
        });
        return config;
    }

    private List<String> getAllTenantIds(String tenantIds) {
        List<String> all = Lists.newArrayList();

        if (Strings.isNullOrEmpty(tenantIds)) {
            all = tpm2Service.queryBudgetTenant();
        } else {
            if (tenantIds.contains(",")) {
                Lists.newArrayList(tenantIds.split(","));
            } else {
                all.add(tenantIds);
            }
        }
        return all;
    }

    private void doUpdateBudgetStatisticDescribeUsedOrFrozen(List<String> all) {
        for (String tenantId : all) {
            try {
                IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_BUDGET_STATISTIC_TABLE);
                if (Objects.isNull(describe)) {
                    continue;
                }

                Map<String, IFieldDescribe> fieldDescribeMap = describe.getFieldDescribeMap();

                List<IFieldDescribe> updateField = Lists.newArrayList();
                IFieldDescribe usedAmountField = fieldDescribeMap.get("used_amount");
                if (usedAmountField != null && !"formula".equals(usedAmountField.get("count_field_type"))) {
                    usedAmountField.set("count_field_type", "formula");
                    updateField.add(usedAmountField);
                }

                IFieldDescribe frozenAmountField = fieldDescribeMap.get("frozen_amount");
                if (frozenAmountField != null && !"formula".equals(frozenAmountField.get("count_field_type"))) {
                    frozenAmountField.set("count_field_type", "formula");
                    updateField.add(frozenAmountField);
                }

                if (CollectionUtils.isNotEmpty(updateField)) {
                    serviceFacade.updateFieldDescribe(describe, updateField);
                }
            } catch (Exception e) {
                log.info(" doUpdateBudgetStatisticDescribeUsedOrFrozen error tenantId = {} ", tenantId, e);
            }
        }
    }


    private void refreshStatisticsUsedOrFrozenData(String tenantId, List<IObjectData> accounts) {
        List<String> budgetStatisticIds = accounts.stream().map(a -> a.get(TPMBudgetAccountFields.BUDGET_STATISTIC_TABLE_ID, String.class)).distinct().collect(Collectors.toList());
        for (String budgetStatisticId : budgetStatisticIds) {
            budgetStatisticTableService.refresh(tenantId, User.systemUser(tenantId), budgetStatisticId);
        }
    }

    private void recalculateStatisticsData(String tenantId, List<IObjectData> accounts) {
        List<IObjectData> unStatisticAccounts = accounts.stream().filter(f -> Strings.isNullOrEmpty(f.get(TPMBudgetAccountFields.BUDGET_STATISTIC_TABLE_ID, String.class))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(unStatisticAccounts)) {
            return;
        }

        budgetStatisticTableService.asyncDoStatistic(tenantId, unStatisticAccounts);
    }

    private void clearUselessStatistics(String tenantId, List<IObjectData> allAccounts) {
        List<String> usefulStatisticIds = allAccounts.stream().filter(o -> !Strings.isNullOrEmpty(o.get(TPMBudgetAccountFields.BUDGET_STATISTIC_TABLE_ID, String.class))).map(m -> m.get(TPMBudgetAccountFields.BUDGET_STATISTIC_TABLE_ID, String.class)).collect(Collectors.toList());

        List<IObjectData> statistics = queryAllStatistic(tenantId);
        List<IObjectData> uselessStatistics = statistics.stream().filter(f -> !usefulStatisticIds.contains(f.getId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(uselessStatistics)) {
            return;
        }

        log.info("clear useless statistics : {}", uselessStatistics.stream().map(IObjectData::getId).collect(Collectors.toList()));

        // ⚠️ note : PAAS 提供了 bulkInvalidAndDeleteWithSuperPrivilege 方法，可以作废并删除数据，且注意单次不能删除大量数据
        invalidAndDeleteObjectData(tenantId, uselessStatistics);

        log.info("clear useless statistics end");
    }

    private void invalidAndDeleteObjectData(String tenantId, List<IObjectData> data) {
        List<List<IObjectData>> partitions = Lists.partition(data, 50);
        User sys = User.systemUser(tenantId);
        for (List<IObjectData> partition : partitions) {
            serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(partition, sys);
        }
    }

    private List<IObjectData> queryAllStatistic(String tenantId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(0);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setFindExplicitTotalNum(false);
        stq.setSearchSource("db");

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        stq.setFilters(Lists.newArrayList(lifeStatusFilter));

        return CommonUtils.queryData(this.serviceFacade, User.systemUser(tenantId), ApiNames.TPM_BUDGET_STATISTIC_TABLE, stq);
    }

    private List<IObjectData> queryAllAccount(String tenantId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(0);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setFindExplicitTotalNum(false);
        stq.setSearchSource("db");

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        stq.setFilters(Lists.newArrayList(lifeStatusFilter));

        return CommonUtils.queryData(this.serviceFacade, User.systemUser(tenantId), ApiNames.TPM_BUDGET_ACCOUNT, stq);
    }

    private List<IObjectData> queryAllNeedRefreshAccount(String tenantId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(0);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setFindExplicitTotalNum(false);
        stq.setSearchSource("db");

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        Filter budgetStatisticFilter = new Filter();
        budgetStatisticFilter.setFieldName(TPMBudgetAccountFields.BUDGET_STATISTIC_TABLE_ID);
        budgetStatisticFilter.setOperator(Operator.ISN);
        budgetStatisticFilter.setFieldValues(Lists.newArrayList(""));

        Filter usedAmountFilter = new Filter();
        usedAmountFilter.setFieldName(TPMBudgetAccountFields.USED_AMOUNT);
        usedAmountFilter.setOperator(Operator.GT);
        usedAmountFilter.setFieldValues(Lists.newArrayList("0"));

        Filter frozenAmountFilter = new Filter();
        frozenAmountFilter.setFieldName(TPMBudgetAccountFields.FROZEN_AMOUNT);
        frozenAmountFilter.setOperator(Operator.GT);
        frozenAmountFilter.setFieldValues(Lists.newArrayList("0"));

        stq.setFilters(Lists.newArrayList(lifeStatusFilter, budgetStatisticFilter, usedAmountFilter, frozenAmountFilter));
        stq.setPattern(" 1 and 2 and ( 3 or 4 ) ");

        return CommonUtils.queryData(this.serviceFacade, User.systemUser(tenantId), ApiNames.TPM_BUDGET_ACCOUNT, stq);
    }

    private List<String> loadSqlFromResource(String fieldApiName) {
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/shell/%s", fieldApiName));
            return FileUtils.readLines(file, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new MetaDataBusinessException("read field from file cause io exception.");
        }
    }

    private List<IObjectData> queryEnterpriseRelationObj(String tenantId, String templateEa) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");
        query.setLimit(-1);

        IFilter templateEaFilter = new Filter();
        templateEaFilter.setFieldName("template_ea");
        templateEaFilter.setFieldValues(Lists.newArrayList(templateEa));
        templateEaFilter.setOperator(Operator.EQ);

        //1 复制中; 2 复制成功; 3 复制失败
        IFilter copyStatusFilter = new Filter();
        copyStatusFilter.setFieldName("copy_status");
        copyStatusFilter.setFieldValues(Lists.newArrayList("2"));
        copyStatusFilter.setOperator(Operator.EQ);

        query.setFilters(Lists.newArrayList(templateEaFilter, copyStatusFilter));

        List<String> fields = Lists.newArrayList("enterprise_account");
        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.ENTERPRISE_RELATION_OBJ, query, fields);
    }

    @Override
    public String addDescribeField(List<Integer> tenantIds, Map<String, Map<String, Map<String, Object>>> fieldToAddDescribe) {

        Map<Integer, Map<String, String>> resultMap = new HashMap<>();
        for (Integer tenantId : tenantIds) {
            Map<String, String> innerMap = new HashMap<>();
            if (!isActiveEnterprise(tenantId)) {
                log.info("企业已经过期。ei:{}", tenantId);
                Map<String, String> h = new HashMap<>();
                h.put("msg", "企业已经过期");
                resultMap.put(tenantId, h);
                continue;
            }
            if (!dbRouterService.existsPGDBRouter(String.valueOf(tenantId))) {
                log.info("企业没有路由。ei:{}", tenantId);
                Map<String, String> h = new HashMap<>();
                h.put("msg", "企业没有路由");
                resultMap.put(tenantId, h);
                continue;
            }
            User user = User.systemUser(String.valueOf(tenantId));

            fieldToAddDescribe.forEach((objectApiName, fieldsDescribeInfo) -> {
                fieldsDescribeInfo.forEach((fieldApiName, info) -> {
                    JSONObject field = JSON.parseObject(JSON.toJSONString(info.get("describe")));
                    JSONArray layoutList = JSONArray.parseArray(info.get("layoutList").toString());

                    List<FieldLayoutPojo> layoutPojoList = Lists.newArrayList();
                    if (layoutList.isEmpty()) {
                        layoutPojoList.add(getDefaultLayout(user, DETAIL_LAYOUT_TYPE, objectApiName, field, true));
                    } else {
                        layoutPojoList = layoutList.stream().map(o -> JSON.parseObject(o.toString(), FieldLayoutPojo.class)).collect(Collectors.toList());
                    }

                    try {
                        serviceFacade.addDescribeCustomField(user, objectApiName, field.toJSONString(), layoutPojoList, Lists.newArrayList());
                        innerMap.put(objectApiName + "." + fieldApiName, "添加字段成功");
                    } catch (Exception e) {
                        innerMap.put(objectApiName + "." + fieldApiName, "添加字段失败");
                    }
                });
            });
            resultMap.put(tenantId, innerMap);
        }
        return JSON.toJSONString(resultMap);
    }

    @Override
    public String updateFieldDescribe(List<Integer> tenantIds, Map<String, Map<String, Map<String, Object>>> fieldToUpdateDescribe) {

        Map<String, String> resultMap = new HashMap<>();
        for (Integer tenantId : tenantIds) {
            if (!isActiveEnterprise(tenantId)) {
                log.info("企业已经过期。ei:{}", tenantId);
                resultMap.put(String.valueOf(tenantId), "企业已经过期");
                continue;
            }
            if (!dbRouterService.existsPGDBRouter(String.valueOf(tenantId))) {
                log.info("企业没有路由。ei:{}", tenantId);
                resultMap.put(String.valueOf(tenantId), "企业没有路由");
                continue;
            }
            fieldToUpdateDescribe.forEach((objectApiName, fieldsDescribeInfo) -> {
                PaasDescribeGetField.Arg getFieldArg = new PaasDescribeGetField.Arg();
                getFieldArg.setDescribeApiName(objectApiName);
                fieldsDescribeInfo.forEach((fieldApiName, fieldDescribeInfo) -> {
                    getFieldArg.setFieldApiName(fieldApiName);

                    IObjectDescribe orderDescribe = serviceFacade.findObject(String.valueOf(tenantId), objectApiName);
                    IFieldDescribe fieldDescribe = orderDescribe.getFieldDescribe(fieldApiName);

                    Map fieldDescribeMap = JSON.parseObject(JSON.toJSONString(fieldDescribeInfo), Map.class);
                    if (!fieldDescribeMap.isEmpty()) {
                        fieldDescribeMap.forEach((k, v) -> {
                            fieldDescribe.set((String) k, v);
                        });
                    }
                    serviceFacade.updateFieldDescribe(orderDescribe, Lists.newArrayList(fieldDescribe));
                });
            });
        }

        return JSON.toJSONString(resultMap);
    }
}
