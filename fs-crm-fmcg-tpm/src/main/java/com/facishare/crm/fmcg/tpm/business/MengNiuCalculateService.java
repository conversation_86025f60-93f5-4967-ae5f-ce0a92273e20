package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IMengNiuAICalculateService;
import com.facishare.crm.fmcg.tpm.business.dto.*;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.MengNiuCalculateUtils;
import com.facishare.crm.fmcg.tpm.utils.Pair;
import com.facishare.crm.fmcg.tpm.utils.dto.ContinuesFacingDTO;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fs.fmcg.sdk.ai.IDetectClient;
import com.fs.fmcg.sdk.ai.contract.Detect;
import com.fs.fmcg.sdk.ai.contract.ObjectDTO;
import com.fs.fmcg.sdk.ai.contract.SdkContext;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2024/3/4 16:13
 */
//IgnoreI18nFile
@Slf4j
@Service
public class MengNiuCalculateService implements IMengNiuAICalculateService {

    @Resource
    private IDetectClient detectClient;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private DescribeCacheService describeCacheService;

    private static Map<String, String> AI_PICTURE_FIELD_MAP = new HashMap<>();

    private static final String AI_RULE_MESSAGE_TEMPLATE = "活动（%s）:%s";

    private static final Cache<String, Set<String>> PRODUCT_CODE_CACHE = CacheBuilder.newBuilder().maximumSize(200).expireAfterWrite(5, TimeUnit.MINUTES).build();
    private static final Cache<String, Set<String>> PRODUCT_ID_CACHE = CacheBuilder.newBuilder().maximumSize(200).expireAfterWrite(5, TimeUnit.MINUTES).build();

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", conf -> {
            String json = conf.get("ai_picture_field_map");
            if (!Strings.isNullOrEmpty(json)) {
                AI_PICTURE_FIELD_MAP = JSON.parseObject(json, new TypeReference<Map<String, String>>() {
                });
            }
        });
    }


    @Override
    public boolean isFitMengNiuAiRule(String activityTenantId, String activityId, String dataTenantId, String ruleString, String triggerApiName, String triggerDataId, boolean isWriteError) {

        return isFitMengNiuAiRule(serviceFacade.findObjectData(User.systemUser(activityTenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ), dataTenantId, triggerApiName, triggerDataId, isWriteError);
    }

    @Override
    public boolean isFitMengNiuAiRule(IObjectData activity, String dataTenantId, String triggerApiName, String triggerDataId, boolean isWriteError) {
        String ruleString = activity.get("mn_ai_rule__c", String.class);
        log.info("dataTenantId:{},TriggerId:{},activityId:{},activityTenantId:{}", dataTenantId, triggerDataId, activity.getId(), activity.getTenantId());
        IObjectData triggerData = serviceFacade.findObjectData(User.systemUser(dataTenantId), triggerDataId, triggerApiName);
        if (Strings.isNullOrEmpty(ruleString)) {
            log.info("没有ai规则。activityTenantId:{}, tenantId:{},triggerApiName:{},triggerDataId:{}", activity.getTenantId(), dataTenantId, triggerApiName, triggerDataId);
            addAIMathMessage(dataTenantId, triggerData, AIRuleFields.AIMatchStatus.PASS, "没有设置AI特殊规则条件", isWriteError);
            return true;
        }
        AIRewardRuleDTO aiRewardRuleDTO = JSON.parseObject(ruleString, AIRewardRuleDTO.class);
        if (CollectionUtils.isEmpty(aiRewardRuleDTO.getSkuCountRules())
                && CollectionUtils.isEmpty(aiRewardRuleDTO.getContinuesSkuFacingCountRules())
                && CollectionUtils.isEmpty(aiRewardRuleDTO.getSkuFacingRules())) {
            log.info("all is empty true");
            addAIMathMessage(dataTenantId, triggerData, AIRuleFields.AIMatchStatus.PASS, "没有设置AI特殊规则条件", isWriteError);
            return true;
        }
        List<String> nPaths = getPathByObject(triggerData);
        if (CollectionUtils.isEmpty(nPaths)) {
            log.info("没有拍摄ai图片。");
            addAIMathMessage(dataTenantId, triggerData, AIRuleFields.AIMatchStatus.FAIL, "未拍摄AI图片", isWriteError);
            return false;
        }
        int tenantIdIntValue = Integer.parseInt(dataTenantId);
        String tenantAccount = eieaConverter.enterpriseIdToAccount(tenantIdIntValue);
        String modelId = getModelId(activity.getTenantId(), triggerData);
        StringBuilder message = new StringBuilder(String.format("活动（%s）判断AI规则条件不满足。简要信息如下：\n", activity.getName()));
        boolean paas = false;
        for (int i = 0; i < nPaths.size(); i++) {
            String path = nPaths.get(i);
            Pair<Boolean, String> result = dealSingleAIImageRuleValidate(tenantIdIntValue, tenantAccount, modelId, activity, path, aiRewardRuleDTO);
            if (Boolean.TRUE.equals(result.getFirst())) {
                paas = true;
                break;
            }
            message.append(String.format("图片%d（%s）匹配失败： %s \n", i + 1, path, result.getSecond()));
        }
        if (paas) {
            addAIMathMessage(dataTenantId, triggerData, AIRuleFields.AIMatchStatus.PASS, "", isWriteError);
        } else {
            addAIMathMessage(dataTenantId, triggerData, AIRuleFields.AIMatchStatus.FAIL, message.toString(), isWriteError);
        }
        return paas;
    }

    private BigDecimal sumProductsLargeUnitNum(String tenantId, List<IObjectData> orderProducts, Set<String> ruleProductIds) {
        BigDecimal sum = new BigDecimal("0");
        List<IObjectData> ruleProducts = orderProducts.stream().filter(data -> ruleProductIds.contains(data.get(SalesOrderProductFields.PRODUCT_ID, String.class))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ruleProducts)) {
            return sum;
        }
        Map<String, BigDecimal> largeConversionRadioGroupByProductId = getLargeConversionRadioGroupByProductId(tenantId, ruleProducts.stream().map(data -> data.get(SalesOrderProductFields.PRODUCT_ID, String.class)).collect(Collectors.toSet()));


        for (IObjectData ruleProduct : ruleProducts) {
            BigDecimal quantity = ruleProduct.get(SalesOrderProductFields.QUANTITY, BigDecimal.class);
            BigDecimal conversionRadio = ruleProduct.get(SalesOrderProductFields.CONVERSION_RATIO, BigDecimal.class);

            BigDecimal largeConversionRadio = largeConversionRadioGroupByProductId.get(ruleProduct.get(SalesOrderProductFields.PRODUCT_ID, String.class));
            if (Objects.isNull(largeConversionRadio)) {
                log.info("product:[{}],don't have large conversion radio", ruleProduct.get(SalesOrderProductFields.PRODUCT_ID, String.class));
                continue;
            }

            sum = sum.add(quantity.multiply(conversionRadio).divide(largeConversionRadio, 2, RoundingMode.HALF_DOWN));
        }
        log.info("sumIntValue is:{}", sum.intValue());
        return sum;

    }


    @Override
    public boolean isFitMengNiuProductRule(IObjectData activity, String dataTenantId, String triggerApiName, String triggerDataId, boolean isWriteError) {
        if (!ApiNames.SALES_ORDER_OBJ.equals(triggerApiName)) {
            return true;
        }
        String ruleString = activity.get("mn_order_product_rule__c", String.class);
        if (Strings.isNullOrEmpty(ruleString)) {
            log.info("没有订单产品规则。activityTenantId:{}, tenantId:{},triggerApiName:{},triggerDataId:{}", activity.getTenantId(), dataTenantId, triggerApiName, triggerDataId);
            return true;
        }
        OrderProductRewardRuleDTO orderProductRewardRuleDTO = JSON.parseObject(ruleString, OrderProductRewardRuleDTO.class);
        if (Objects.isNull(orderProductRewardRuleDTO)) {
            return true;
        }

        if (CollectionUtils.isEmpty(orderProductRewardRuleDTO.getProductCountRules()) && CollectionUtils.isEmpty(orderProductRewardRuleDTO.getProductAmountRules())) {
            return true;
        }

        List<IObjectData> orderProducts = orderProductsByOrderId(dataTenantId, triggerDataId);
        boolean countMatch = false;
        boolean amountMatch = false;
        if (productCountRuleNotEmpty(orderProductRewardRuleDTO.getProductCountRules())) {
            or:
            for (List<OrderProductCountRuleDTO> productCountRule : orderProductRewardRuleDTO.getProductCountRules()) {

                and:
                for (OrderProductCountRuleDTO orderProductCountRuleDTO : productCountRule) {
                    Set<String> ruleProductIds = getRuleProductIds(activity.getTenantId(), orderProductCountRuleDTO.getProductRanges());
                    BigDecimal matchTotalCount = sumProductsLargeUnitNum(activity.getTenantId(), orderProducts, ruleProductIds);
                    if (matchTotalCount.compareTo(orderProductCountRuleDTO.getCount()) < 0) {
                        continue or;
                    }
                }

                countMatch = true;
                break or;
            }
        } else {
            countMatch = true;
        }


        if (productAmountRuleNotEmpty(orderProductRewardRuleDTO.getProductAmountRules())) {
            or:
            for (List<OrderProductAmountRuleDTO> productAmountRules : orderProductRewardRuleDTO.getProductAmountRules()) {

                and:
                for (OrderProductAmountRuleDTO orderProductAmountRuleDTO : productAmountRules) {
                    Set<String> ruleProductIds = getRuleProductIds(activity.getTenantId(), orderProductAmountRuleDTO.getProductRanges());
                    BigDecimal matchTotalAmount = orderProducts.stream().filter(data -> ruleProductIds.contains(data.get(SalesOrderProductFields.PRODUCT_ID, String.class))).map(data -> data.get(SalesOrderProductFields.PRICE_BOOK_SUBTOTAL, BigDecimal.class, BigDecimal.ZERO))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (matchTotalAmount.compareTo(orderProductAmountRuleDTO.getAmount()) < 0) {
                        continue or;
                    }
                }

                amountMatch = true;
                break or;
            }
        } else {
            amountMatch = true;
        }

        return countMatch && amountMatch;
    }

    private boolean productCountRuleNotEmpty(List<List<OrderProductCountRuleDTO>> productCountRules) {
        boolean notEmpty = false;
        if (CollectionUtils.isNotEmpty(productCountRules)) {
            for (List<OrderProductCountRuleDTO> productCountRule : productCountRules) {
                if (CollectionUtils.isNotEmpty(productCountRule)) {
                    for (OrderProductCountRuleDTO orderProductCountRuleDTO : productCountRule) {
                        if (Objects.nonNull(orderProductCountRuleDTO)) {
                            notEmpty = true;
                            break;
                        }
                    }
                }
            }
        }
        return notEmpty;
    }

    private boolean productAmountRuleNotEmpty(List<List<OrderProductAmountRuleDTO>> productAmountRules) {
        boolean notEmpty = false;
        if (CollectionUtils.isNotEmpty(productAmountRules)) {
            for (List<OrderProductAmountRuleDTO> productAmountRule : productAmountRules) {
                if (CollectionUtils.isNotEmpty(productAmountRule)) {
                    for (OrderProductAmountRuleDTO orderProductAmountRuleDTO : productAmountRule) {
                        if (Objects.nonNull(orderProductAmountRuleDTO)) {
                            notEmpty = true;
                            break;
                        }
                    }
                }
            }
        }
        return notEmpty;
    }

    private Pair<Boolean, String> dealSingleAIImageRuleValidate(Integer tenantIdIntValue, String tenantAccount, String modelId, IObjectData activity, String path, AIRewardRuleDTO aiRewardRuleDTO) {
        String traceId = TraceContext.get().getTraceId();
        SdkContext sdkContext = SdkContext.createInstance(tenantIdIntValue, tenantAccount, -10000);
        Detect.Arg detectArg = new Detect.Arg();
        detectArg.setPath(path);
        detectArg.setScene("new_number_form");
        detectArg.setModelId(modelId);
        detectArg.setMustRecord(ConfigFactory.getConfig("fs-fmcg-tpm-config").getBool("ai_must_record", true));
        detectArg.setExtraData("{\"enable_recapture_classify\":true}");
        Detect.Result detectResult = detectClient.detect(sdkContext, detectArg);
        TraceContext.get().setTraceId(traceId);
        if (!Strings.isNullOrEmpty(detectResult.getErrorMessage())) {
            throw new ValidateException("get detect result error：" + detectResult.getErrorMessage());
        }
        if (Boolean.TRUE.equals(detectResult.getRecapture())) {
            return new Pair<>(false, "图片疑似翻拍。");
        }
        String baseUnitType = activity.get("detect_unit__c", String.class);
        //按单位过滤
        if (!Strings.isNullOrEmpty(baseUnitType)) {
            detectResult.getData().getObjectList().removeIf(objectDTO -> {
                if (MapUtils.isNotEmpty(objectDTO.getExtraData())) {
                    String unitType = objectDTO.getExtraData().getString("unit_type");
                    return !baseUnitType.equals(unitType);
                }
                return true;
            });
        }

        List<ObjectDTO> skuInfos = getSkuInfo(String.valueOf(tenantIdIntValue), detectResult.getData().getObjectList());
        log.info("skuInfo:{}", skuInfos);
        if (CollectionUtils.isNotEmpty(aiRewardRuleDTO.getSkuCountRules())) {
            for (SkuCountRuleDTO rule : aiRewardRuleDTO.getSkuCountRules()) {
                Set<String> productSkuCodes = getProductSkuCodes(activity.getTenantId(), rule.getSkuRanges());
                long count = skuInfos.stream().map(v -> (String) v.getExtraData().get(ProductFields.PRODUCT_CODE)).distinct().filter(productSkuCodes::contains).count();
                if (count < rule.getCount()) {
                    log.info("sku总：{},skuRange:{},skuCodes:{}", count, rule.getSkuRanges(), productSkuCodes);
                    return new Pair<>(false, "sku总数条件不满足。");
                }
            }
        }
        Map<String, List<ContinuesFacingDTO>> continuesSkuInfoMap = null;
        if (CollectionUtils.isNotEmpty(aiRewardRuleDTO.getSkuFacingRules())) {
            continuesSkuInfoMap = MengNiuCalculateUtils.getContinuesFacingByLayer(skuInfos);
            List<String> productCodeInDetectList = continuesSkuInfoMap.values().stream()
                    .flatMap(List::stream).map(ContinuesFacingDTO::getSkuInfos).flatMap(List::stream)
                    .map(v -> (String) v.getExtraData().get(ProductFields.PRODUCT_CODE)).collect(Collectors.toList());
            for (SkuFacingRuleDTO rule : aiRewardRuleDTO.getSkuFacingRules()) {
                Set<String> productSkuCodes = getProductSkuCodes(activity.getTenantId(), rule.getSkuRanges());
                long count = productCodeInDetectList.stream().filter(productSkuCodes::contains).count();
                if (count < rule.getCount()) {
                    log.info("sku排面：{},skuRange:{},skuCodes:{},productCodeInDetectList：{}", count, rule.getSkuRanges(), productSkuCodes, productCodeInDetectList);
                    return new Pair<>(false, "sku排面条件不满足。");
                }
            }
        }
        if (CollectionUtils.isNotEmpty(aiRewardRuleDTO.getContinuesSkuFacingCountRules())) {
            continuesSkuInfoMap = continuesSkuInfoMap == null ? MengNiuCalculateUtils.getContinuesFacingByLayer(skuInfos) : continuesSkuInfoMap;
            for (ContinuesSkuFacingCountRuleDTO rule : aiRewardRuleDTO.getContinuesSkuFacingCountRules()) {
                if (!isSatisfied(activity.getTenantId(), continuesSkuInfoMap, rule)) {
                    return new Pair<>(false, "连续面条件不满足。");
                }
            }
        }
        return new Pair<>(true, "OK");
    }


    private void addAIMathMessage(String tenantId, IObjectData data, String status, String message, boolean isWriteError) {
        if (isWriteError) {
            ParallelUtils.createParallelTask().submit(() -> {
                addField(tenantId, data, AIRuleFields.AI_MATCH_RESULT, "{\"type\":\"long_text\",\"define_type\":\"custom\",\"api_name\":\"ai_match_result__c\",\"label\":\"AI规则匹配详情\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":2000,\"min_length\":0,\"is_extend\":true}");
                addField(tenantId, data, AIRuleFields.AI_MATCH_STATUS, "{\"type\":\"select_one\",\"define_type\":\"custom\",\"api_name\":\"ai_match_status__c\",\"label\":\"AI规则匹配结果\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"options\":[{\"font_color\":\"#54f400\",\"label\":\"合格\",\"value\":\"1\"},{\"label\":\"不合格\",\"value\":\"0\",\"font_color\":\"#f53500\"},{\"label\":\"其他\",\"value\":\"other\",\"not_usable\":true}],\"is_extend\":true}");

                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(AIRuleFields.AI_MATCH_RESULT, message);
                updateMap.put(AIRuleFields.AI_MATCH_STATUS, status);
                serviceFacade.updateWithMap(User.systemUser(tenantId), data, updateMap);
            }).run();
        }
    }

    private void addField(String tenantId, IObjectData data, String fieldApiName, String fieldDescribe) {
        if (!describeCacheService.isExistField(tenantId, data.getDescribeApiName(), fieldApiName)) {
            try {
                serviceFacade.addDescribeCustomField(User.systemUser(tenantId), data.getDescribeApiName(), fieldDescribe, null, null);
            } catch (Exception e) {
                log.error("addDescribeCustomField error", e);
            }
        }
    }

    private String getModelId(String tenantId, IObjectData triggerData) {
        String modelId = triggerData.get("model_id__c", String.class);
        if (!Strings.isNullOrEmpty(modelId)) {
            return modelId;
        }
        String modelJson = ConfigFactory.getConfig("fs-fmcg-tpm-config").get("MN_AI_MODEL", "{}");
        JSONObject map = JSON.parseObject(modelJson);
        modelId = map.getString(tenantId);
        if (Strings.isNullOrEmpty(modelId)) {
            throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_0));
        }
        return modelId;
    }

    private boolean isSatisfied(String tenantId, Map<String, List<ContinuesFacingDTO>> continuesSkuInfoMap, ContinuesSkuFacingCountRuleDTO rule) {
        for (List<ContinuesFacingDTO> continuesFacingList : continuesSkuInfoMap.values()) {
            for (ContinuesFacingDTO continuesFacing : continuesFacingList) {
                if (continuesFacing.getSkuInfos().size() < rule.getFocusSkuCount()) {
                    continue;
                }
                boolean isFitRatio = true;
                for (ContinuesSkuFacingCountRuleDTO.SkuFacingRatioDTO skuFacingRatio : rule.getSkuFacingRatios()) {
                    Set<String> productCodeSet = getProductSkuCodes(tenantId, skuFacingRatio.getSkuRanges());
                    long count = continuesFacing.getSkuInfos().stream().filter(skuInfo -> productCodeSet.contains(skuInfo.getExtraData().getString(ProductFields.PRODUCT_CODE))).count();
                    double ratio = count * 100.0 / continuesFacing.getSkuInfos().size();
                    if (ratio < skuFacingRatio.getCount()) {
                        isFitRatio = false;
                        break;
                    }
                    log.info("fit ratio:{},count:{},totalCount:{}", ratio, count, continuesFacing.getSkuInfos().size());
                }
                if (isFitRatio) {
                    return true;
                }
            }
        }
        return false;
    }

    private List<IObjectData> orderProductsByOrderId(String tenantId, String orderId) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        IFilter filter = new Filter();
        filter.setFieldName(SalesOrderProductFields.ORDER_ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(orderId));

        query.setFilters(Lists.newArrayList(filter));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.SALES_ORDER_PRODUCT_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID,
                        SalesOrderProductFields.CONVERSION_RATIO,
                        SalesOrderProductFields.PRICE_BOOK_SUBTOTAL,
                        SalesOrderProductFields.PRODUCT_ID,
                        SalesOrderProductFields.QUANTITY
                ),
                true
        );
    }

    private Map<String, BigDecimal> getLargeConversionRadioGroupByProductId(String tenantId, Set<String> productIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        IFilter filter = new Filter();
        filter.setFieldName(MultiUnitRelatedObjFields.PRODUCT_ID);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList(productIds));


        IFilter unitTypeLarge = new Filter();
        unitTypeLarge.setFieldName(MultiUnitRelatedObjFields.UNIT_TYPE);
        unitTypeLarge.setOperator(Operator.EQ);
        unitTypeLarge.setFieldValues(Lists.newArrayList(MultiUnitRelatedObjFields.UNIT_TYPE__LARGE));

        query.setFilters(Lists.newArrayList(filter, unitTypeLarge));
        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.MULTI_UNIT_RELATED_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID,
                        MultiUnitRelatedObjFields.PRODUCT_ID,
                        MultiUnitRelatedObjFields.CONVERSION_RATIO

                ),
                true
        );
        return data.stream().collect(Collectors.toMap(k -> k.get(MultiUnitRelatedObjFields.PRODUCT_ID, String.class),
                v -> v.get(MultiUnitRelatedObjFields.CONVERSION_RATIO, BigDecimal.class),
                (oldData, newData) -> oldData));
    }

    private Set<String> getProductSkuCodes(String tenantId, List<SimpleObjectDTO> productRanges) {
        Set<String> productCodeSet = new HashSet<>();
        productRanges.forEach(productRange -> {
            String apiName = Strings.isNullOrEmpty(productRange.getApiName()) ? "ProductGroupDetailObj__c" : productRange.getApiName();
            String key = String.format("%s_%s_%s", tenantId, apiName, productRange.getId());
            Set<String> productCodeInCache = PRODUCT_CODE_CACHE.getIfPresent(key);
            if (productCodeInCache == null) {
                synchronized (key.intern()) {
                    productCodeInCache = PRODUCT_CODE_CACHE.getIfPresent(key);
                    if (productCodeInCache == null) {
                        productCodeInCache = getProductSkuCodeByProductRangeDetail(tenantId, apiName, productRange.getId());
                        PRODUCT_CODE_CACHE.put(key, productCodeInCache);
                    }
                }
            }
            productCodeSet.addAll(productCodeInCache);
        });
        return productCodeSet;
    }

    private Set<String> getProductSkuCodeByProductRangeDetail(String tenantId, String apiName, String productRangeId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1,
                Lists.newArrayList(SearchQueryUtil.filter("master_id__c", Operator.EQ, Lists.newArrayList(productRangeId))));
        Set<String> productCodeSet = new HashSet<>();
        QueryDataUtil.findAndConsume(serviceFacade, User.systemUser(tenantId), "ProductGroupDetailObj__c", query, Lists.newArrayList("product_id__c", "product_code__c"), data -> {
            data.stream().map(item -> item.get("product_code__c", String.class)).filter(Objects::nonNull).forEach(productCodeSet::add);
        });
        return productCodeSet;
    }

    private Set<String> getRuleProductIds(String tenantId, List<SimpleObjectDTO> productRanges) {
        List<String> allProductIds = Lists.newArrayList();
        productRanges.forEach(data -> {
            String apiName = data.getApiName();
            String id = data.getId();
            String cacheKey = String.format("%s_%s_%s", tenantId, apiName, id);
            Set<String> productIds = PRODUCT_ID_CACHE.getIfPresent(cacheKey);
            if (CollectionUtils.isEmpty(productIds)) {
                synchronized (cacheKey.intern()) {
                    productIds = PRODUCT_ID_CACHE.getIfPresent(cacheKey);
                    if (CollectionUtils.isEmpty(productIds)) {
                        productIds = getProductIdsByProductRangeDetail(tenantId, apiName, id);
                        PRODUCT_ID_CACHE.put(cacheKey, productIds);
                    }
                }
            }
            allProductIds.addAll(productIds);
        });
        return new HashSet<>(allProductIds);
    }

    private Set<String> getProductIdsByProductRangeDetail(String tenantId, String apiName, String productRangeId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1,
                Lists.newArrayList(SearchQueryUtil.filter("master_id__c", Operator.EQ, Lists.newArrayList(productRangeId))));
        Set<String> productIdSet = new HashSet<>();
        QueryDataUtil.findAndConsume(serviceFacade, User.systemUser(tenantId), "ProductGroupDetailObj__c", query, Lists.newArrayList("product_id__c", "product_code__c"), data -> {
            data.stream().map(item -> item.get("product_id__c", String.class)).filter(Objects::nonNull).forEach(productIdSet::add);
        });
        return productIdSet;
    }

    private List<ObjectDTO> getSkuInfo(String tenantId, List<ObjectDTO> objectDTOS) {
        objectDTOS.removeIf(objectDTO -> !ApiNames.PRODUCT_OBJ.equals(objectDTO.getApiName()));
        List<ObjectDTO> noSkuCodeData = objectDTOS.stream().filter(v -> v.getExtraData() == null || v.getExtraData().get(ProductFields.PRODUCT_CODE) == null).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(noSkuCodeData)) {
            Map<String, String> productCodeMap = getProductCodeMap(tenantId, objectDTOS.stream().map(ObjectDTO::getDataId).collect(Collectors.toList()));
            noSkuCodeData.forEach(objectDTO -> {
                String productCode = productCodeMap.get(objectDTO.getDataId());
                if (Strings.isNullOrEmpty(productCode)) {
                    log.info("product is null:{}", objectDTO);
                    return;
                }
                if (objectDTO.getExtraData() == null) {
                    objectDTO.setExtraData(new JSONObject());
                }
                objectDTO.getExtraData().put(ProductFields.PRODUCT_CODE, productCode);
            });
        }
        return objectDTOS;
    }

    private Map<String, String> getProductCodeMap(String tenantId, List<String> productIds) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(CommonFields.ID, Operator.IN, productIds)
        ));
        Map<String, String> productCodeMap = new HashMap<>();
        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.PRODUCT_OBJ, query, Lists.newArrayList(ProductFields.PRODUCT_CODE));
        data.forEach(obj -> productCodeMap.put(obj.getId(), obj.get(ProductFields.PRODUCT_CODE, String.class)));
        return productCodeMap;
    }


    private boolean isDigest(String number) {
        for (int i = 0; i < number.length(); i++) {
            if (!Character.isDigit(number.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    private List<String> getPathByObject(IObjectData objectData) {
        String field = "ai_paths";
        if (objectData.getDescribeApiName().equals("PurchaseReportingObj")) {
            field = "field_Uyb2Q__c";
        }
        if (AI_PICTURE_FIELD_MAP.containsKey(objectData.getDescribeApiName())) {
            field = AI_PICTURE_FIELD_MAP.get(objectData.getDescribeApiName());
        }
        log.info("field:{}", field);
        return getPathList(objectData, field);
    }

    private List<String> getPathList(IObjectData object, String imageField) {
        List<Map> jsonArray = object.get(imageField, List.class, new ArrayList());
        List<String> pathList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(jsonArray)) {
            jsonArray.forEach(map -> pathList.add(map.get("path").toString()));
        }
        return pathList;
    }

    @Override
    public void validateAIRewardRule(String ruleString) {
        if (Strings.isNullOrEmpty(ruleString)) {
            return;
        }
        AIRewardRuleDTO aiRewardRuleDTO = JSON.parseObject(ruleString, AIRewardRuleDTO.class);
        if (CollectionUtils.isNotEmpty(aiRewardRuleDTO.getSkuCountRules())) {
            aiRewardRuleDTO.getSkuCountRules().forEach(skuCountRuleDTO -> {
                if (skuCountRuleDTO.getCount() == null) {
                    throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_1));
                }
                if (CollectionUtils.isEmpty(skuCountRuleDTO.getSkuRanges())) {
                    throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_2));
                }
            });
        }

        if (CollectionUtils.isNotEmpty(aiRewardRuleDTO.getSkuFacingRules())) {
            aiRewardRuleDTO.getSkuFacingRules().forEach(skuFacingRuleDTO -> {
                if (skuFacingRuleDTO.getCount() == null) {
                    throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_3));
                }
                if (CollectionUtils.isEmpty(skuFacingRuleDTO.getSkuRanges())) {
                    throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_4));
                }
            });
        }
        if (CollectionUtils.isNotEmpty(aiRewardRuleDTO.getContinuesSkuFacingCountRules())) {
            aiRewardRuleDTO.getContinuesSkuFacingCountRules().forEach(continuesSkuFacingCountRuleDTO -> {
                if (continuesSkuFacingCountRuleDTO.getFocusSkuCount() == null) {
                    throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_5));
                }
                if (CollectionUtils.isNotEmpty(continuesSkuFacingCountRuleDTO.getSkuFacingRatios())) {
                    continuesSkuFacingCountRuleDTO.getSkuFacingRatios().forEach(skuFacingRatioDTO -> {
                        if (skuFacingRatioDTO.getCount() == null) {
                            throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_6));
                        }
                        if (CollectionUtils.isEmpty(skuFacingRatioDTO.getSkuRanges())) {
                            throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_7));
                        }
                    });
                }
            });
        }
    }

    @Override
    public void validateOrderProductRule(String ruleString) {
        if (StringUtils.isEmpty(ruleString)) {
            return;
        }
        OrderProductRewardRuleDTO orderProductRewardRuleDTO = JSON.parseObject(ruleString, OrderProductRewardRuleDTO.class);
        if (orderProductRewardRuleDTO == null) {
            return;
        }
        if (productCountRuleNotEmpty(orderProductRewardRuleDTO.getProductCountRules())) {
            for (List<OrderProductCountRuleDTO> productCountRule : orderProductRewardRuleDTO.getProductCountRules()) {
                for (OrderProductCountRuleDTO orderProductCountRuleDTO : productCountRule) {
                    if (orderProductCountRuleDTO.getCount() == null) {
                        throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_8));
                    }
                    if (CollectionUtils.isEmpty(orderProductCountRuleDTO.getProductRanges())) {
                        throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_9));
                    }
                }
            }
        }

        if (productAmountRuleNotEmpty(orderProductRewardRuleDTO.getProductAmountRules())) {
            for (List<OrderProductAmountRuleDTO> productAmountRule : orderProductRewardRuleDTO.getProductAmountRules()) {

                for (OrderProductAmountRuleDTO orderProductAmountRuleDTO : productAmountRule) {
                    if (Objects.isNull(orderProductAmountRuleDTO.getAmount())) {
                        throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_10));
                    }
                    if (CollectionUtils.isEmpty(orderProductAmountRuleDTO.getProductRanges())) {
                        throw new ValidateException(I18N.text(I18NKeys.MENG_NIU_CALCULATE_SERVICE_11));
                    }
                }
            }
        }


    }
}
