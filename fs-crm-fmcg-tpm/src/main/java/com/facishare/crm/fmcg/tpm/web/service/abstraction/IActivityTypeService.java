package com.facishare.crm.fmcg.tpm.web.service.abstraction;

import com.facishare.crm.fmcg.tpm.web.contract.*;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/24 18:59
 */
public interface IActivityTypeService {

    SetActivityTypeStatus.Result setStatus(SetActivityTypeStatus.Arg arg);

    AddActivityType.Result add(AddActivityType.Arg arg);

    EditActivityType.Result edit(EditActivityType.Arg arg);

    DeleteActivityType.Result delete(DeleteActivityType.Arg arg);

    GetActivityType.Result get(GetActivityType.Arg arg);

    ListActivityType.Result list(ListActivityType.Arg arg);

    ValidActivityType.Result validActivityType(ValidActivityType.Arg arg);

    LoadDesignerConfig.Result loadDesignerConfig(LoadDesignerConfig.Arg arg);

    DataActivityType.Result dataActivityType(DataActivityType.Arg arg);

    FindActivityData.Result findActivityData(FindActivityData.Arg arg);

    ValidActivityDateByRecordType.Result validActivityDateByRecordType(ValidActivityDateByRecordType.Arg arg);

    TPMPermission.Result permission();

    QueryActivityTypeByModelAndRule.Result queryByModelAndRule(QueryActivityTypeByModelAndRule.Arg arg);

    ListActivityTypeForProofAi.Result listForPoofAi(ListActivityTypeForProofAi.Arg arg);
}
