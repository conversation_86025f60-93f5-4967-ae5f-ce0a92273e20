package com.facishare.crm.fmcg.tpm.web.service;

import com.facishare.crm.fmcg.common.apiname.ApiNameUtil;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.IRoleService;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetTypeVO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetAccrualRuleManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetConsumeRuleManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IBudgetTypeService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@SuppressWarnings("Duplicates")
public class BudgetTypeService extends BaseService implements IBudgetTypeService {

    public static final int ALLOW = 1;
    protected static final Map<String, String> PERMISSION_KEY_MAP = Maps.newHashMap();
    protected static final Set<String> SKIP_PERMISSION_SCENE = Sets.newHashSet();

    static {
        PERMISSION_KEY_MAP.put(ApiNames.TPM_BUDGET_ACCOUNT, "ALL");
        PERMISSION_KEY_MAP.put(ApiNames.TPM_BUDGET_CARRY_FORWARD, "ALL");
        PERMISSION_KEY_MAP.put(ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, "ALL");
        PERMISSION_KEY_MAP.put(ApiNames.TPM_BUDGET_TRANSFER_DETAIL, "ALL");

        SKIP_PERMISSION_SCENE.add("LIST");
        SKIP_PERMISSION_SCENE.add("CONSUME_RULE");
    }

    @Resource
    private BudgetTypeDAO budgetTypeDAO;
    @Resource
    private IBudgetTypeManager budgetTypeManager;
    @Resource
    private IBudgetConsumeRuleManager budgetConsumeRuleManager;
    @Resource
    private IBudgetAccrualRuleManager budgetAccrualRuleManager;

    @Resource(name = "tpmOrganizationService")
    private OrganizationService organizationService;
    @Resource(name = "tpmRoleService")
    private IRoleService roleService;

    @Override
    public AddBudgetType.Result add(AddBudgetType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        Function<String, Boolean> isDuplicateFunc = (String code) -> budgetTypeDAO.isDuplicateApiName(context.getTenantId(), code);
        if (Strings.isNullOrEmpty(arg.getBudgetType().getApiName())) {
            arg.getBudgetType().setApiName(ApiNameUtil.getBudgetTypeApiName(isDuplicateFunc));
        } else {
            boolean isDuplicate = isDuplicateFunc.apply(arg.getBudgetType().getApiName());
            if (isDuplicate) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_API_NAME_CONFLICT_ERROR));
            }
        }

        if (budgetTypeDAO.count(context.getTenantId(), "") >= Integer.MAX_VALUE) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_INSUFFICIENT_QUOTA_ERROR));
        }

        int maximumLevel = budgetTypeManager.maximumDepartmentLevel(context.getTenantId(), arg.getBudgetType().getDepartmentRange());
        if (arg.getBudgetType().getRootNode().getDepartmentDimensionLevel() < maximumLevel) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DEPARTMENT_CANNOT_HIGHER_DEPARTMENT_LEVEL_BY_BUDGET_TYPE));
        }

        List<BudgetTypeNodeEntity> nodes = budgetTypeManager.convertToNodesWhenUnused(context.getTenantId(), arg.getBudgetType().getRootNode());
        BudgetTypePO po = BudgetTypePO.fromAddVO(arg.getBudgetType(), nodes);
        budgetTypeManager.basicInformationValidation(context.getTenantId(), po);

        String id = budgetTypeDAO.add(context.getTenantId(), context.getEmployeeId(), po);

        budgetTypeManager.publishSyncBudgetTypeFieldTask(context.getTenantId());
        budgetTypeManager.publishSyncBudgetNodeFieldTask(context.getTenantId());
        budgetTypeManager.publishSyncCreateBudgetTypeReference(context.getTenantId(), po);

        asyncBudgetTypeLog(context.getTenantId(), context.getEmployeeId(), po);
        return AddBudgetType.Result.builder().budgetType(loadFullBudgetTypeVO(context.getTenantId(), id)).build();
    }

    private void asyncBudgetTypeLog(String tenantId, int userId, BudgetTypePO po) {
        for (int i = 0; i < po.getNodes().size(); i++) {
            BuryService.asyncBudgetLog(tenantId, userId, String.format(BuryModule.Budget.BUDGET_TYPE_LABEL, i + 1), BuryOperation.CREATE);
        }
    }

    private BudgetTypeVO loadFullBudgetTypeVO(String tenantId, String id) {
        BudgetTypePO po = budgetTypeDAO.get(tenantId, id);
        BudgetTypeVO vo = BudgetTypePO.toVO(po);

        if (Objects.isNull(vo)) {
            throw new MetaDataBusinessException("budget type not found.");
        }

        vo.setUsed(budgetTypeManager.isUsed(tenantId, id));
        vo.setRootNode(budgetTypeManager.convertToRootNodeVO(po.getNodes()));
        return vo;
    }

    @Override
    public DeleteBudgetType.Result delete(DeleteBudgetType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        boolean used = budgetTypeManager.isUsed(context.getTenantId(), arg.getId());
        if (used) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_USED_BY_BUDGET_ACCOUNT_ERROR));
        }

        innerDelete(context.getTenantId(), context.getEmployeeId(), arg.getId());
        return DeleteBudgetType.Result.builder().build();
    }

    @Override
    public ForceDeleteBudgetType.Result forceDelete(ForceDeleteBudgetType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);
        if (!TPMGrayUtils.isBudgetTypeEnableForceDelete(context.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_USED_BY_BUDGET_ACCOUNT_ERROR));
        }
        ParallelUtils.createParallelTask()
                .submit(MonitorTaskWrapper.wrap(() -> innerDelete(context.getTenantId(), context.getEmployeeId(), arg.getId())))
                .run();
        return ForceDeleteBudgetType.Result.builder().build();
    }

    private void innerDelete(String tenantId, int employeeId, String id) {
        if (budgetTypeManager.isUsed(tenantId, id)) {
            budgetTypeManager.deleteObjectDataByTypeId(tenantId, id);
            budgetConsumeRuleManager.deleteConsumeRuleByTypeId(tenantId, employeeId, id);
            budgetAccrualRuleManager.deleteAccrualRuleByTypeId(tenantId, employeeId, id);
        }
        budgetTypeManager.delete(tenantId, employeeId, id);
    }

    @Override
    public EditBudgetType.Result edit(EditBudgetType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        BudgetTypePO old = budgetTypeDAO.get(context.getTenantId(), arg.getBudgetType().getId());

        if (old == null) {
            throw new ValidateException("budget type not found.");
        }

        if (old.getVersion() != arg.getBudgetType().getVersion()) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_VERSION_VALIDATION_ERROR));
        }

        boolean used = budgetTypeManager.isUsed(context.getTenantId(), arg.getBudgetType().getId());

        List<BudgetTypeNodeEntity> nodes;
        if (used) {
            budgetTypeManager.editDepartmentRangeValidate(context.getTenantId(), arg.getBudgetType().getDepartmentRange(), old.getDepartmentRange());
            nodes = budgetTypeManager.convertToNodesWhenUsed(arg.getBudgetType().getRootNode(), old.getNodes());
        } else {
            nodes = budgetTypeManager.convertToNodesWhenUnused(context.getTenantId(), arg.getBudgetType().getRootNode());
        }

        BudgetTypePO po = BudgetTypePO.fromEditVO(old, arg.getBudgetType(), nodes);
        budgetTypeManager.basicInformationValidation(context.getTenantId(), po);
        budgetTypeDAO.edit(context.getTenantId(), context.getEmployeeId(), po.getId().toString(), po);

        budgetTypeManager.publishSyncBudgetTypeFieldTask(context.getTenantId());
        budgetTypeManager.publishSyncBudgetNodeFieldTask(context.getTenantId());
        budgetTypeManager.publishSyncEditBudgetTypeReference(context.getTenantId(), old, po);

        return EditBudgetType.Result.builder()
                .budgetType(loadFullBudgetTypeVO(context.getTenantId(), arg.getBudgetType().getId()))
                .build();
    }

    @Override
    public SetBudgetTypeStatus.Result setStatus(SetBudgetTypeStatus.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        if (Arrays.stream(StatusType.values()).noneMatch(n -> n.value().equals(arg.getStatus()))) {
            throw new ValidateException("status value not exists.");
        }

        budgetTypeDAO.setStatus(context.getTenantId(), context.getEmployeeId(), arg.getId(), arg.getStatus());
        return SetBudgetTypeStatus.Result.builder()
                .budgetType(loadFullBudgetTypeVO(context.getTenantId(), arg.getId()))
                .build();
    }

    @Override
    public GetBudgetType.Result get(GetBudgetType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        return GetBudgetType.Result.builder().budgetType(loadFullBudgetTypeVO(context.getTenantId(), arg.getId())).build();
    }

    @Override
    public ListBudgetType.Result list(ListBudgetType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        List<BudgetTypePO> types = budgetTypeDAO.list(context.getTenantId(), arg.getKeyword());
        if (CollectionUtils.isEmpty(types)){
            //兼容翻译工作台
            return ListBudgetType.Result.builder().data(Lists.newArrayList()).enableForceDelete(false).build();
        }

        fillDimensionLabels(context.getTenantId(), types);
        types = permissionFilter(context.getTenantId(), context.getEmployeeId(), arg.getScene(), types);

        List<BudgetTypeVO> data = types.stream().map(m -> {
            BudgetTypeVO vo = BudgetTypePO.toVO(m);
            if (!Objects.isNull(vo)) {
                vo.setRootNode(budgetTypeManager.convertToRootNodeVO(m.getNodes()));
                vo.setUsed(budgetTypeManager.isUsed(context.getTenantId(), vo.getId()));
            }
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        return ListBudgetType.Result.builder()
                .data(data)
                .enableForceDelete(TPMGrayUtils.isBudgetTypeEnableForceDelete(context.getTenantId()))
                .build();
    }

    @Override
    public MaximumDepartmentLevel.Result maximumDepartmentLevel(MaximumDepartmentLevel.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        int level = budgetTypeManager.maximumDepartmentLevel(context.getTenantId(), arg.getDepartmentIds());
        return MaximumDepartmentLevel.Result.builder().level(level).build();
    }

    private void fillDimensionLabels(String tenantId, List<BudgetTypePO> types) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_BUDGET_ACCOUNT);
        Map<String, IFieldDescribe> fieldMap = describe.getFieldDescribeMap();
        for (BudgetTypePO type : types) {
            for (BudgetTypeNodeEntity node : type.getNodes()) {
                for (BudgetDimensionEntity dimension : node.getDimensions()) {
                    if (fieldMap.containsKey(dimension.getApiName())) {
                        dimension.setLabel(fieldMap.get(dimension.getApiName()).getLabel());
                    } else {
                        dimension.setLabel("--");
                    }
                }
            }
        }
    }

    private List<BudgetTypePO> permissionFilter(String tenantId, int userId, String scene, List<BudgetTypePO> types) {
        if (SKIP_PERMISSION_SCENE.contains(scene)) {
            return types;
        }

        List<Integer> departmentIds = organizationService.getDepartmentIds(Integer.parseInt(tenantId), userId);
        List<String> roleIds = roleService.queryRoleByEmployeeId(Integer.parseInt(tenantId), userId);

        switch (scene) {
            case ApiNames.TPM_BUDGET_ACCOUNT:
                fillBudgetAccountPermission(scene, userId, departmentIds, roleIds, types);
                break;
            case ApiNames.TPM_BUDGET_CARRY_FORWARD:
                fillBudgetCarryForwardPermission(scene, userId, departmentIds, roleIds, types);
                break;
            case ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ:
                fillBudgetDisassemblyPermission(scene, userId, departmentIds, roleIds, types);
                break;
            case ApiNames.TPM_BUDGET_TRANSFER_DETAIL:
                fillBudgetTransferPermission(scene, userId, departmentIds, roleIds, types);
                break;
            default:
                break;
        }
        if (Strings.isNullOrEmpty(scene)) {
            return types;
        }
        return types.stream().filter(f -> f.getNodes().stream().anyMatch(a -> a.getPermission() == ALLOW)).collect(Collectors.toList());
    }

    private void fillBudgetAccountPermission(String scene, int employeeId, List<Integer> departmentIds, List<String> roleIds, List<BudgetTypePO> types) {
        for (BudgetTypePO po : types) {
            for (BudgetTypeNodeEntity node : po.getNodes()) {
                if (Strings.isNullOrEmpty(node.getParentNodeId()) && nodePermissionCheck(scene, employeeId, departmentIds, roleIds, node)) {
                    node.setPermission(ALLOW);
                }
            }
        }
    }

    private void fillBudgetCarryForwardPermission(String scene, int employeeId, List<Integer> departmentIds, List<String> roleIds, List<BudgetTypePO> types) {
        for (BudgetTypePO po : types) {
            for (BudgetTypeNodeEntity node : po.getNodes()) {
                if (node.isEnableCarryForward() && nodePermissionCheck(scene, employeeId, departmentIds, roleIds, node)) {
                    node.setPermission(ALLOW);
                }
            }
        }
    }

    private void fillBudgetDisassemblyPermission(String scene, int employeeId, List<Integer> departmentIds, List<String> roleIds, List<BudgetTypePO> types) {
        for (BudgetTypePO po : types) {
            for (BudgetTypeNodeEntity node : po.getNodes()) {
                BudgetTypeNodeEntity sub = po.getNodes().stream().filter(f -> node.getNodeId().equals(f.getParentNodeId())).findFirst().orElse(null);
                if (Objects.nonNull(sub) && nodePermissionCheck(scene, employeeId, departmentIds, roleIds, sub)) {
                    node.setPermission(ALLOW);
                }
            }
        }
    }

    private void fillBudgetTransferPermission(String scene, int employeeId, List<Integer> departmentIds, List<String> roleIds, List<BudgetTypePO> types) {
        for (BudgetTypePO po : types) {
            for (BudgetTypeNodeEntity node : po.getNodes()) {
                if (nodePermissionCheck(scene, employeeId, departmentIds, roleIds, node)) {
                    node.setPermission(ALLOW);
                }
            }
        }
    }

    private boolean nodePermissionCheck(String scene, int employeeId, List<Integer> departmentIds, List<String> roleIds, BudgetTypeNodeEntity node) {
        String permissionKey = PERMISSION_KEY_MAP.get(scene);
        ScopeEntity scope = node.getPermissions().get(permissionKey);

        if (Objects.isNull(scope)) {
            return false;
        }

        if (CollectionUtils.isNotEmpty(scope.getEmployeeIds()) && scope.getEmployeeIds().contains(employeeId)) {
            return true;
        }

        if (CollectionUtils.isNotEmpty(scope.getDepartmentIds())) {
            for (Integer departmentId : departmentIds) {
                if (scope.getDepartmentIds().contains(departmentId)) {
                    return true;
                }
            }
        }

        if (CollectionUtils.isNotEmpty(scope.getRoleIds())) {
            for (String roleId : roleIds) {
                if (scope.getRoleIds().contains(roleId)) {
                    return true;
                }
            }
        }

        return false;
    }
}