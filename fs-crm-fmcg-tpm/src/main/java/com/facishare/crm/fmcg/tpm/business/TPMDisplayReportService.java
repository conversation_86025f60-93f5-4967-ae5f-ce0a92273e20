package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.AbstractDisPlayReportBaseService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDisplayReportValidator;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPMDisplayReportService;
import com.facishare.crm.fmcg.tpm.business.enums.DetectCapabilityEnum;
import com.facishare.crm.fmcg.tpm.business.enums.DisplayFormItemEnum;
import com.facishare.crm.fmcg.tpm.business.enums.RIOSpecialDisplayFormEnum;
import com.facishare.crm.fmcg.tpm.business.enums.StandardStatusType;
import com.facishare.crm.fmcg.tpm.business.manager.CheckinsManager;
import com.facishare.crm.fmcg.tpm.business.manager.ProofPeriodManager;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityDisplayImgDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityDisplayImgPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAiConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.StatusType;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.model.TPMPreDisplayReport;
import com.facishare.crm.fmcg.tpm.web.contract.model.TPMProofPeriodTime;
import com.facishare.crm.fmcg.tpm.web.contract.model.ValidateCheckin;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fmcg.framework.http.CheckinProxy;
import com.fmcg.framework.http.FmcgCrmProxy;
import com.fmcg.framework.http.FmcgServiceProxy;
import com.fmcg.framework.http.contract.checkin.GetCheckinActionInfoV2;
import com.fmcg.framework.http.contract.fmcg.DataReportAch;
import com.fmcg.framework.http.contract.fmcg.ai.BatchQueryAIRuleByIds;
import com.fmcg.framework.http.contract.fmcg.ai.GetAIRuleDescription;
import com.fmcg.framework.http.contract.fmcg.ai.GetDisplayScenesByModelId;
import com.fs.fmcg.sdk.ai.IDetectClient;
import com.fs.fmcg.sdk.ai.contract.*;
import com.fxiaoke.functions.utils.Maps;
import com.fxiaoke.stone.commons.domain.utils.Strings;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMDisplayReportService extends AbstractDisPlayReportBaseService implements ITPMDisplayReportService {

    private static Map<String, Map<String, String>> TENANT_DISPLAY_FORM_ROW_LAYER_NUMBER = new HashMap<>();

    //{"tenantId":{"shelf":"freezer","shelf":"freezer"}}   RIOSpecialDisplayFormEnum
    private static Map<String, Map<String, String>> TENANT_DISPLAY_DETECT_FAULT_TOLERANCE = new HashMap<>();

    private static Map<String, List<String>> RIO_TENANT_AI_DISPLAY_IDS = new HashMap<>();
    public static final String AI_ERROR_MESSAGE_PREFIX = "AIError";
    public static final String TPM_ERROR_MESSAGE_PREFIX = "TPMError";

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", iConfig -> {

            String displayItemRowOrLayerNumber = iConfig.get("DISPLAY_FORM_ROW_LAYER_NUMBER");
            if (!Strings.isNullOrEmpty(displayItemRowOrLayerNumber)) {
                TENANT_DISPLAY_FORM_ROW_LAYER_NUMBER = JSON.parseObject(displayItemRowOrLayerNumber, new TypeReference<Map<String, Map<String, String>>>() {
                });
            }

            String tenantDisplayDetectFaultTolerance = iConfig.get("TENANT_DISPLAY_DETECT_FAULT_TOLERANCE");
            if (!Strings.isNullOrEmpty(tenantDisplayDetectFaultTolerance)) {
                TENANT_DISPLAY_DETECT_FAULT_TOLERANCE = JSON.parseObject(tenantDisplayDetectFaultTolerance, new TypeReference<Map<String, Map<String, String>>>() {
                });
            }

            String rioTenantAIDisplayIds = iConfig.get("RIO_TENANT_AI_DISPLAY_IDS");
            if (!Strings.isNullOrEmpty(rioTenantAIDisplayIds)) {
                RIO_TENANT_AI_DISPLAY_IDS = JSON.parseObject(rioTenantAIDisplayIds, new TypeReference<Map<String, List<String>>>() {
                });
            }
        });
    }

    private static final List<String> AI_DETECT_EXCLUDE_FIELD_LIST = Lists.newArrayList("aiPath", "posmRelatedSku");

    private static final String ASYNC_PROCESS_PROOF_DISPLAY_IMG = "TPM:ASYNC_PROCESS_PROOF_DISPLAY_IMG:AI:LOCK:%s:%s";
    private static final int LOCK_WAIT = 30;
    private static final int LOCK_LEASE = 420;

    @Resource
    private IActivityTypeManager activityTypeManager;
    @Resource
    private FmcgServiceProxy fmcgServiceProxy;
    @Resource
    private FmcgCrmProxy fmcgCrmProxy;
    @Resource
    private IDetectClient detectClient;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private ActivityDisplayImgDAO activityDisplayImgDAO;
    @Resource
    private ITransactionProxy transactionProxy;
    @Resource
    private CheckinProxy checkinProxy;
    @Resource
    private RedissonClient redissonCmd;
    @Resource
    private IDisplayReportValidator displayReportValidator;
    @Resource
    private ProofPeriodManager proofPeriodManager;
    @Resource
    private CheckinsManager checkinsManager;

    @Override
    public void validateDisplayReport(BaseObjectSaveAction.Arg arg) {
        displayReportValidator.validateDisplayReport(arg);
    }

    @Override
    public void addProofPeriodTime(String userId, BaseObjectSaveAction.Result result) {
        proofPeriodManager.addProofPeriodTime(userId, result);
    }

    @Override
    public void editProofPeriodTime(String userId, BaseObjectSaveAction.Result result) {
        proofPeriodManager.editProofPeriodTime(userId, result);
    }


    @Override
    public TPMProofPeriodTime.Result queryAllProofPeriodTimeData(TPMProofPeriodTime.Arg arg) {
        return proofPeriodManager.queryAllProofPeriodTimeData(arg);
    }


    @Override
    public TPMProofPeriodTime.ProofResult queryRangeProofPeriodTime(TPMProofPeriodTime.Arg arg) {
        return proofPeriodManager.queryRangeProofPeriodTime(arg);
    }

    @Override
    public void asyncProcessProofDisplayImgAi(String tenantId, String userId, ActivityTypeExt activityType, String proofId) {
        log.info("asyncProcessProofDisplayImgAi start,dataId:{}", proofId);

        if (super.isRecognized(tenantId, proofId)) {
            log.info("proof is isRecognized ,proofId:{}", proofId);
            return;
        }
        // 1. 初始化与参数验证
        validateParams(tenantId, activityType, proofId);

        if (isLocked(tenantId, proofId)) {
            log.info("asyncProcessProofDisplayImgAi get lock fail,proofId:{}", proofId);
            throw new ValidateException("system busy");
        }

        String traceId = TraceContext.get().getTraceId();
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
            if (!tryLock(tenantId, proofId)) {
                log.error("asyncProcessProofDisplayImgAi get lock fail,proofId:{}", proofId);
                return;
            }
            boolean detectStatus = false;
            StopWatch stopWatch = StopWatch.create("asyncProcessProofDisplayImgAi");
            try {
                // 2. 加载基础数据
                AIProcessingContext context = initProcessingContext(tenantId, userId, activityType, proofId);
                if (Objects.nonNull(context)) {
                    transactionProxy.run(() -> {
                        TraceContext.get().setTraceId(traceId);
                        // 3. 处理所有图片
                        processImages(context);

                        // 4. 处理失败重试队列
                        processFailedQueue(context);
                    });
                    stopWatch.lap("processImages");

                    // 5. 新建举证项目AI识别结果
                    processAiProofDetail(context);
                    stopWatch.lap("processAiProofDetail");

                    // 6. 更新陈列图片状态
                    updateDisplayImgStatus(context);
                    stopWatch.lap("updateDisplayImgStatus");

                    // 7. 全部处理完成陈列上报
                    completeAllToSendDisplayReport(context, activityType);
                    stopWatch.lap("completeAllToSendDisplayReport");

                    // 8. 更新举证对象
                    updateProof(context);
                    stopWatch.lap("updateProof");

                    // 9. 更新举证时段
                    updateProofPeriod(context);
                    stopWatch.lap("updateProofPeriod");

                    checkinsManager.sendErrorMessageToCheckin(tenantId, proofId, true);
                    stopWatch.lap("sendErrorMessageToCheckin");

                    detectStatus = true;
                }
            } catch (ValidateException validateException) {
                log.error("asyncProcessProofDisplayImgAi async validateException,", validateException);
            } catch (MetaDataBusinessException metaDataBusinessException) {
                log.error("asyncProcessProofDisplayImgAi  async metaDataBusinessException,", metaDataBusinessException);
            } catch (Exception ex) {
                log.error("asyncProcessProofDisplayImgAi async error,", ex);
            } finally {
                if (!detectStatus) {
                    updateProofFail(tenantId, proofId);
                    checkinsManager.sendErrorMessageToCheckin(tenantId, proofId, false);
                }
                unlock(tenantId, proofId);
                stopWatch.log();
            }
            log.info("所有举证图片处理完成，proofId: {}", proofId);

        })).run();
    }


    @Override
    public void testAsyncProcessProofDisplayImgAi(String activityTypeId, String proofId) {
        ApiContext context = ApiContextManager.getContext();
        String tenantId = context.getTenantId();
        ActivityTypeExt activityType = activityTypeManager.find(tenantId, activityTypeId);
        asyncProcessProofDisplayImgAi(tenantId, String.valueOf(context.getEmployeeId()), activityType, proofId);
    }


    @Override
    public void addProofValidation(String tenantId, BaseObjectSaveAction.Arg arg) {
        displayReportValidator.addProofValidation(tenantId, arg);
    }

    @Override
    public TPMPreDisplayReport.Result preDisplayReport(TPMPreDisplayReport.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String tenantId = context.getTenantId();
        // 通过举证 arg.getProofId, 去获取举证数据，取到举证数据里的 visitId , actionId
        IObjectData proofData = serviceFacade.findObjectData(User.systemUser(tenantId), arg.getProofId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        if (proofData == null) {
            throw new ValidateException("activity proof not found.");
        }
        // 获取举证的 visitId , actionId
        String visitId = proofData.get(TPMActivityProofFields.VISIT_ID, String.class);
        String actionId = proofData.get(TPMActivityProofFields.ACTION_ID, String.class);
        String accountId = proofData.get(TPMActivityProofFields.STORE_ID, String.class);
        String activityId = proofData.get(TPMActivityProofFields.ACTIVITY_ID, String.class);

        // 调用外勤服务接口，获取自定义页面的模板id ，templateId，
        GetCheckinActionInfoV2.Arg activityActionInfoArg = new GetCheckinActionInfoV2.Arg();
        activityActionInfoArg.setTenantId(tenantId);
        activityActionInfoArg.setCheckinId(visitId);
        activityActionInfoArg.setActionId(actionId);
        GetCheckinActionInfoV2.Result activityActionInfo = checkinProxy.getActivityActionInfo(Integer.parseInt(tenantId), activityActionInfoArg);
        if (activityActionInfo == null) {
            throw new ValidateException("get appTemplateId is null.");
        }
        return TPMPreDisplayReport.Result.builder()
                .pageTemplateId(activityActionInfo.getAppTemplateId())
                .checkinsId(visitId)
                .actionId(actionId)
                .accountId(accountId)
                .activityId(activityId)
                .proofId(proofData.getId())
                .sourceActionId(activityActionInfo.getSourceActionId())
                .showButton(buildShowButton(activityActionInfo.getShowButton()))
                .build();
    }

    private List<TPMPreDisplayReport.JumpActionButton> buildShowButton(List<GetCheckinActionInfoV2.JumpActionButton> showButton) {

        List<TPMPreDisplayReport.JumpActionButton> showButtonList = new ArrayList<>();
        if (showButton != null) {
            for (GetCheckinActionInfoV2.JumpActionButton btn : showButton) {
                TPMPreDisplayReport.JumpActionButton jumpBtn = new TPMPreDisplayReport.JumpActionButton();
                jumpBtn.setBtnName(btn.getBtnName());
                jumpBtn.setBtnType(btn.getBtnType());
                jumpBtn.setActionId(btn.getActionId());
                jumpBtn.setFuncApiName(btn.getFuncApiName());
                jumpBtn.setCustomParams(btn.getCustomParams());
                jumpBtn.setNeedPopup(btn.isNeedPopup());
                jumpBtn.setPopupBtnText(btn.getPopupBtnText());
                jumpBtn.setActionCode(btn.getActionCode());
                showButtonList.add(jumpBtn);
            }
        }
        return showButtonList;
    }

    /**
     * 处理单个举证时段对象的状态更新
     */
    @Override
    public void processOnePeriodDetail(String tenantId, IObjectData periodDetailObj) {
        proofPeriodManager.processOnePeriodDetail(tenantId, periodDetailObj);
    }

    @Override
    public ValidateCheckin.Result validateCheckin(ValidateCheckin.Arg validateCheckin) {
        ApiContext context = ApiContextManager.getContext();
        if (StringUtils.isEmpty(validateCheckin.getCheckinId())) {
            throw new ValidateException("checkin id is null");
        }
        IObjectData data = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), validateCheckin.getCheckinId(), ApiNames.CHECKINS_OBJ);
        if (Objects.isNull(data)) {
            throw new ValidateException("DATA NOT FOUND");
        }
        Boolean status = data.get(CheckinsObjFields.CHECKIN_STATUS, Boolean.class);
        if (Boolean.TRUE.equals(status)) {
            return ValidateCheckin.Result.builder()
                    .status(ValidateCheckin.Status.FINISH)
                    .message(I18N.text(I18NKeys.CHECKIN_IS_ALREADY_FINISH))
                    .build();
        }
        return ValidateCheckin.Result.builder()
                .status(ValidateCheckin.Status.PROCESSING)
                .message(I18N.text(I18NKeys.CHECKIN_IS_PROCESSING))
                .build();
    }

    private void updateProofFail(String tenantId, String proofId) {
        try {
            IObjectData proofObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), proofId, ApiNames.TPM_ACTIVITY_PROOF_OBJ);
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(TPMActivityProofFields.AI_IDENTIFY_STATUS, TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFY_FAILED);
            serviceFacade.updateWithMap(User.systemUser(tenantId), proofObj, updateMap);
        } catch (Exception ex) {
            log.error("更新举证失败，proofId: {}", proofId, ex);
        }

    }

    private void updateProofPeriod(AIProcessingContext context) {
        try {
            String proofPeriodDetailId = context.getProofObj().get(TPMActivityProofFields.PROOF_TIME_PERIOD_DETAIL_ID, String.class);
            if (StringUtils.isNotBlank(proofPeriodDetailId)) {
                IObjectData objectData = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), proofPeriodDetailId, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ);
                processOnePeriodDetail(context.getTenantId(), objectData);
            }
        } catch (Exception ex) {
            log.error("更新举证时段失败，proofId: {}", context.getProofId(), ex);
        }

    }


    private void completeAllToSendDisplayReport(AIProcessingContext context, ActivityTypeExt activityType) {
        log.info("completeAllToSendDisplayReport proofId:{}", context.getProofId());
        if (!super.isDisplayType(activityType)) {
            log.info("非陈列类活动举证 proofId:{}", context.getProofId());
            return;
        }

        if (!context.existAgreementNode) {
            log.info("未开启协议节点 proofId:{}", context.getProofId());
            return;
        }

        DataReportAch.Arg arg = new DataReportAch.Arg();
        arg.setTenantId(context.getTenantId());
        arg.setDataMainId(context.getProofId());
        arg.setDataMainApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        arg.setCheckinsId(context.getProofObj().get(TPMActivityProofFields.VISIT_ID, String.class, ""));
        arg.setBusinessDataApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        arg.setBusinessDataId(context.getProofObj().get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID, String.class));
        arg.setAccountId(getAccountIdOfProof(context.getProofObj()));
        if (context.existAgreementNode) {
            //协议
            arg.setStandardMainId(context.getProofObj().get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID, String.class));
            arg.setStandardMainApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        } else {
            //活动
            arg.setStandardMainId(context.getProofObj().get(TPMActivityProofFields.ACTIVITY_ID, String.class));
            arg.setStandardMainApiName(ApiNames.TPM_ACTIVITY_OBJ);
        }
        DataReportAch.Result result = fmcgCrmProxy.totalReport(Integer.parseInt(context.getTenantId()), Integer.parseInt(context.getUserId()), arg);
        if (result.getCode() != 0) {
            log.error("completeAllToSendDisplayReport error:{}", result.getMessage());
        }
    }

    private void updateProof(AIProcessingContext context) {
        log.info("updateProof proofId:{}", context.getProofId());
        List<IObjectData> proofDisplayImgs = context.getProofDisplayImgs();
        if (CollectionUtils.isEmpty(proofDisplayImgs)) {
            return;
        }

        List<String> systemJudgmentStatusList = proofDisplayImgs.stream()
                .filter(img -> StringUtils.isNotBlank(img.get(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS, String.class)))
                .map(img -> img.get(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS, String.class))
                .collect(Collectors.toList());
        String systemJudgmentStatus = super.getProofMasterSystemJudgmentStatus(context.getTenantId(), systemJudgmentStatusList);

        List<ActivityDisplayImgPO> errorRecordList = activityDisplayImgDAO.findByProofIds(context.getTenantId(), Lists.newArrayList(context.getProofId()));
        String aiIdentifyStatus = CollectionUtils.isEmpty(errorRecordList)
                ? TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFIED : TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFY_FAILED;

        IObjectData proofObj = serviceFacade.findObjectDataIgnoreAll(
                User.builder().tenantId(context.getTenantId()).userId(context.getUserId()).build()
                , context.getProofId()
                , ApiNames.TPM_ACTIVITY_PROOF_OBJ
        );
        proofObj.set(TPMActivityProofFields.SYSTEM_JUDGMENT_STATUS, systemJudgmentStatus);
        proofObj.set(TPMActivityProofFields.AI_IDENTIFY_STATUS, aiIdentifyStatus);

        serviceFacade.updateObjectData(User.builder().tenantId(context.getTenantId()).userId(context.getUserId()).build(), proofObj);
    }

    private void updateDisplayImgStatus(AIProcessingContext context) {
        List<IObjectData> proofDetails = context.getProofDetails();
        Map<String, List<IObjectData>> displayFormIdActivityProofDetailsMap = proofDetails.stream().collect(Collectors.groupingBy(k -> k.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID, String.class)));

        List<IObjectData> proofDisplayImgs = queryTPMActivityProofDisplayImgsByProofIds(context.getTenantId(), Lists.newArrayList(context.getProofId()));
        Map<String, AIDetectionResults> aiResultsMap = context.getAiResultsMapGroupByProofDisplayImgId();
        log.info("updateDisplayImgStatus proofId:{},aiResultsMap:{},proofDisplayImgs ids:{}", context.getProofId(), JSON.toJSONString(aiResultsMap), proofDisplayImgs.stream().map(DBRecord::getId).collect(Collectors.toList()));
        for (IObjectData img : proofDisplayImgs) {
            String displayFormId = img.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID, String.class);
            if (StringUtils.isNotBlank(displayFormId)) {
                List<IObjectData> activityProofDetails = displayFormIdActivityProofDetailsMap.get(displayFormId);
                if (CollectionUtils.isNotEmpty(activityProofDetails)) {
                    List<String> systemJudgmentStatusList = activityProofDetails.stream()
                            .map(activityProofDetail -> activityProofDetail.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class))
                            .collect(Collectors.toList());

                    String systemJudgmentStatus = super.getProofImgSystemJudgmentStatus(systemJudgmentStatusList);
                    if (StringUtils.isNotBlank(systemJudgmentStatus)) {
                        img.set(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS, systemJudgmentStatus);
                    }
                    String auditStatus = (StringUtils.isBlank(systemJudgmentStatus) || TPMActivityProofDisplayImgFields.NOT_DISPLAY_SYSTEM_JUDGMENT_STATUS.equals(systemJudgmentStatus)) ? TPMActivityProofDisplayImgFields.FAIL_STATUS : systemJudgmentStatus;
                    img.set(TPMActivityProofDisplayImgFields.AUDIT_STATUS, auditStatus);
                }
            }

            AIDetectionResults aiResults = aiResultsMap.get(img.getId());
            if (Objects.nonNull(aiResults)) {
                img.set(TPMActivityProofDisplayImgFields.AI_DISPLAY_FORM_ID, aiResults.getAiDisplayFormIdList());
                img.set(TPMActivityProofDisplayImgFields.AI_LAYER_NUMBER, aiResults.getAiLayerNumber());
                img.set(TPMActivityProofDisplayImgFields.AI_GROUP_NUMBER, aiResults.getAiGroupNumber());
                img.set(TPMActivityProofDisplayImgFields.AI_VISIBLE_NUMBER, aiResults.getAiVisibleNumber());
            }
            serviceFacade.updateObjectData(User.builder().tenantId(context.getTenantId()).userId(context.getUserId()).build(), img);
        }

        context.setProofDisplayImgs(proofDisplayImgs);
    }

    private void processAiProofDetail(AIProcessingContext context) {

        List<IObjectData> proofDetails = buildProofDetails(context);
        if (CollectionUtils.isNotEmpty(proofDetails)) {
            log.info("processAiProofDetail proofDetails :{}", proofDetails.size());
            serviceFacade.bulkSaveObjectData(proofDetails, User.builder().tenantId(context.getTenantId()).userId(context.getUserId()).build());
        }
        context.setProofDetails(proofDetails);
    }

    private List<IObjectData> buildProofDetails(AIProcessingContext context) {
        if (context == null) {
            return Lists.newArrayList();
        }

        // 准备基础数据
        IObjectData proofObj = context.getProofObj();
        String tenantId = context.getTenantId();
        String userId = context.getUserId();
        String proofId = context.getProofId();
        boolean existAgreementNode = context.existAgreementNode;

        // 准备展示图片相关数据
        List<String> displayFormIds = extractDisplayFormIds(context.getProofDisplayImgs());

        Map<String, IObjectData> proofDisplayImgMap = buildProofDisplayImgMap(context.getProofDisplayImgs(), context.getAiResultsMapGroupByProofDisplayImgId());

        // 查询产品和物料详情
        List<IObjectData> proofProductDetails = queryTPMActivityProofProductDetailsByProofIds(tenantId, proofId);
        Map<String, List<IObjectData>> proofImgIdProofProductDetailsMap = groupDetailsByProofImgId(proofProductDetails);

        List<IObjectData> proofMaterialDetails = queryTPMActivityProofMaterialDetailsByProofIds(tenantId, proofId);
        Map<String, List<IObjectData>> displayFormIdProofMaterialDetailsMap = groupDetailsByDisplayFormId(proofMaterialDetails, TPMActivityProofMaterialDetailFields.DISPLAY_FORM_ID);

        // 根据是否存在协议节点分别处理
        if (!existAgreementNode) {
            return buildProofDetailsForActivity(
                    context, proofObj, tenantId, userId, proofId, displayFormIds,
                    proofDisplayImgMap, proofImgIdProofProductDetailsMap, displayFormIdProofMaterialDetailsMap
            );
        } else {
            return buildProofDetailsForAgreement(
                    context, proofObj, tenantId, userId, proofId, displayFormIds,
                    proofDisplayImgMap, proofImgIdProofProductDetailsMap
            );
        }
    }

    /**
     * 从展示图片列表中提取所有的展示形式ID
     */
    private List<String> extractDisplayFormIds(List<IObjectData> proofDisplayImgs) {
        return proofDisplayImgs.stream()
                .map(img -> img.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID, String.class))
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 构建展示图片映射，按展示形式ID索引
     */
    private Map<String, IObjectData> buildProofDisplayImgMap(List<IObjectData> proofDisplayImgs, Map<String, AIDetectionResults> aiResultsMapGroupByProofDisplayImgId) {

        return proofDisplayImgs.stream()
                .collect(Collectors.toMap(
                        proofDisplayImg -> proofDisplayImg.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID, String.class),
                        proofDisplayImg -> {
                            AIDetectionResults aiResults = aiResultsMapGroupByProofDisplayImgId.get(proofDisplayImg.getId());
                            if (Objects.nonNull(aiResults)) {
                                String displayFromId = proofDisplayImg.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID, String.class);
                                proofDisplayImg.set(TPMActivityProofDisplayImgFields.AI_LAYER_NUMBER, (double) aiResults.getAiLayerNumber());
                                proofDisplayImg.set(TPMActivityProofDisplayImgFields.AI_GROUP_NUMBER, (double) aiResults.getAiGroupNumber());
                                proofDisplayImg.set(TPMActivityProofDisplayImgFields.AI_VISIBLE_NUMBER, (double) aiResults.getAiVisibleNumber());
                                proofDisplayImg.set(TPMActivityProofDisplayImgFields.PILE_HEAD_COUNT, (double) aiResults.getPileHeadCount());
                                Map<String, Long> countGroupByDisplayId = aiResults.getCountGroupByDisplayId();
                                if (countGroupByDisplayId != null) {
                                    proofDisplayImg.set(TPMActivityProofDisplayImgFields.COUNT_NUMBER, Double.parseDouble(String.valueOf(countGroupByDisplayId.getOrDefault(displayFromId, 0L))));
                                }
                            }
                            return proofDisplayImg;
                        }
                ));
    }

    /**
     * 按陈列形式ID对详情数据进行分组
     */
    private <T> Map<String, List<T>> groupDetailsByDisplayFormId(List<T> details, String displayFormIdField) {
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyMap();
        }
        return details.stream()
                .collect(Collectors.groupingBy(detail ->
                        ((IObjectData) detail).get(displayFormIdField, String.class)
                ));
    }

    /**
     * 按举证陈列图片ID对详情数据进行分组
     */
    private Map<String, List<IObjectData>> groupDetailsByProofImgId(List<IObjectData> details) {
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyMap();
        }
        return details.stream()
                .collect(Collectors.groupingBy(detail ->
                        detail.get(TPMActivityProofProductDetailFields.ACTIVITY_PROOF_DISPLAY_IMG_ID, String.class)
                ));
    }

    /**
     * 为活动构建举证详情
     */
    private List<IObjectData> buildProofDetailsForActivity(
            AIProcessingContext context, IObjectData proofObj, String tenantId, String userId, String proofId,
            List<String> displayFormIds, Map<String, IObjectData> proofDisplayImgMap,
            Map<String, List<IObjectData>> proofImgIdProofProductDetailsMap,
            Map<String, List<IObjectData>> displayFormIdProofMaterialDetailsMap) {

        List<IObjectData> proofDetails = Lists.newArrayList();

        String activityId = proofObj.get(TPMActivityProofFields.ACTIVITY_ID, String.class);
        String agreementId = proofObj.get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID, String.class);

        // 查询产品和物料达成状态
        Map<String, ProductOrMaterialAchieveStatusDTO> productOrMaterialAchieveStatusMap =
                queryProductOrMaterialAchieveStatus(tenantId, activityId, agreementId, proofId, proofObj, false);

        // 查询活动详情
        List<IObjectData> activityDetails = queryTPMActivityDetailByObjectId(tenantId, activityId);
        if (CollectionUtils.isEmpty(activityDetails)) {
            return proofDetails;
        }

        // 查询RIO特殊展示形式
        Map<String, IObjectData> rioSpecialDisplayFormDTOMapGroupByActivityDetailId =
                queryRIODisplayProjectJudgmentStandard(tenantId, activityDetails, false);

        // 过滤出有效的活动详情
        activityDetails = filterActivityDetailsByDisplayFormIds(activityDetails, displayFormIds);

        // 准备活动项ID和名称的映射
        Map<String, String> activityItemIdNameMap = prepareActivityItemNameMap(tenantId, activityDetails);

        // 为每个活动详情创建举证详情对象
        for (IObjectData activityDetail : activityDetails) {
            IObjectData proofDetail = createProofDetailForActivity(
                    activityDetail, proofObj, tenantId, userId, proofId, activityId,
                    proofDisplayImgMap, proofImgIdProofProductDetailsMap, displayFormIdProofMaterialDetailsMap,
                    productOrMaterialAchieveStatusMap, rioSpecialDisplayFormDTOMapGroupByActivityDetailId,
                    activityItemIdNameMap
            );

            proofDetails.add(proofDetail);
        }

        return proofDetails;
    }

    /**
     * 过滤出与指定展示形式ID匹配的活动详情
     */
    private List<IObjectData> filterActivityDetailsByDisplayFormIds(List<IObjectData> activityDetails, List<String> displayFormIds) {
        return activityDetails.stream()
                .filter(activityDetail ->
                        displayFormIds.contains(activityDetail.get(TPMActivityDetailFields.DISPLAY_FORM_ID, String.class))
                )
                .collect(Collectors.toList());
    }

    /**
     * 准备活动项ID和名称的映射
     */
    private Map<String, String> prepareActivityItemNameMap(String tenantId, List<IObjectData> activityDetails) {
        List<String> activityItemIds = activityDetails.stream()
                .map(activityDetail -> activityDetail.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID, String.class))
                .distinct()
                .collect(Collectors.toList());

        List<IObjectData> activityItems = serviceFacade.findObjectDataByIds(tenantId, activityItemIds, ApiNames.TPM_ACTIVITY_ITEM_OBJ);

        return activityItems.stream()
                .collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));
    }

    /**
     * 为活动创建单个举证详情对象
     */
    private IObjectData createProofDetailForActivity(
            IObjectData activityDetail, IObjectData proofObj, String tenantId, String userId, String proofId, String activityId,
            Map<String, IObjectData> proofDisplayImgMap,
            Map<String, List<IObjectData>> proofImgIdProofProductDetailsMap,
            Map<String, List<IObjectData>> displayFormIdProofMaterialDetailsMap,
            Map<String, ProductOrMaterialAchieveStatusDTO> productOrMaterialAchieveStatusMap,
            Map<String, IObjectData> rioSpecialDisplayFormDTOMap,
            Map<String, String> activityItemIdNameMap) {

        // 从活动明细映射字段到举证明细
        String displayFormId = activityDetail.get(TPMActivityDetailFields.DISPLAY_FORM_ID, String.class);
        String activityItemId = activityDetail.get(TPMActivityDetailFields.ACTIVITY_ITEM_ID, String.class);
        Double amountCostStandard = activityDetail.get(TPMActivityDetailFields.ACTIVITY_COST_STANDARD, Double.class, 0.0);
        Double amountAmountStandard = activityDetail.get(TPMActivityDetailFields.ACTIVITY_AMOUNT_STANDARD, Double.class, 0.0);

        // 计算AI识别数值
        Double aiNumber = 0.0;
        Double aiVisibleNumber = 0.0;
        Double aiFaceNumber = calculateAiFaceNumber(proofImgIdProofProductDetailsMap, proofDisplayImgMap, displayFormId);
        Double aiSkuNumber = calculateAiSkuNumber(proofImgIdProofProductDetailsMap, proofDisplayImgMap, displayFormId);

        // 确定正确的AI数值类型
        String countNumberItemId = "";
        String visibleItemId = "";
        if (StringUtils.isNotBlank(activityItemId)) {
            String layerNumberItemId = getDisplayFormSpecialItemIdByTenantId(tenantId, DisplayFormItemEnum.LAYER_NUMBER.getFieldKey());
            String rowNumberItemId = getDisplayFormSpecialItemIdByTenantId(tenantId, DisplayFormItemEnum.ROW_NUMBER.getFieldKey());
            countNumberItemId = getDisplayFormSpecialItemIdByTenantId(tenantId, DisplayFormItemEnum.COUNT_NUMBER.getFieldKey());
            visibleItemId = getDisplayFormSpecialItemIdByTenantId(tenantId, DisplayFormItemEnum.VISIBLE_NUMBER.getFieldKey());
            String groupItemId = getDisplayFormSpecialItemIdByTenantId(tenantId, DisplayFormItemEnum.GROUP_NUMBER.getFieldKey());

            //费用项目：堆头。堆头需要特判，只使用可视面判断特殊衡量标准。
            if (StringUtils.isNotBlank(visibleItemId) && Objects.equals(visibleItemId, activityItemId)) {
                aiVisibleNumber = proofDisplayImgMap.get(displayFormId).get(TPMActivityProofDisplayImgFields.AI_VISIBLE_NUMBER, Double.class, 0.0);
            }

            //费用项目：组数
            if (StringUtils.isNotBlank(groupItemId) && Objects.equals(groupItemId, activityItemId)) {
                aiNumber = proofDisplayImgMap.get(displayFormId).get(TPMActivityProofDisplayImgFields.AI_GROUP_NUMBER, Double.class, 0.0);
            }

            //费用项目：层数
            if (StringUtils.isNotBlank(layerNumberItemId) && Objects.equals(layerNumberItemId, activityItemId)) {
                aiNumber = proofDisplayImgMap.get(displayFormId).get(TPMActivityProofDisplayImgFields.AI_LAYER_NUMBER, Double.class, 0.0);
            }

            //费用项目：排面数
            if (StringUtils.isNotBlank(rowNumberItemId) && Objects.equals(rowNumberItemId, activityItemId)) {
                aiNumber = aiFaceNumber;
            }

            //费用项目：个数
            if (StringUtils.isNotBlank(countNumberItemId) && Objects.equals(countNumberItemId, activityItemId)) {
                aiNumber = aiFaceNumber;
            }
        }

        // 创建举证明细对象
        IObjectData proofDetail = buildDefaultIObjectData(userId, ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, activityDetail);

        // 设置关联信息
        proofDetail.set(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID, proofId);
        proofDetail.set(TPMActivityProofFields.ACTIVITY_ID, activityId);
        proofDetail.set(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID, activityDetail.getId());
        proofDetail.set(TPMActivityProofDetailFields.DISPLAY_FORM_ID, displayFormId);
        proofDetail.set(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, activityItemId);
        proofDetail.set(TPMActivityProofDetailFields.PROOF_DETAIL_COST_STANDARD, amountCostStandard);
        proofDetail.set(TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD, amountAmountStandard);
        proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, null);

        // 设置默认值
        proofDetail.set(TPMActivityProofDetailFields.PROOF_ITEM, activityItemIdNameMap.getOrDefault(activityItemId, ""));
        //堆头存的个数
        if (StringUtils.isNotBlank(visibleItemId) && Objects.equals(visibleItemId, activityItemId)) {
            proofDetail.set(TPMActivityProofDetailFields.AI_VISIBLE_NUMBER, aiVisibleNumber);
            Double pileHeadCount = proofDisplayImgMap.get(displayFormId).get(TPMActivityProofDisplayImgFields.PILE_HEAD_COUNT, Double.class, 0.0);
            proofDetail.set(TPMActivityProofDetailFields.AMOUNT, pileHeadCount);
            proofDetail.set(TPMActivityProofDetailFields.AI_NUMBER, pileHeadCount);
            proofDetail.set(TPMActivityProofDetailFields.AI_FACE_NUMBER, 0);
        }
        //冰箱、tg存的个数
        else if (StringUtils.isNotBlank(countNumberItemId) && Objects.equals(countNumberItemId, activityItemId)) {
            Double count = proofDisplayImgMap.get(displayFormId).get(TPMActivityProofDisplayImgFields.COUNT_NUMBER, Double.class, 0.0);
            proofDetail.set(TPMActivityProofDetailFields.AMOUNT, count);
            proofDetail.set(TPMActivityProofDetailFields.AI_NUMBER, count);
            proofDetail.set(TPMActivityProofDetailFields.AI_FACE_NUMBER, aiFaceNumber);
        } else {
            proofDetail.set(TPMActivityProofDetailFields.AMOUNT, aiNumber);
            proofDetail.set(TPMActivityProofDetailFields.AI_NUMBER, aiNumber);
            proofDetail.set(TPMActivityProofDetailFields.AI_FACE_NUMBER, aiFaceNumber);
        }
        proofDetail.set(TPMActivityProofDetailFields.AI_SKU_NUMBER, aiSkuNumber);

        if (TPMGrayUtils.aiDisplayRioMode(tenantId)) {
            BigDecimal aiVisibleNumberDecimal = BigDecimal.valueOf(aiVisibleNumber);
            if (BigDecimal.ZERO.compareTo(aiVisibleNumberDecimal) == 0) {
                proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_NUMBER__C, aiFaceNumber);
            } else {
                proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_NUMBER__C, aiVisibleNumber);
            }
        }
        // 设置产品和物料陈列结果
        setProductAndMaterialDisplayStatus(proofDetail, activityDetail, productOrMaterialAchieveStatusMap);


        // 计算并设置系统判定结果
        List<String> aiDisplayIds = RIO_TENANT_AI_DISPLAY_IDS.get(tenantId);
        //如果是不需要AI识别的陈列形式，统一判定为待审核
        if (CollectionUtils.isNotEmpty(aiDisplayIds) && !aiDisplayIds.contains(displayFormId)) {
            proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, TPMActivityProofDetailFields.PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS);
        }//费用项目：个数、费用项目：地堆。不进行标准数量的判定，只进行RIO的特殊衡量标准判定
        else if ((StringUtils.isNotBlank(countNumberItemId) && Objects.equals(countNumberItemId, activityItemId)) ||
                (StringUtils.isNotBlank(visibleItemId) && Objects.equals(visibleItemId, activityItemId))) {
            if (TPMGrayUtils.aiDisplayRioMode(tenantId)) {
                proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, determineRioSystemJudgmentStatus(rioSpecialDisplayFormDTOMap, activityDetail, aiFaceNumber, aiVisibleNumber));
                proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_MODE__C, TPMActivityProofDetailFields.ONLY_METRIC);
            }
        } else {
            setSystemJudgmentStatus(proofDetail, tenantId, activityDetail, rioSpecialDisplayFormDTOMap, aiNumber, aiFaceNumber, aiVisibleNumber, amountAmountStandard);
        }
        log.info("displayId:{},aiFaceNumber:{},activityItemId:{},aiNumber:{},amountAmountStandard:{},system_judgment_status:{}", displayFormId,
                aiFaceNumber, activityItemId, aiNumber, amountAmountStandard, proofDetail.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class));
        return proofDetail;
    }

    /**
     * 为协议创建单个举证详情对象
     */
    private IObjectData createProofDetailForAgreement(
            IObjectData agreementDetail, IObjectData proofObj, String tenantId, String userId, String proofId, String activityId,
            Map<String, IObjectData> proofDisplayImgMap,
            Map<String, List<IObjectData>> proofImgIdProofProductDetailsMap,
            Map<String, ProductOrMaterialAchieveStatusDTO> productOrMaterialAchieveStatusMap,
            Map<String, IObjectData> rioSpecialDisplayFormDTOMapGroupByActivityDetailId,
            Map<String, String> activityItemIdNameMap) {

        // 从协议明细映射字段到举证明细
        String displayFormId = agreementDetail.get(TPMActivityAgreementDetailFields.DISPLAY_FORM_ID, String.class);
        String activityDetailId = agreementDetail.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID, String.class);
        String activityItemId = agreementDetail.get(TPMActivityAgreementDetailFields.ACTIVITY_ITEM_ID, String.class);
        Double amountCostStandard = agreementDetail.get(TPMActivityDetailFields.ACTIVITY_COST_STANDARD, Double.class, 0.0);
        Double amountAmountStandard = agreementDetail.get(TPMActivityDetailFields.AGREEMENT_AMOUNT_STANDARD, Double.class, 0.0);

        // 创建举证明细对象
        IObjectData proofDetail = buildDefaultIObjectData(userId, ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, agreementDetail);

        // 设置关联信息
        proofDetail.set(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID, proofId);
        proofDetail.set(TPMActivityProofFields.ACTIVITY_ID, agreementDetail);
        proofDetail.set(TPMActivityProofDetailFields.ACTIVITY_AGREEMENT_DETAIL_ID, agreementDetail.getId());
        proofDetail.set(TPMActivityProofFields.ACTIVITY_ID, activityId);
        proofDetail.set(TPMActivityProofDetailFields.ACTIVITY_DETAIL_ID, activityDetailId);
        proofDetail.set(TPMActivityProofDetailFields.DISPLAY_FORM_ID, displayFormId);
        proofDetail.set(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, activityItemId);
        proofDetail.set(TPMActivityProofDetailFields.PROOF_DETAIL_COST_STANDARD, amountCostStandard);
        proofDetail.set(TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD, amountAmountStandard);
        proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, null);

        // 计算AI识别数值
        Double aiNumber = 0.0;
        Double aiVisibleNumber = 0.0;
        Double aiFaceNumber = calculateAiFaceNumber(proofImgIdProofProductDetailsMap, proofDisplayImgMap, displayFormId);
        Double aiSkuNumber = calculateAiSkuNumber(proofImgIdProofProductDetailsMap, proofDisplayImgMap, displayFormId);

        // 确定正确的AI数值类型
        String countNumberItemId = "";
        String visibleItemId = "";
        if (StringUtils.isNotBlank(activityItemId)) {
            String layerNumberItemId = getDisplayFormSpecialItemIdByTenantId(tenantId, DisplayFormItemEnum.LAYER_NUMBER.getFieldKey());
            String rowNumberItemId = getDisplayFormSpecialItemIdByTenantId(tenantId, DisplayFormItemEnum.ROW_NUMBER.getFieldKey());
            countNumberItemId = getDisplayFormSpecialItemIdByTenantId(tenantId, DisplayFormItemEnum.COUNT_NUMBER.getFieldKey());
            visibleItemId = getDisplayFormSpecialItemIdByTenantId(tenantId, DisplayFormItemEnum.VISIBLE_NUMBER.getFieldKey());
            String groupItemId = getDisplayFormSpecialItemIdByTenantId(tenantId, DisplayFormItemEnum.GROUP_NUMBER.getFieldKey());

            //费用项目：地堆。地堆需要特判，只使用可视面判断特殊衡量标准。
            if (StringUtils.isNotBlank(visibleItemId) && Objects.equals(visibleItemId, activityItemId)) {
                aiVisibleNumber = proofDisplayImgMap.get(displayFormId).get(TPMActivityProofDisplayImgFields.AI_VISIBLE_NUMBER, Double.class, 0.0);
            }

            //费用项目：组数
            if (StringUtils.isNotBlank(groupItemId) && Objects.equals(groupItemId, activityItemId)) {
                aiNumber = proofDisplayImgMap.get(displayFormId).get(TPMActivityProofDisplayImgFields.AI_GROUP_NUMBER, Double.class, 0.0);
            }

            //费用项目：层数
            if (StringUtils.isNotBlank(layerNumberItemId) && Objects.equals(layerNumberItemId, activityItemId)) {
                aiNumber = proofDisplayImgMap.get(displayFormId).get(TPMActivityProofDisplayImgFields.AI_LAYER_NUMBER, Double.class, 0.0);
            }

            //费用项目：排面数
            if (StringUtils.isNotBlank(rowNumberItemId) && Objects.equals(rowNumberItemId, activityItemId)) {
                aiNumber = aiFaceNumber;
            }

            //费用项目：个数
            if (StringUtils.isNotBlank(countNumberItemId) && Objects.equals(countNumberItemId, activityItemId)) {
                aiNumber = aiFaceNumber;
            }

        }

        // 设置默认值
        proofDetail.set(TPMActivityProofDetailFields.PROOF_ITEM, activityItemIdNameMap.getOrDefault(activityItemId, ""));
        if (StringUtils.isNotBlank(visibleItemId) && Objects.equals(visibleItemId, activityItemId)) {
            proofDetail.set(TPMActivityProofDetailFields.AI_VISIBLE_NUMBER, aiVisibleNumber);
            Double pileHeadCount = proofDisplayImgMap.get(displayFormId).get(TPMActivityProofDisplayImgFields.PILE_HEAD_COUNT, Double.class, 0.0);
            proofDetail.set(TPMActivityProofDetailFields.AMOUNT, pileHeadCount);
            proofDetail.set(TPMActivityProofDetailFields.AI_NUMBER, pileHeadCount);
            proofDetail.set(TPMActivityProofDetailFields.AI_FACE_NUMBER, 0);
        }
        //冰箱、tg存的个数
        else if (StringUtils.isNotBlank(countNumberItemId) && Objects.equals(countNumberItemId, activityItemId)) {
            Double count = proofDisplayImgMap.get(displayFormId).get(TPMActivityProofDisplayImgFields.COUNT_NUMBER, Double.class, 0.0);
            proofDetail.set(TPMActivityProofDetailFields.AMOUNT, count);
            proofDetail.set(TPMActivityProofDetailFields.AI_NUMBER, count);
            proofDetail.set(TPMActivityProofDetailFields.AI_FACE_NUMBER, aiFaceNumber);
        } else {
            proofDetail.set(TPMActivityProofDetailFields.AMOUNT, aiNumber);
            proofDetail.set(TPMActivityProofDetailFields.AI_NUMBER, aiNumber);
            proofDetail.set(TPMActivityProofDetailFields.AI_FACE_NUMBER, aiFaceNumber);
        }
        proofDetail.set(TPMActivityProofDetailFields.AI_SKU_NUMBER, aiSkuNumber);

        if (TPMGrayUtils.aiDisplayRioMode(tenantId)) {
            BigDecimal aiVisibleNumberDecimal = BigDecimal.valueOf(aiVisibleNumber);
            if (BigDecimal.ZERO.compareTo(aiVisibleNumberDecimal) == 0) {
                proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_NUMBER__C, aiFaceNumber);
            } else {
                proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_NUMBER__C, aiVisibleNumber);
            }
        }

        // 设置产品和物料陈列结果
        setProductAndMaterialDisplayStatus(proofDetail, agreementDetail, productOrMaterialAchieveStatusMap);

        // 计算并设置系统判定结果
        //费用项目：个数、费用项目：地堆。不进行标准数量的判定，只进行RIO的特殊衡量标准判定
        List<String> aiDisplayIds = RIO_TENANT_AI_DISPLAY_IDS.get(tenantId);
        //如果是不需要AI识别的陈列形式，统一判定为待审核
        if (CollectionUtils.isNotEmpty(aiDisplayIds) && !aiDisplayIds.contains(displayFormId)) {
            proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, TPMActivityProofDetailFields.PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS);
        } else if ((StringUtils.isNotBlank(countNumberItemId) && Objects.equals(countNumberItemId, activityItemId)) ||
                (StringUtils.isNotBlank(visibleItemId) && Objects.equals(visibleItemId, activityItemId))) {
            if (TPMGrayUtils.aiDisplayRioMode(tenantId)) {
                proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, determineRioSystemJudgmentStatus(rioSpecialDisplayFormDTOMapGroupByActivityDetailId, agreementDetail, aiFaceNumber, aiVisibleNumber));
                proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_MODE__C, TPMActivityProofDetailFields.ONLY_METRIC);
            }
        } else {
            setSystemJudgmentStatus(proofDetail, tenantId, agreementDetail, rioSpecialDisplayFormDTOMapGroupByActivityDetailId, aiNumber, aiFaceNumber, aiVisibleNumber, amountAmountStandard);
        }
        log.info("displayId:{},aiFaceNumber:{},activityItemId:{},aiNumber:{},amountAmountStandard:{},system_judgment_status:{}", displayFormId,
                aiFaceNumber, activityItemId, aiNumber, amountAmountStandard, proofDetail.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class));
        return proofDetail;
    }

    /**
     * 计算AI面数（产品排面数量总和）
     */
    private Double calculateAiFaceNumber(Map<String, List<IObjectData>> proofImgIdProofProductDetailsMap, Map<String, IObjectData> proofDisplayImgMap, String displayFormId) {
        String proofImageId = proofDisplayImgMap.get(displayFormId).getId();
        return Optional.ofNullable(proofImgIdProofProductDetailsMap.get(proofImageId))
                .map(products -> products.stream()
                        .mapToDouble(img -> img.get(TPMActivityProofProductDetailFields.AI_NUMBER, Double.class, 0.0))
                        .sum())
                .orElse(0.0);
    }

    /**
     * 计算AI物料数量
     */
    private Double calculateAiMaterialNumber(Map<String, List<IObjectData>> displayFormIdProofMaterialDetailsMap, String displayFormId) {
        return Optional.ofNullable(displayFormIdProofMaterialDetailsMap.get(displayFormId))
                .map(materials -> materials.stream()
                        .mapToDouble(img -> img.get(TPMActivityProofMaterialDetailFields.AI_NUMBER, Double.class, 0.0))
                        .sum())
                .orElse(0.0);
    }

    /**
     * 计算AI的SKU数量（不同产品ID的数量）
     */
    private Double calculateAiSkuNumber(Map<String, List<IObjectData>> proofImgIdProofProductDetailsMap, Map<String, IObjectData> proofDisplayImgMap, String displayFormId) {
        String proofImageId = proofDisplayImgMap.get(displayFormId).getId();
        return Optional.ofNullable(proofImgIdProofProductDetailsMap.get(proofImageId))
                .map(products -> products.stream()
                        .map(product -> product.get(TPMActivityProofProductDetailFields.PRODUCT_ID, String.class))
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .count())
                .map(count -> (double) count)
                .orElse(0.0);
    }

    /**
     * 设置产品和物料陈列结果
     */
    private void setProductAndMaterialDisplayStatus(IObjectData proofDetail, IObjectData detailObj,
                                                    Map<String, ProductOrMaterialAchieveStatusDTO> productOrMaterialAchieveStatusMap) {
        String productDisplayStatus = "";
        String materialDisplayStatus = "";

        ProductOrMaterialAchieveStatusDTO statusDTO = productOrMaterialAchieveStatusMap.get(detailObj.getId());
        if (Objects.nonNull(statusDTO)) {
            productDisplayStatus = StringUtils.isNotBlank(statusDTO.getProductAchieveStatus())
                    ? statusDTO.getProductAchieveStatus() : "";
            materialDisplayStatus = StringUtils.isNotBlank(statusDTO.getMaterialAchieveStatus())
                    ? statusDTO.getMaterialAchieveStatus() : "";
        }

        proofDetail.set(TPMActivityProofDetailFields.PRODUCT_DISPLAY_STATUS, productDisplayStatus);
        proofDetail.set(TPMActivityProofDetailFields.MATERIAL_DISPLAY_STATUS, materialDisplayStatus);
    }

    /**
     * 计算并设置系统判定结果
     */
    private void setSystemJudgmentStatus(IObjectData proofDetail, String tenantId, IObjectData detailObj,
                                         Map<String, IObjectData> rioSpecialDisplayFormDTOMap, Double aiNumber, Double aiFaceNumber, Double aiVisibleNumber, Double amountAmountStandard) {
        String productDisplayStatus = proofDetail.get(TPMActivityProofDetailFields.PRODUCT_DISPLAY_STATUS, String.class, "");
        String materialDisplayStatus = proofDetail.get(TPMActivityProofDetailFields.MATERIAL_DISPLAY_STATUS, String.class, "");
        String productItemStandardId = detailObj.get(TPMActivityDetailFields.PRODUCT_ITEM_STANDARD_ID, String.class, "");
        String materialItemStandardId = detailObj.get(TPMActivityDetailFields.MATERIAL_STANDARD_REQUIREM_ID, String.class, "");

        // 确定标准状态类型
        StandardStatusType standardStatus = determineStandardStatusType(productItemStandardId, materialItemStandardId);

        String systemJudgmentStatus = "";
        String judgmentMode = TPMActivityProofDetailFields.ONLY_STANDARD;
        if (aiNumber.compareTo(amountAmountStandard) >= 0) {

            systemJudgmentStatus = TPMActivityProofDetailFields.PASS_STATUS;

            if (TPMGrayUtils.aiDisplayRioMode(tenantId)) {
                judgmentMode = TPMActivityProofDetailFields.METRIC_AND_STANDARD;
                systemJudgmentStatus = determineRioSystemJudgmentStatus(rioSpecialDisplayFormDTOMap, detailObj, aiFaceNumber, aiVisibleNumber);
                //如果没有额外的衡量标准，这里还是赋值达标状态
                if (StringUtils.isEmpty(systemJudgmentStatus)) {
                    systemJudgmentStatus = TPMActivityProofDetailFields.PASS_STATUS;
                    judgmentMode = TPMActivityProofDetailFields.ONLY_STANDARD;
                }
            }
            if (!TPMActivityProofDetailFields.PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS.equals(systemJudgmentStatus)) {
                systemJudgmentStatus = handleAiNumberMeetsStandard(tenantId, systemJudgmentStatus, productDisplayStatus, materialDisplayStatus, standardStatus);
            }
        } else if (aiNumber.compareTo(0.0) == 0) {
            systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
        } else if (aiNumber.compareTo(amountAmountStandard) < 0) {
            systemJudgmentStatus = handleAiNumberBelowStandard(tenantId, systemJudgmentStatus, productDisplayStatus, materialDisplayStatus, standardStatus);
        }
        proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, systemJudgmentStatus);
        proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_MODE__C, judgmentMode);
    }

    private String handleAiNumberMeetsStandard(String tenantId, String systemJudgmentStatus,
                                               String productDisplayStatus, String materialDisplayStatus,
                                               StandardStatusType standardStatus) {

        switch (standardStatus) {
            case BOTH_STANDARD:
                if (StringUtils.isBlank(productDisplayStatus) || StringUtils.isBlank(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(productDisplayStatus) && TPMActivityProofDetailFields.PASS_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.PASS_STATUS;
                } else if (TPMActivityProofDetailFields.FAIL_STATUS.equals(productDisplayStatus) || TPMActivityProofDetailFields.FAIL_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;
            case PRODUCT_STANDARD_ONLY:
                if (StringUtils.isBlank(productDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(productDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.PASS_STATUS;
                } else if (TPMActivityProofDetailFields.FAIL_STATUS.equals(productDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;
            case MATERIAL_STANDARD_ONLY:
                if (StringUtils.isBlank(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.PASS_STATUS;
                } else if (TPMActivityProofDetailFields.FAIL_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;
            case NO_STANDARD:
            default:
                break;
        }
        return systemJudgmentStatus;
    }

    /**
     * 处理AI数量低于标准数量的情况
     */
    private String handleAiNumberBelowStandard(
            String tenantId, String systemJudgmentStatus, String productDisplayStatus, String materialDisplayStatus,
            StandardStatusType standardStatus) {
        switch (standardStatus) {
            case NO_STANDARD:
                systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
                break;
            case BOTH_STANDARD:
                if (TPMActivityProofDetailFields.FAIL_STATUS.equals(productDisplayStatus) && TPMActivityProofDetailFields.FAIL_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(productDisplayStatus) || TPMActivityProofDetailFields.PASS_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;
            case PRODUCT_STANDARD_ONLY:
                if (StringUtils.isBlank(productDisplayStatus) || TPMActivityProofDetailFields.FAIL_STATUS.equals(productDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(productDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;
            case MATERIAL_STANDARD_ONLY:
                if (StringUtils.isBlank(materialDisplayStatus) || TPMActivityProofDetailFields.FAIL_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;

            default:
                break;
        }

        return systemJudgmentStatus;
    }

    /**
     * 确定标准状态类型
     */
    private StandardStatusType determineStandardStatusType(String productItemStandardId, String materialItemStandardId) {
        if (StringUtils.isBlank(productItemStandardId) && StringUtils.isBlank(materialItemStandardId)) {
            return StandardStatusType.NO_STANDARD;
        } else if (StringUtils.isNotBlank(productItemStandardId) && StringUtils.isNotBlank(materialItemStandardId)) {
            return StandardStatusType.BOTH_STANDARD;
        } else if (StringUtils.isNotBlank(productItemStandardId) && StringUtils.isBlank(materialItemStandardId)) {
            return StandardStatusType.PRODUCT_STANDARD_ONLY;
        } else {
            return StandardStatusType.MATERIAL_STANDARD_ONLY;
        }
    }

    /**
     * 确定RIO系统的判定结果
     */
    private String determineRioSystemJudgmentStatus(Map<String, IObjectData> rioSpecialDisplayFormDTOMap,
                                                    IObjectData detailObj, Double aiFaceNumber, Double aiVisibleNumber) {
        String activityDetailId = ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ.equals(detailObj.getDescribeApiName()) ?
                detailObj.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID, String.class) : detailObj.getId();
        IObjectData displayProjectJudgmentStandardObj = rioSpecialDisplayFormDTOMap.get(activityDetailId);
        BigDecimal aiVisibleNumberDecimal = BigDecimal.valueOf(aiVisibleNumber);

        if (Objects.nonNull(displayProjectJudgmentStandardObj)) {
            Double targetValue = displayProjectJudgmentStandardObj.get(DisplayProjectJudgmentStandardFields.TARGET_VALUE, Double.class, 0.0);
            Double unmetValue = displayProjectJudgmentStandardObj.get(DisplayProjectJudgmentStandardFields.UNMET_VALUE, Double.class, 0.0);

            if (BigDecimal.ZERO.compareTo(aiVisibleNumberDecimal) == 0) {
                return recorrectStatus(aiFaceNumber, targetValue, unmetValue);
            } else {
                return recorrectStatus(aiVisibleNumber, targetValue, unmetValue);
            }

        }
        return "";
    }

    private String recorrectStatus(Double currentValue, Double targetValue, Double unmetValue) {
        if (currentValue.compareTo(targetValue) >= 0) {
            return TPMActivityProofDetailFields.PASS_STATUS;
        } else if (currentValue.compareTo(unmetValue) < 0) {
            return TPMActivityProofDetailFields.FAIL_STATUS;
        } else if (currentValue.compareTo(unmetValue) >= 0 && currentValue.compareTo(targetValue) < 0) {
            return TPMActivityProofDetailFields.PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS;
        }
        return "";
    }

    /**
     * 为协议构建举证详情
     */
    private List<IObjectData> buildProofDetailsForAgreement(
            AIProcessingContext context, IObjectData proofObj, String tenantId, String userId, String proofId,
            List<String> displayFormIds, Map<String, IObjectData> proofDisplayImgMap,
            Map<String, List<IObjectData>> proofImgIdProofProductDetailsMap) {

        List<IObjectData> proofDetails = Lists.newArrayList();

        String activityId = proofObj.get(TPMActivityProofFields.ACTIVITY_ID, String.class);
        String agreementId = proofObj.get(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID, String.class);

        // 查询协议详情
        List<IObjectData> agreementDetails = queryTPMAgreementDetailByObjectId(tenantId, agreementId);
        if (CollectionUtils.isEmpty(agreementDetails)) {
            return proofDetails;
        }

        // 查询产品和物料达成状态
        Map<String, ProductOrMaterialAchieveStatusDTO> productOrMaterialAchieveStatusMap =
                queryProductOrMaterialAchieveStatus(tenantId, activityId, agreementId, proofId, proofObj, true);

        // 查询RIO特殊的陈列形式衡量标准
        Map<String, IObjectData> rioSpecialDisplayFormDTOMapGroupByActivityDetailId =
                queryRIODisplayProjectJudgmentStandard(tenantId, agreementDetails, true);

        // 过滤出有效的协议详情
        agreementDetails = agreementDetails.stream()
                .filter(detail -> displayFormIds.contains(detail.get(TPMActivityAgreementDetailFields.DISPLAY_FORM_ID, String.class)))
                .collect(Collectors.toList());

        // 准备活动项ID和名称的映射
        Map<String, String> activityItemIdNameMap = prepareAgreementActivityItemNameMap(tenantId, agreementDetails);

        // 为每个协议详情创建举证详情对象
        for (IObjectData agreementDetail : agreementDetails) {
            IObjectData proofDetail = createProofDetailForAgreement(
                    agreementDetail, proofObj, tenantId, userId, proofId, activityId,
                    proofDisplayImgMap, proofImgIdProofProductDetailsMap,
                    productOrMaterialAchieveStatusMap, rioSpecialDisplayFormDTOMapGroupByActivityDetailId,
                    activityItemIdNameMap
            );

            proofDetails.add(proofDetail);
        }

        return proofDetails;
    }

    /**
     * 准备协议活动项ID和名称的映射
     */
    private Map<String, String> prepareAgreementActivityItemNameMap(String tenantId, List<IObjectData> agreementDetails) {
        List<String> activityItemIds = agreementDetails.stream()
                .map(detail -> detail.get(TPMActivityAgreementDetailFields.ACTIVITY_ITEM_ID, String.class))
                .distinct()
                .collect(Collectors.toList());

        List<IObjectData> activityItems = serviceFacade.findObjectDataByIds(tenantId, activityItemIds, ApiNames.TPM_ACTIVITY_ITEM_OBJ);

        return activityItems.stream()
                .collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));
    }


    private Map<String, IObjectData> queryRIODisplayProjectJudgmentStandard(String tenantId, List<IObjectData> details, boolean existAgreementNode) {
        if (!TPMGrayUtils.isRioTenant(tenantId) || CollectionUtils.isEmpty(details)) {
            return Collections.emptyMap();
        }

        Map<String, IObjectData> rioDisplayProjectJudgmentStandard = new HashMap<>();
        List<String> activityDetails;
        if (existAgreementNode) {
            activityDetails = details.stream().map(d -> d.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID, String.class)).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(activityDetails)) {
                return Collections.emptyMap();
            }
        } else {
            activityDetails = details.stream().map(DBRecord::getId).collect(Collectors.toList());
        }

        List<IObjectData> activityDetailObjs = serviceFacade.findObjectDataByIds(tenantId, activityDetails, ApiNames.TPM_ACTIVITY_DETAIL_OBJ);
        if (CollectionUtils.isEmpty(activityDetailObjs)) {
            return Collections.emptyMap();
        }

        Map<String, IObjectData> activityDetailObjMap = activityDetailObjs.stream().collect(Collectors.toMap(DBRecord::getId, activityDetail -> activityDetail));

        List<String> displayProjectJudgmentStandardIds = activityDetailObjs.stream()
                .filter(detail -> StringUtils.isNotBlank(detail.get(TPMActivityDetailFields.DISPLAY_PROJECT_JUDGMENT_STANDARD, String.class)))
                .map(detail -> detail.get(TPMActivityDetailFields.DISPLAY_PROJECT_JUDGMENT_STANDARD, String.class))
                .collect(Collectors.toList());

        List<IObjectData> displayProjectJudgmentStandardObjs = serviceFacade.findObjectDataByIds(tenantId, displayProjectJudgmentStandardIds, ApiNames.DISPLAY_PROJECT_JUDGMENT_STANDARD__C);
        if (CollectionUtils.isEmpty(displayProjectJudgmentStandardObjs)) {
            return Collections.emptyMap();
        }

        Map<String, IObjectData> displayProjectJudgmentStandardMap = displayProjectJudgmentStandardObjs.stream().collect(Collectors.toMap(DBRecord::getId, obj -> obj));

        for (IObjectData data : details) {
            String activityDetailId = existAgreementNode ? data.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID, String.class) : data.getId();
            String displayProjectJudgmentStandardId = activityDetailObjMap.get(activityDetailId).get(TPMActivityDetailFields.DISPLAY_PROJECT_JUDGMENT_STANDARD, String.class);
            if (StringUtils.isBlank(displayProjectJudgmentStandardId)) {
                continue;
            }
            if (!displayProjectJudgmentStandardMap.containsKey(displayProjectJudgmentStandardId)) {
                continue;
            }
            rioDisplayProjectJudgmentStandard.put(activityDetailId, displayProjectJudgmentStandardMap.get(displayProjectJudgmentStandardId));
        }

        return rioDisplayProjectJudgmentStandard;
    }

    private Map<String, ProductOrMaterialAchieveStatusDTO> queryProductOrMaterialAchieveStatus(String tenantId, String activityId, String agreementId, String proofId, IObjectData proofObj, boolean existAgreementNode) {
        DataReportAch.Arg arg = new DataReportAch.Arg();
        arg.setTenantId(tenantId);
        arg.setDataMainId(proofId);
        arg.setDataMainApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        arg.setCheckinsId(proofObj.get(TPMActivityProofFields.VISIT_ID, String.class, ""));
        arg.setBusinessDataApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        arg.setBusinessDataId(proofId);
        arg.setAccountId(getAccountIdOfProof(proofObj));

        Map<String, ProductOrMaterialAchieveStatusDTO> productOrMaterialAchieveStatusMap = new HashMap<>();
        if (existAgreementNode) {
            //协议
            arg.setStandardMainId(agreementId);
            arg.setStandardMainApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            if (StringUtils.isBlank(agreementId)) {
                return Collections.emptyMap();
            }
        } else {
            //活动
            arg.setStandardMainId(activityId);
            arg.setStandardMainApiName(ApiNames.TPM_ACTIVITY_OBJ);
            if (StringUtils.isBlank(activityId)) {
                return Collections.emptyMap();
            }
        }

        log.info("prodMatRes start:{}", JSON.toJSONString(arg));
        DataReportAch.Result result = fmcgCrmProxy.prodMatRes(Integer.parseInt(tenantId), -10000, arg);
        if (result.getCode() != 0 || result.getData().isEmpty()) {
            log.info("prodMatRes fail:{}", result.getMessage());
            return Collections.emptyMap();
        }
        log.info("prodMatRes success:{}", result.getData());

        result.getData().forEach((k, v) -> {
            String productAchieveStatus = revertAchieveStatus(tenantId, v.getProductDetail());
            String materialAchieveStatus = revertAchieveStatus(tenantId, v.getMaterialDetail());

            ProductOrMaterialAchieveStatusDTO materialAchieveStatusDTO = ProductOrMaterialAchieveStatusDTO.builder()
                    .proofId(proofId)
                    .dataId(k)
                    .productAchieveStatus(productAchieveStatus)
                    .materialAchieveStatus(materialAchieveStatus)
                    .build();
            productOrMaterialAchieveStatusMap.put(k, materialAchieveStatusDTO);
        });

        return productOrMaterialAchieveStatusMap;
    }

    private static String revertAchieveStatus(String tenantId, DataReportAch.ProjectAchievementDetail productDetail) {
        String productAchieveStatus = "";
        if (Objects.isNull(productDetail)) {
            return productAchieveStatus;
        }
        if (productDetail.isAchieved()) {
            productAchieveStatus = TPMActivityProofDetailFields.PASS_STATUS;
        } else if (productDetail.getAchievedCount() == 0) {
            productAchieveStatus = TPMActivityProofDetailFields.FAIL_STATUS;
        } else if (productDetail.getAchievedCount() < productDetail.getTotalCount()) {
            productAchieveStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
        }
        return productAchieveStatus;
    }

    /**
     * 验证参数有效性
     */
    private void validateParams(String tenantId, ActivityTypeExt activityType, String proofId) {
        if (!super.openAi(tenantId)) {
            throw new ValidateException("ai is not open");
        }

        ActivityProofAiConfigEntity aiConfig = activityType.proofNode().getActivityProofConfig().getAiConfig();
        if (Objects.isNull(aiConfig)) {
            throw new ValidateException("aiConfig is null");
        }
    }

    /**
     * 初始化处理上下文，加载所需的数据和配置
     */
    private AIProcessingContext initProcessingContext(String tenantId, String userId, ActivityTypeExt activityType, String proofId) {
        // 1. 查询举证图片
        List<IObjectData> proofDisplayImgs = queryTPMActivityProofDisplayImgsByProofIds(tenantId, Lists.newArrayList(proofId));
        if (CollectionUtils.isEmpty(proofDisplayImgs)) {
            log.info("proofDisplayImgs is empty, proofId: {}", proofId);
            return null;
        }

        // 2. 清理无需的详情
        invalidAndDeleteNotNeedDetails(tenantId, userId, proofId, proofDisplayImgs);

        // 3. 获取AI配置信息
        ActivityProofAiConfigEntity aiConfig = activityType.proofNode().getActivityProofConfig().getAiConfig();
        String modelId = aiConfig.getDisplayRecognitionModel();
        boolean existAgreementNode = Objects.nonNull(activityType.agreementNode());

        // 4. 查询AI规则
        BatchQueryAIRuleByIds.AIDetectRule aiRule = queryAIRule(tenantId, activityType);

        // 5. 查询举证对象
        IObjectData proofObj = serviceFacade.findObjectData(User.systemUser(tenantId), proofId, ApiNames.TPM_ACTIVITY_PROOF_OBJ);

        // 6. 加载陈列形式和项目字段映射
        GetDisplayScenesByModelId.Result displayScenesByModelIdRest = getDisplayScenesByModelIdRest(tenantId, modelId);
        //获取企业级陈列形式识别容错配置
        List<TenantDetectFaultTolerantForDisPlayForm> tenantDetectFaultTolerantForDisPlayFormList = queryTenantDetectFaultTolerantForDisPlayForm(tenantId, displayScenesByModelIdRest);
        Map<String, IObjectData> displayFormItemDTOMap = queryDisplayFormItemDTOMap(tenantId);

        // 7. 解析能力字段关系
        Map<String, Integer> detectCapabilityMap = aiRule.getDetectCapabilityMap();
        List<String> tpmDetectCapabilityList = DetectCapabilityEnum.getTPMDetectCapabilityList();
        Map<String, Integer> tpmDetectCapabilityMap = detectCapabilityMap.entrySet().stream()
                .filter(entry -> entry.getValue() == 1 && tpmDetectCapabilityList.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 8. 获取规则描述
        GetAIRuleDescription.Arg getAIRuleDescriptionArg = new GetAIRuleDescription.Arg();
        getAIRuleDescriptionArg.setModelId(modelId);
        GetAIRuleDescription.Result aiRuleDescribe = fmcgServiceProxy.getAIRuleDescribe(Integer.parseInt(tenantId), -10000, getAIRuleDescriptionArg);
        if (Objects.isNull(aiRuleDescribe)) {
            throw new ValidateException("ai rule describe is null");
        }

        // 9. 筛选识别能力字段关系
        Map<String, List<String>> tpmDetectCapabilityFieldRelationMap = aiRuleDescribe.getDetectCapabilityFieldRelationMap().entrySet().stream()
                .filter(entry -> tpmDetectCapabilityMap.containsKey(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 11. 创建上下文对象
        return new AIProcessingContext(
                tenantId, userId, proofId, modelId, existAgreementNode, proofObj.get(TPMActivityProofFields.AI_IDENTIFY_STATUS, String.class),
                proofDisplayImgs, proofObj, aiRule,
                displayFormItemDTOMap,
                tpmDetectCapabilityMap, tpmDetectCapabilityFieldRelationMap,
                tenantDetectFaultTolerantForDisPlayFormList
        );
    }

    private GetDisplayScenesByModelId.Result getDisplayScenesByModelIdRest(String tenantId, String modelId) {
        GetDisplayScenesByModelId.Arg arg = new GetDisplayScenesByModelId.Arg();
        arg.setModelId(modelId);
        return fmcgServiceProxy.getDisplayScenesByModelId(Integer.parseInt(tenantId), -10000, arg);
    }

    private List<TenantDetectFaultTolerantForDisPlayForm> queryTenantDetectFaultTolerantForDisPlayForm(String tenantId, GetDisplayScenesByModelId.Result displayScenesByModelIdRest) {

        Map<String, String> faultKeyRelationMap = TENANT_DISPLAY_DETECT_FAULT_TOLERANCE.get(tenantId);
        if (TPMGrayUtils.isRioTenant(tenantId)) {
            faultKeyRelationMap = Maps.of(RIOSpecialDisplayFormEnum.SHELF.getFieldKey(), RIOSpecialDisplayFormEnum.GE.getFieldKey());
        }

        if (Objects.isNull(faultKeyRelationMap) || faultKeyRelationMap.isEmpty()) {
            log.info("faultKeyRelationMap is empty, tenantId: {}", tenantId);
            return Collections.emptyList();
        }

        log.info("faultKeyRelationMap: {}", faultKeyRelationMap);

        List<TenantDetectFaultTolerantForDisPlayForm> tenantDetectFaultTolerantForDisPlayFormList = new ArrayList<>();

        faultKeyRelationMap.forEach((faultKey, reliabilityKey) -> {
            String faultValue = displayScenesByModelIdRest.getDisplayScenes().stream()
                    .filter(scene -> scene.getKey().equals(faultKey))
                    .findFirst()
                    .map(GetDisplayScenesByModelId.DisplaySceneDTO::getValue)
                    .orElse("");

            String reliabilityValue = displayScenesByModelIdRest.getDisplayScenes().stream()
                    .filter(scene -> scene.getKey().equals(reliabilityKey))
                    .findFirst()
                    .map(GetDisplayScenesByModelId.DisplaySceneDTO::getValue)
                    .orElse("");

            if (StringUtils.isBlank(faultValue) || StringUtils.isBlank(reliabilityValue)) {
                log.info("faultKey: {}, reliabilityKey: {}, faultValue: {}, reliabilityValue: {}", faultKey, reliabilityKey, faultValue, reliabilityValue);
                return;
            }
            tenantDetectFaultTolerantForDisPlayFormList.add(
                    TenantDetectFaultTolerantForDisPlayForm.builder()
                            .faultKeys(Lists.newArrayList(faultKey))
                            .reliabilityKey(reliabilityKey)
                            .faultValues(Lists.newArrayList(faultValue))
                            .reliabilityValue(reliabilityValue)
                            .build()
            );
        });

        return tenantDetectFaultTolerantForDisPlayFormList;
    }

    /**
     * 构建AI识别额外参数
     */
    private JSONObject buildExtraData(IObjectData proofObj, Map<String, Integer> tpmDetectCapabilityMap) {
        JSONObject extraData = new JSONObject();
        extraData.put("enable_recapture_classify", true);

        String accountId = getAccountIdOfProof(proofObj);

        extraData.put("account_id", accountId);
        extraData.put("scene_type", "hj");

        tpmDetectCapabilityMap.forEach((key, value) -> extraData.put(key, true));

        return extraData;
    }

    private static String getAccountIdOfProof(IObjectData proofObj) {
        String accountId = proofObj.get(TPMActivityProofFields.STORE_ID, String.class);
        if (StringUtils.isEmpty(accountId)) {
            accountId = proofObj.get(TPMActivityProofFields.DEALER_ID, String.class);
        }
        return accountId;
    }

    /**
     * 处理所有图片
     */
    private void processImages(AIProcessingContext context) {
        if (CollectionUtils.isEmpty(context.getProofDisplayImgs())) {
            return;
        }

        List<FailedImage> failedQueue = Collections.synchronizedList(new ArrayList<>());
        context.setFailedQueue(failedQueue);

        // 第一阶段：处理所有图片（初次处理，重试次数为0）
        context.getProofDisplayImgs().forEach(img -> processSingleDisplayFormImages(img, context, failedQueue, 0));
    }

    /**
     * 处理单张陈列形式下多个图片（包括初次处理和重试）
     *
     * @param img         图片对象
     * @param context     处理上下文
     * @param failedQueue 失败队列
     * @param retryCount  当前重试次数（0表示初次处理）
     * @return 处理是否成功
     */
    private boolean processSingleDisplayFormImages(IObjectData img, AIProcessingContext context, List<FailedImage> failedQueue, int retryCount) {
        ProcessingResult processingResult = null;

        StopWatch stopWatch = StopWatch.create(String.format("processSingleDisplayFormImages_%s", img.getId()));
        try {
            // 获取图片路径
            String displayFormId = img.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID, String.class);
            List<String> imgPaths = extractImagePaths(img);
            if (CollectionUtils.isEmpty(imgPaths)) {
                log.warn("图片路径为空，跳过处理，imgId: {}", img.getId());
                return true;  // 返回true表示处理完成，不再加入队列
            }

            // 如果是重试，记录日志并等待
            if (retryCount > 0) {
                log.info("重试识别图片，第{}次尝试，imgId: {}", retryCount, img.getId());
                // 重试前等待一段时间
                try {
                    Thread.sleep(200);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // 1. 尝试AI识别所有图片
            processingResult = detectImages(img, imgPaths, context);
            if (!processingResult.isSuccess()) {
                // 处理失败，决定是否重试
                if (retryCount < 3) {  // 最大重试3次
                    // 加入失败队列
                    failedQueue.add(new FailedImage(img, retryCount));
                } else {
                    // 达到最大重试次数，记录失败
                    context.setProofAiStatus(TPMActivityProofFields.AI_IDENTIFY_STATUS);
                    recordFailedImage(img, context, String.format(AI_ERROR_MESSAGE_PREFIX + ":%s", processingResult.getErrorMessage()));
                }
                return false;
            }

            stopWatch.lap("detectImages");

            // 2. 处理识别结果
            List<DetectResultDTO> detectResultDTOS = processingResult.getDetectResultDTOS();
            List<ActivityDisplayImgPO> addActivityDisplayImgPOs = processingResult.getAddActivityDisplayImgPOs();
            Map<String, String> productIdCategoryIdMap = getProductIdCategoryIdMap(context.getTenantId(), processingResult.getProductIdList().stream().distinct().collect(Collectors.toList()));
            stopWatch.lap("handle detectResultDTOS");

            // 3. 提取AI识别结果（陈列图片）
            AIDetectionResults aiResults = extractAIDetectionResults(context.getTenantId(), detectResultDTOS, img.getId(), displayFormId);
            Map<String, AIDetectionResults> aiResultsMap = context.getAiResultsMapGroupByProofDisplayImgId();
            aiResultsMap.putIfAbsent(img.getId(), aiResults);
            log.info("processSingleDisplayFormImages aiResultsMap size：{}", aiResultsMap.size());
            stopWatch.lap("get aiResultsMap");

            // 4. 创建物料和产品明细
            List<IObjectData> proofProductDetails = buildProofProductDetails(context, detectResultDTOS, productIdCategoryIdMap, displayFormId, img.getId());
            List<IObjectData> proofMaterialDetails = buildProofMaterialDetails(context, detectResultDTOS, displayFormId, img.getId());
            log.info("processSingleDisplayFormImages proofProductDetails：{}，proofMaterialDetails：{}", proofProductDetails.size(), proofMaterialDetails.size());
            stopWatch.lap("get proofProductDetails and proofMaterialDetails");

            // 5. 保存处理结果,DAO、产品、物料明细
            saveProcessingResults(
                    context.getTenantId(),
                    context.getUserId(),
                    context.getProofObj(),
                    addActivityDisplayImgPOs,
                    proofProductDetails,
                    proofMaterialDetails
            );

            stopWatch.lap("saveProcessingResults");
            stopWatch.log();
            return true;
        } catch (Exception e) {
            log.error("处理举证图片异常，proofId: {}, imgId: {}, retryCount: {}",
                    context.getProofId(), img.getId(), retryCount, e);

            // 处理失败，决定是否重试
            if (retryCount < 3) {  // 最大重试3次
                // 加入失败队列
                failedQueue.add(new FailedImage(img, retryCount));
            } else {
                // 达到最大重试次数，记录失败
                recordFailedImage(img, context, String.format(TPM_ERROR_MESSAGE_PREFIX + ":%s", e.getMessage()));
            }
            return false;
        }
    }

    private Map<String, String> getProductIdCategoryIdMap(String tenantId, List<String> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return Collections.emptyMap();
        }
        List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds(tenantId, productIdList, ApiNames.PRODUCT_OBJ);
        return objectDataByIds.stream()
                .filter(product -> StringUtils.isNotBlank(product.get(ProductFields.PRODUCT_CATEGORY_ID, String.class)))
                .collect(Collectors.toMap(DBRecord::getId, o -> o.get(ProductFields.PRODUCT_CATEGORY_ID, String.class)));
    }

    private List<IObjectData> buildProofMaterialDetails(AIProcessingContext context, List<DetectResultDTO> detectResultDTOS, String displayFormId, String imgId) {
        DetectCapabilityEnum detectCapabilityEnum = DetectCapabilityEnum.IS_POSM_DETECT;

        // 计算物料数
        List<TPMMaterialAiNumberDTO> materialAiNumber = calculateMaterialAiNumber(context.getTenantId(), displayFormId, detectResultDTOS);

        Map<String, List<BatchQueryAIRuleByIds.Field>> objectFieldListMap = getObjectFieldListMap(context, detectCapabilityEnum);

        String proofId = context.getProofId();
        String userId = context.getUserId();
        IObjectData proofObj = context.getProofObj();

        return processMaterialDetect(materialAiNumber, objectFieldListMap, proofId, imgId, userId, proofObj);
    }

    private List<IObjectData> buildProofProductDetails(AIProcessingContext context, List<DetectResultDTO> detectResultDTOS, Map<String, String> productIdCategoryIdMap, String displayFormId, String imgId) {
        DetectCapabilityEnum detectCapabilityEnum = DetectCapabilityEnum.IS_OPEN_PRODUCT_ROW_NUMBER;

        Map<String, List<BatchQueryAIRuleByIds.Field>> objectFieldListMap = getObjectFieldListMap(context, detectCapabilityEnum);

        // 计算排面数
        List<TPMProductSkuRowNumberDTO> productSkuRowNumber = calculateProductSkuRowNumber(
                context.getTenantId(),
                displayFormId,
                context.getTenantDetectFaultTolerantForDisPlayFormList(),
                detectResultDTOS,
                objectFieldListMap
        );

        log.info("buildProofProductDetails displayFormId: {}, productSkuRowNumber：{}", displayFormId, productSkuRowNumber.size());

        String proofId = context.getProofId();
        String userId = context.getUserId();
        IObjectData proofObj = context.getProofObj();

        return processProductRowNumber(productSkuRowNumber, objectFieldListMap, proofId, imgId, productIdCategoryIdMap, userId, proofObj);
    }

    @NotNull
    private static Map<String, List<BatchQueryAIRuleByIds.Field>> getObjectFieldListMap(AIProcessingContext context, DetectCapabilityEnum detectCapabilityEnum) {
        Map<String, Integer> tpmDetectCapabilityMap = context.getTpmDetectCapabilityMap();
        Map<String, List<String>> tpmDetectCapabilityFieldRelationMap = context.getTpmDetectCapabilityFieldRelationMap();
        BatchQueryAIRuleByIds.AIDetectRule aiRule = context.getAiRule();
        Map<String, List<BatchQueryAIRuleByIds.Field>> objectFieldListMap = new HashMap<>();
        boolean containsKey = tpmDetectCapabilityMap.containsKey(detectCapabilityEnum.getKey());
        if (containsKey) {
            // 从规则描述中获取需要哪些AI识别字段
            List<String> tpmDetectCapabilityFields = tpmDetectCapabilityFieldRelationMap.getOrDefault(
                    detectCapabilityEnum.getKey(), Collections.emptyList());

            if (CollectionUtils.isNotEmpty(tpmDetectCapabilityFields)) {
                // 从AI规则中获取AI识别字段关联的对象字段
                Map<String, BatchQueryAIRuleByIds.Field> currentFieldMap = aiRule.getFieldMap().entrySet().stream()
                        .filter(entry -> tpmDetectCapabilityFields.contains(entry.getKey()) && !AI_DETECT_EXCLUDE_FIELD_LIST.contains(entry.getKey()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

                objectFieldListMap = currentFieldMap.values()
                        .stream()
                        .collect(Collectors.groupingBy(BatchQueryAIRuleByIds.Field::getObjectApiName));
            }
        }
        return objectFieldListMap;
    }

    /**
     * 尝试AI识别一组图片
     */
    private ProcessingResult detectImages(IObjectData img, List<String> imgPaths, AIProcessingContext context) {
        List<ActivityDisplayImgPO> addActivityDisplayImgPOs = new ArrayList<>();
        List<DetectResultDTO> detectResultDTOS = new ArrayList<>();
        List<String> productIdList = Lists.newArrayList();
        // 10. 组装额外数据
        JSONObject extraData = buildExtraData(context.getProofObj(), context.getTpmDetectCapabilityMap());
        for (String path : imgPaths) {
            log.info("处理图片识别，imgId: {}, path: {}", img.getId(), path);
            try {
                Detect.Result detectResult = callAIService(context.getTenantId(), path, context.getModelId(), extraData);

                // 识别成功
                if (detectResult.getErrorCode() == 0) {
                    log.info("图片识别成功，imgId: {}, path: {}", img.getId(), path);
                    detectResultDTOS.add(detectResult.getData());
                    List<String> productIds = extractProductInfo(detectResult);
                    if (CollectionUtils.isNotEmpty(productIds)) {
                        productIdList.addAll(productIds);
                    }
                    addActivityDisplayImgPOs.add(buildActivityDisplayImgPOForAddOrUpdate(
                            context.getTenantId(), context.getProofId(), img, path, context.getProofObj(), detectResult));
                } else {
                    // 识别失败
                    log.warn("图片识别失败，imgId: {}, path: {}, errorCode: {}, errorMessage: {}",
                            img.getId(), path, detectResult.getErrorCode(), detectResult.getErrorMessage());

                    // 一旦有失败的，标记整体失败
                    doClearCollections(addActivityDisplayImgPOs, detectResultDTOS);
                    return new ProcessingResult(false, detectResultDTOS, addActivityDisplayImgPOs, productIdList, detectResult.getErrorMessage());
                }
            } catch (Exception e) {
                // 识别发生异常
                log.error("图片识别异常，imgId: {}, path: {}", img.getId(), path, e);

                // 一旦有异常，标记整体失败
                doClearCollections(addActivityDisplayImgPOs, detectResultDTOS);
                return new ProcessingResult(false, detectResultDTOS, addActivityDisplayImgPOs, productIdList, e.getMessage());
            }
        }

        return new ProcessingResult(true, detectResultDTOS, addActivityDisplayImgPOs, productIdList, "");
    }

    /**
     * 调用AI服务进行识别
     */
    private Detect.Result callAIService(String tenantId, String path, String modelId, JSONObject extraData) {
        int tenantIdIntValue = Integer.parseInt(tenantId);
        String tenantAccount = eieaConverter.enterpriseIdToAccount(tenantIdIntValue);
        SdkContext sdkContext = SdkContext.createInstance(tenantIdIntValue, tenantAccount, -10000);

        Detect.Arg detectArg = new Detect.Arg();
        detectArg.setPath(path);
        detectArg.setScene("new_number_form");
        detectArg.setModelId(modelId);
        //detectArg.setMustRecord(ConfigFactory.getConfig("fs-fmcg-tpm-config").getBool("ai_must_record", true));
        detectArg.setMustRecord(false);
        detectArg.setExtraData(extraData.toJSONString());

        return detectClient.detect(sdkContext, detectArg);
    }

    /**
     * 从检测结果中提取产品id
     */
    private List<String> extractProductInfo(Detect.Result detectResult) {
        List<String> productIdList = new ArrayList<>();
        Map<String, Map<String, JSONObject>> objectData = detectResult.getObjectData();
        if (!objectData.isEmpty() && !objectData.get("ProductObj").isEmpty()) {
            Map<String, JSONObject> productObj = objectData.get("ProductObj");
            if (productObj.isEmpty()) {
                return new ArrayList<>();
            }
            productIdList = new ArrayList<>(productObj.keySet());
        }
        return productIdList;
    }

    /**
     * 从检测结果中提取AI识别信息
     */
    private AIDetectionResults extractAIDetectionResults(String tenantId, List<DetectResultDTO> detectResultDTOS, String imgId, String displayFormId) {
        List<String> aiDisplayFormIdList = new ArrayList<>();
        AtomicInteger aiLayerNumber = new AtomicInteger(0);
        AtomicInteger aiGroupNumber = new AtomicInteger(0);
        AtomicInteger aiVisibleNumber = new AtomicInteger(0);

        List<SceneInfoDTO> sceneInfoDTOList = new ArrayList<>();
        for (DetectResultDTO detectResultDTO : detectResultDTOS) {
            Map<String, List<SceneInfoDTO>> sceneInfoMap = detectResultDTO.getSceneInfoMap();
            if (sceneInfoMap.isEmpty()) {
                continue;
            }
            aiDisplayFormIdList.addAll(sceneInfoMap.keySet());
            if (!sceneInfoMap.containsKey(displayFormId)) {
                continue;
            }

            if (TPMGrayUtils.isRioTenant(tenantId)) {
                sceneInfoDTOList.addAll(sceneInfoMap.values().stream()
                        .flatMap(List::stream)
                        .collect(Collectors.toList()));
            } else {
                sceneInfoDTOList.addAll(sceneInfoMap.get(displayFormId));
            }
        }

        sceneInfoDTOList.stream()
                .filter(sceneInfo -> sceneInfo.getDisplayTotalLayerNumber() != null)
                .max(Comparator.comparingInt(SceneInfoDTO::getDisplayTotalLayerNumber))
                .ifPresent(sceneInfo -> aiLayerNumber.set(sceneInfo.getDisplayTotalLayerNumber()));

        sceneInfoDTOList.stream()
                .filter(sceneInfo -> sceneInfo.getDisplayCutBoxGroupNumber() != null)
                .max(Comparator.comparingInt(SceneInfoDTO::getDisplayCutBoxGroupNumber))
                .ifPresent(sceneInfo -> aiGroupNumber.set(sceneInfo.getDisplayCutBoxGroupNumber()));

        //堆头 需要将所有的可视面求和
        //堆头1 ： 可视面=1  堆头2 ： 可视面=2。落到库中 堆头：可视面=1+2
        sceneInfoDTOList.stream()
                .filter(sceneInfo -> sceneInfo.getDisplayMaxVisibleNumber() != null)
                .forEach(sceneInfo -> aiVisibleNumber.addAndGet(sceneInfo.getDisplayMaxVisibleNumber()));

        //返回的堆头的个数 只有堆头会返回可视面
        int pileHeadCount = (int) sceneInfoDTOList.stream()
                .filter(sceneInfo -> sceneInfo.getDisplayMaxVisibleNumber() != null)
                .count();

        Map<String, Long> countGroupByDisplayId = sceneInfoDTOList.stream()
                .filter(sceneInfo -> sceneInfo.getScene() != null)
                .collect(Collectors.groupingBy(
                        SceneInfoDTO::getScene,
                        Collectors.counting()
                ));

        AIDetectionResults aiDetectionResults = new AIDetectionResults(aiDisplayFormIdList.stream().distinct().collect(Collectors.toList()),
                imgId, aiLayerNumber.get(), aiGroupNumber.get(), aiVisibleNumber.get(), pileHeadCount, countGroupByDisplayId);
        log.info("aiDetectionResults:{}", JSON.toJSONString(aiDetectionResults));
        return aiDetectionResults;
    }

    private static String getDisplayFormSpecialItemIdByTenantId(String tenantId, String key) {
        Map<String, String> displayDisplayFormRowLayerNumber = TENANT_DISPLAY_FORM_ROW_LAYER_NUMBER.get(tenantId);
        if (Objects.isNull(displayDisplayFormRowLayerNumber) || displayDisplayFormRowLayerNumber.isEmpty()) {
            log.info("displayDisplayFormRowLayerNumber is empty. tenant: {}, key: {}", tenantId, key);
            return "";
        }
        return displayDisplayFormRowLayerNumber.get(key);
    }

    /**
     * 处理产品排面数
     */
    private List<IObjectData> processProductRowNumber(
            List<TPMProductSkuRowNumberDTO> productSkuRowNumber,
            Map<String, List<BatchQueryAIRuleByIds.Field>> objectFieldListMap,
            String proofId,
            String imgId,
            Map<String, String> productIdCategoryIdMap,
            String userId,
            IObjectData proofObj) {

        List<IObjectData> proofProductDetails = Lists.newArrayList();

        for (TPMProductSkuRowNumberDTO productSkuRowNumberDTO : productSkuRowNumber) {
            objectFieldListMap.forEach((objectApiName, fields) -> {
                String productId = productSkuRowNumberDTO.getProductId();

                // 映射到对象中会有多个对象
                IObjectData proofProductDetail = buildDefaultIObjectData(userId, objectApiName, proofObj);

                if (ApiNames.TPM_ACTIVITY_PROOF_PRODUCT_DETAIL_OBJ.equals(objectApiName)) {
                    // 设置关联ID
                    proofProductDetail.set(TPMActivityProofProductDetailFields.ACTIVITY_PROOF_ID, proofId);


                    proofProductDetail.set(TPMActivityProofProductDetailFields.ACTIVITY_ITEM_ID, getDisplayFormSpecialItemIdByTenantId(proofObj.getTenantId(), DisplayFormItemEnum.ROW_NUMBER.getFieldKey()));
                    proofProductDetail.set(TPMActivityProofProductDetailFields.DISPLAY_FORM_ID, productSkuRowNumberDTO.getDisplayFormId());
                    proofProductDetail.set(TPMActivityProofProductDetailFields.ACTIVITY_PROOF_DISPLAY_IMG_ID, imgId);
                    proofProductDetail.set(TPMActivityProofProductDetailFields.PRODUCT_CATEGORY_ID, productIdCategoryIdMap.get(productId));
                }

                for (BatchQueryAIRuleByIds.Field field : fields) {
                    String fieldKey = field.getFieldKey();
                    switch (fieldKey) {
                        case "aiRowNumber":
                            // 设置产品相关字段
                            proofProductDetail.set(field.getAiStoreFieldApiName(), productSkuRowNumberDTO.getRowNumber());
                            break;
                        case "productName":
                            // 设置产品相关字段
                            proofProductDetail.set(field.getAiStoreFieldApiName(), productId);
                            break;
                        case "aiPath":
                            continue;
                    }
                }

                proofProductDetails.add(proofProductDetail);
            });
        }

        return proofProductDetails;
    }

    /**
     * 处理物料检测
     */
    private List<IObjectData> processMaterialDetect(
            List<TPMMaterialAiNumberDTO> materialAiNumber,
            Map<String, List<BatchQueryAIRuleByIds.Field>> objectFieldListMap,
            String proofId,
            String imgId,
            String userId,
            IObjectData proofObj) {
        List<IObjectData> proofMaterialDetails = Lists.newArrayList();

        for (TPMMaterialAiNumberDTO materialAiNumberDTO : materialAiNumber) {
            objectFieldListMap.forEach((objectApiName, fields) -> {
                // 映射到对象中会有多个对象
                IObjectData proofMaterialDetail = buildDefaultIObjectData(userId, objectApiName, proofObj);

                if (ApiNames.TPM_ACTIVITY_PROOF_MATERIAL_DETAIL_OBJ.equals(objectApiName)) {
                    // 设置关联ID
                    proofMaterialDetail.set(TPMActivityProofMaterialDetailFields.ACTIVITY_PROOF_ID, proofId);
                    proofMaterialDetail.set(TPMActivityProofMaterialDetailFields.DISPLAY_FORM_ID, materialAiNumberDTO.getDisplayFormId());
                    proofMaterialDetail.set(TPMActivityProofMaterialDetailFields.ACTIVITY_PROOF_DISPLAY_IMG_ID, imgId);
                }

                for (BatchQueryAIRuleByIds.Field field : fields) {
                    String fieldKey = field.getFieldKey();
                    switch (fieldKey) {
                        case "posmName":
                            // 设置物料基本字段
                            proofMaterialDetail.set(field.getAiStoreFieldApiName(), materialAiNumberDTO.getObjectId());
                            break;
                        case "posmNumber":
                            // 设置AI识别结果
                            proofMaterialDetail.set(field.getAiStoreFieldApiName(), materialAiNumberDTO.getCount());
                            break;
                    }
                }

                proofMaterialDetails.add(proofMaterialDetail);
            });
        }
        return proofMaterialDetails;
    }

    /**
     * 保存处理结果
     */
    private void saveProcessingResults(
            String tenantId,
            String userId,
            IObjectData proofObj,
            List<ActivityDisplayImgPO> addActivityDisplayImgPOs,
            List<IObjectData> proofProductDetails,
            List<IObjectData> proofMaterialDetails) {

        doAddActivityDisplayImgPOs(tenantId, addActivityDisplayImgPOs, proofObj);
        serviceFacade.bulkSaveObjectData(proofProductDetails, User.builder().tenantId(tenantId).userId(userId).build());
        serviceFacade.bulkSaveObjectData(proofMaterialDetails, User.builder().tenantId(tenantId).userId(userId).build());
    }

    /**
     * 处理失败重试队列
     */
    private void processFailedQueue(AIProcessingContext context) {

        List<FailedImage> failedQueue = context.getFailedQueue();
        if (CollectionUtils.isEmpty(failedQueue)) {
            return;
        }

        log.info("开始处理识别失败队列，失败数量: {}", failedQueue.size());

        // 创建重试队列的副本，防止循环中修改原队列导致问题
        List<FailedImage> workingQueue = new ArrayList<>(failedQueue);
        failedQueue.clear();

        // 处理所有失败图片
        for (FailedImage failedImage : workingQueue) {
            // 增加重试次数
            failedImage.retryCount++;

            // 使用相同的处理逻辑，传入当前重试次数
            processSingleDisplayFormImages(failedImage.img, context, failedQueue, failedImage.retryCount);
        }

        // 递归处理新的失败队列，直到队列为空
        if (!failedQueue.isEmpty()) {
            processFailedQueue(context);
        }
    }

    /**
     * 记录达到最大重试次数的失败图片
     */
    private void recordFailedImage(IObjectData img, AIProcessingContext context, String errorMessage) {
        log.error("图片达到最大重试次数，放弃识别，imgId: {}", img.getId());

        // 获取图片路径
        List<String> imgPaths = extractImagePaths(img);
        if (!CollectionUtils.isEmpty(imgPaths)) {
            // 为第一个路径创建失败记录
            String path = imgPaths.get(0);
            Detect.Result errorResult = new Detect.Result();
            errorResult.setErrorCode(-1);
            errorResult.setErrorMessage(errorMessage);

            ActivityDisplayImgPO po = buildActivityDisplayImgPOForAddOrUpdate(
                    context.getTenantId(), context.getProofId(), img, path, context.getProofObj(), errorResult);

            activityDisplayImgDAO.add(context.getTenantId(), Integer.parseInt(context.getProofObj().getCreatedBy()), po);
        }
    }

    private List<TPMMaterialAiNumberDTO> calculateMaterialAiNumber(String tenantId, String displayFormId, List<DetectResultDTO> detectResultDTOS) {

        // 参数校验
        if (CollectionUtils.isEmpty(detectResultDTOS) || StringUtils.isBlank(displayFormId)) {
            log.info("calculateMaterialAiNumber 参数校验失败， tenantId: {}, displayFormId: {}, detectResultDTOS: {}",
                    tenantId, displayFormId, detectResultDTOS);
            return Collections.emptyList();
        }

        // 收集物料AI识别数据，按物料ID分组
        Map<String, List<Double>> materialAiNumberMap = detectResultDTOS.stream()
                .filter(Objects::nonNull)
                .flatMap(detectResult -> detectResult.getPosm().stream())
                .filter(posm -> StringUtils.isNotBlank(posm.getScene())
                        && displayFormId.equals(posm.getScene())
                        && StringUtils.isNotBlank(posm.getObjectId()))
                .collect(Collectors.groupingBy(
                        PosmDetailDTO::getObjectId,
                        Collectors.mapping(PosmDetailDTO::getCount, Collectors.toList())
                ));

        // 如果没有收集到数据，返回空列表
        if (materialAiNumberMap.isEmpty()) {
            return Collections.emptyList();
        }

        // 处理每个物料的数据，取每个物料的最大数量值
        return materialAiNumberMap.entrySet().stream()
                .map(entry -> {
                    String objectId = entry.getKey();
                    List<Double> countList = entry.getValue();

                    // 获取最大值
                    double maxCount = countList.stream()
                            .mapToDouble(Double::doubleValue)
                            .max()
                            .orElse(0.0);

                    // 创建并返回DTO对象
                    TPMMaterialAiNumberDTO dto = new TPMMaterialAiNumberDTO();
                    dto.setObjectId(objectId);
                    dto.setCount(maxCount);
                    dto.setDisplayFormId(displayFormId);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    private Map<String, IObjectData> queryDisplayFormItemDTOMap(String tenantId) {
        //TODO  目前写死配置文件，待优化
        Map<String, IObjectData> result = Maps.newHashMap();
        return result;
    }

    private List<TPMProductSkuRowNumberDTO> calculateProductSkuRowNumber(
            String tenantId,
            String displayFormId,
            List<TenantDetectFaultTolerantForDisPlayForm> tenantDetectFaultTolerantForDisPlayFormList,
            List<DetectResultDTO> detectResultDTOS,
            Map<String, List<BatchQueryAIRuleByIds.Field>> objectFieldListMap
    ) {

        if (CollectionUtils.isEmpty(detectResultDTOS)) {
            return Lists.newArrayList();
        }

        BatchQueryAIRuleByIds.Field aiRowNumberField = objectFieldListMap.values().stream()
                .flatMap(List::stream)
                .filter(field -> "aiRowNumber".equals(field.getFieldKey()))
                .findFirst()
                .orElseThrow(null);

        if (Objects.isNull(aiRowNumberField)) {
            return Lists.newArrayList();
        }

        Integer calculateType = aiRowNumberField.getCalculateType();

        Map<String, List<TPMProductSkuRowNumberDTO>> effectiveNPathTPMProductSkuRowNumberDTOMap = getEffectiveNPathTPMProductSkuRowNumberDTOMap(tenantId, displayFormId, tenantDetectFaultTolerantForDisPlayFormList, detectResultDTOS);

        // 第1步：对每个路径下的数据按displayFormId+productId分组求和rowNumber
        Map<String, List<TPMProductSkuRowNumberDTO>> groupedByPathResult = new HashMap<>();

        if (calculateType == 3) {
            for (Map.Entry<String, List<TPMProductSkuRowNumberDTO>> entry : effectiveNPathTPMProductSkuRowNumberDTOMap.entrySet()) {
                String path = entry.getKey();
                List<TPMProductSkuRowNumberDTO> pathProducts = entry.getValue();

                // 按displayFormId+productId分组
                Map<String, List<TPMProductSkuRowNumberDTO>> groupedByDisplayAndProduct = pathProducts.stream()
                        .collect(Collectors.groupingBy(dto -> dto.getDisplayFormId() + ":" + dto.getProductId()));

                // 对每组数据求和rowNumber
                List<TPMProductSkuRowNumberDTO> summedList = new ArrayList<>();
                for (Map.Entry<String, List<TPMProductSkuRowNumberDTO>> group : groupedByDisplayAndProduct.entrySet()) {
                    String[] keys = group.getKey().split(":");
                    String groupDisplayFormId = keys[0];
                    String productId = keys[1];

                    double sumRowNumber = group.getValue().stream()
                            .mapToDouble(TPMProductSkuRowNumberDTO::getRowNumber)
                            .sum();

                    TPMProductSkuRowNumberDTO sumDto = TPMProductSkuRowNumberDTO.builder()
                            .displayFormId(groupDisplayFormId)
                            .productId(productId)
                            .rowNumber(sumRowNumber)
                            .build();

                    summedList.add(sumDto);
                }

                groupedByPathResult.put(path, summedList);
            }
        } else if (calculateType == 5) {
            for (Map.Entry<String, List<TPMProductSkuRowNumberDTO>> entry : effectiveNPathTPMProductSkuRowNumberDTOMap.entrySet()) {
                String path = entry.getKey();
                List<TPMProductSkuRowNumberDTO> pathProducts = entry.getValue();

                // 按displayFormId+productId分组
                Map<String, List<TPMProductSkuRowNumberDTO>> groupedByDisplayAndProduct = pathProducts.stream()
                        .collect(Collectors.groupingBy(dto -> dto.getDisplayFormId() + ":" + dto.getProductId()));

                List<TPMProductSkuRowNumberDTO> summedList = new ArrayList<>();
                for (Map.Entry<String, List<TPMProductSkuRowNumberDTO>> group : groupedByDisplayAndProduct.entrySet()) {
                    String[] keys = group.getKey().split(":");
                    String groupDisplayFormId = keys[0];
                    String productId = keys[1];
                    group.getValue().forEach(dto -> {
                        TPMProductSkuRowNumberDTO sumDto = TPMProductSkuRowNumberDTO.builder()
                                .displayFormId(groupDisplayFormId)
                                .productId(productId)
                                .rowNumber(dto.getRowNumber())
                                .build();

                        summedList.add(sumDto);
                    });

                }

                groupedByPathResult.put(path, summedList);
            }
        }

        // 第2步：对所有路径中的所有displayFormId+productId分组，找出每组rowNumber最大的
        Map<String, TPMProductSkuRowNumberDTO> maxRowNumberMap = new HashMap<>();

        // 汇总所有路径的数据
        for (List<TPMProductSkuRowNumberDTO> pathProducts : groupedByPathResult.values()) {
            for (TPMProductSkuRowNumberDTO dto : pathProducts) {
                String key = dto.getDisplayFormId() + ":" + dto.getProductId();

                // 如果当前产品的rowNumber比已存在的大，或者该key还不存在，则更新/添加
                if (!maxRowNumberMap.containsKey(key) || dto.getRowNumber() > maxRowNumberMap.get(key).getRowNumber()) {
                    maxRowNumberMap.put(key, dto);
                }
            }
        }

        // 转换最终结果为List

        return new ArrayList<>(maxRowNumberMap.values());
    }

    @NotNull
    private static Map<String, List<TPMProductSkuRowNumberDTO>> getEffectiveNPathTPMProductSkuRowNumberDTOMap(String tenantId, String displayFormId, List<TenantDetectFaultTolerantForDisPlayForm> tenantDetectFaultTolerantForDisPlayFormList, List<DetectResultDTO> detectResultDTOS) {
        //应拍陈列形式数据
        Map<String, List<TPMProductSkuRowNumberDTO>> effectiveNPathTPMProductSkuRowNumberDTOMap = Maps.newHashMap();
        for (DetectResultDTO detectResultDTO : detectResultDTOS) {
            List<TPMProductSkuRowNumberDTO> tpmProductSkuRowNumberDTOS = Lists.newArrayList();
            for (JSONObject jsonObject : detectResultDTO.getFormData()) {
                String scene = jsonObject.getString("scene");
                if (StringUtils.isNotBlank(scene) && scene.equals(displayFormId)) {
                    TPMProductSkuRowNumberDTO tpmProductSkuRowNumberDTO = TPMProductSkuRowNumberDTO.builder().productId(jsonObject.getString("product_id"))
                            .displayFormId(displayFormId)
                            .rowNumber(jsonObject.getDouble("row_number"))
                            .build();
                    tpmProductSkuRowNumberDTOS.add(tpmProductSkuRowNumberDTO);
                }
            }
            effectiveNPathTPMProductSkuRowNumberDTOMap.put(detectResultDTO.getPath(), tpmProductSkuRowNumberDTOS);
        }

        if (TPMGrayUtils.isRioTenant(tenantId)) {
            for (DetectResultDTO detectResultDTO : detectResultDTOS) {
                String path = detectResultDTO.getPath();
                //单张照片中，同SKU不同陈列形式识别，需要SKU排面数求和。陈列形式为应拍陈列形式
                boolean existMainShelf = detectResultDTO.getFormData().stream().anyMatch(jsonObject -> displayFormId.equals(jsonObject.getString("scene")));
                if (existMainShelf) {
                    List<String> productIdList = detectResultDTO.getFormData().stream()
                            .filter(jsonObject -> displayFormId.equals(jsonObject.getString("scene")))
                            .map(jsonObject -> jsonObject.getString("product_id"))
                            .distinct()
                            .collect(Collectors.toList());
                    List<TPMProductSkuRowNumberDTO> tpmProductSkuRowNumberDTOS = Lists.newArrayList();
                    detectResultDTO.getFormData().forEach(jsonObject -> {
                        String scene = jsonObject.getString("scene");
                        String productId = jsonObject.getString("product_id");
                        if (!scene.equals(displayFormId) && productIdList.contains(productId)) {
                            TPMProductSkuRowNumberDTO tpmProductSkuRowNumberDTO = TPMProductSkuRowNumberDTO.builder().productId(productId)
                                    .displayFormId(displayFormId)
                                    .rowNumber(jsonObject.getDouble("row_number"))
                                    .build();
                            tpmProductSkuRowNumberDTOS.add(tpmProductSkuRowNumberDTO);
                        }
                    });

                    if (CollectionUtils.isNotEmpty(tpmProductSkuRowNumberDTOS)) {
                        if (!effectiveNPathTPMProductSkuRowNumberDTOMap.containsKey(path)) {
                            effectiveNPathTPMProductSkuRowNumberDTOMap.put(path, tpmProductSkuRowNumberDTOS);
                        } else {
                            effectiveNPathTPMProductSkuRowNumberDTOMap.get(path).addAll(tpmProductSkuRowNumberDTOS);
                        }
                    }
                }
            }
        }

        //容错处理
        if (CollectionUtils.isNotEmpty(tenantDetectFaultTolerantForDisPlayFormList)) {
            Map<String, List<String>> detectFaultTolerantForDisPlayFormMap = tenantDetectFaultTolerantForDisPlayFormList.stream().collect(Collectors.toMap(TenantDetectFaultTolerantForDisPlayForm::getReliabilityValue, TenantDetectFaultTolerantForDisPlayForm::getFaultValues, (a, b) -> a));
            for (DetectResultDTO detectResultDTO : detectResultDTOS) {
                String path = detectResultDTO.getPath();
                if (detectFaultTolerantForDisPlayFormMap.containsKey(displayFormId)) {
                    List<String> detectFaultTolerantForDisPlayFormMapOrDefault = detectFaultTolerantForDisPlayFormMap.getOrDefault(displayFormId, Lists.newArrayList());
                    if (CollectionUtils.isEmpty(detectFaultTolerantForDisPlayFormMapOrDefault)) {
                        continue;
                    }
                    List<TPMProductSkuRowNumberDTO> tpmProductSkuRowNumberDTOS = Lists.newArrayList();
                    detectResultDTO.getFormData().forEach(jsonObject -> {
                        String scene = jsonObject.getString("scene");
                        if (!scene.equals(displayFormId) && detectFaultTolerantForDisPlayFormMapOrDefault.contains(scene)) {
                            TPMProductSkuRowNumberDTO tpmProductSkuRowNumberDTO = TPMProductSkuRowNumberDTO.builder().productId(jsonObject.getString("product_id"))
                                    .displayFormId(displayFormId)
                                    .rowNumber(jsonObject.getDouble("row_number"))
                                    .build();
                            tpmProductSkuRowNumberDTOS.add(tpmProductSkuRowNumberDTO);
                        }
                    });
                    if (CollectionUtils.isNotEmpty(tpmProductSkuRowNumberDTOS)) {
                        if (!effectiveNPathTPMProductSkuRowNumberDTOMap.containsKey(path)) {
                            effectiveNPathTPMProductSkuRowNumberDTOMap.put(path, tpmProductSkuRowNumberDTOS);
                        } else {
                            effectiveNPathTPMProductSkuRowNumberDTOMap.get(path).addAll(tpmProductSkuRowNumberDTOS);
                        }
                    }
                }
            }
        }
        return effectiveNPathTPMProductSkuRowNumberDTOMap;
    }

    private void doAddActivityDisplayImgPOs(String tenantId, List<ActivityDisplayImgPO> addActivityDisplayImgPOs, IObjectData proofObj) {
        for (ActivityDisplayImgPO activityDisplayImgPO : addActivityDisplayImgPOs) {
            activityDisplayImgDAO.add(tenantId, Integer.parseInt(proofObj.getCreatedBy()), activityDisplayImgPO);
        }
    }

    private void doClearCollections(List<ActivityDisplayImgPO> addActivityDisplayImgPOs, List<DetectResultDTO> detectResultDTOS) {
        addActivityDisplayImgPOs.clear();
        detectResultDTOS.clear();
    }

    private void invalidAndDeleteNotNeedDetails(String tenantId, String userId, String proofId, List<IObjectData> proofDisplayImgs) {
        List<IObjectData> emptyImgRecords = proofDisplayImgs.stream()
                .filter(img -> Objects.isNull(img.get(TPMActivityProofDisplayImgFields.IMAGE)))
                .collect(Collectors.toList());

        // 删除举证下已有的图片和物料识别结果
        if (CollectionUtils.isNotEmpty(emptyImgRecords)) {
            serviceFacade.bulkInvalid(emptyImgRecords, User.systemUser(tenantId));
            serviceFacade.bulkDelete(emptyImgRecords, User.systemUser(tenantId));

            // 从原列表中移除已删除的记录
            proofDisplayImgs.removeAll(emptyImgRecords);
        }

        //2、查举证产品明细 TPMActivityProofProductDetailObj
        List<IObjectData> proofProductDetails = queryTPMActivityProofProductDetailsByProofIds(tenantId, proofId);
        if (!CollectionUtils.isEmpty(proofProductDetails)) {
            serviceFacade.bulkInvalid(proofProductDetails, User.systemUser(tenantId));
            serviceFacade.bulkDelete(proofProductDetails, User.systemUser(tenantId));
        }

        //3、查举证物料明细  TPMActivityProofMaterialDetailObj
        List<IObjectData> proofMaterialDetails = queryTPMActivityProofMaterialDetailsByProofIds(tenantId, proofId);
        if (!CollectionUtils.isEmpty(proofMaterialDetails)) {
            serviceFacade.bulkInvalid(proofMaterialDetails, User.systemUser(tenantId));
            serviceFacade.bulkDelete(proofMaterialDetails, User.systemUser(tenantId));
        }

        //4、查举证项目  TPMActivityProofDetailObj
        List<IObjectData> proofDetails = queryTPMActivityProofDetailsByProofId(tenantId, proofId);
        if (!CollectionUtils.isEmpty(proofDetails)) {
            serviceFacade.bulkInvalid(proofDetails, User.systemUser(tenantId));
            serviceFacade.bulkDelete(proofDetails, User.systemUser(tenantId));
        }

        List<ActivityDisplayImgPO> displayImgDAOByProofIdList = activityDisplayImgDAO.findByProofId(tenantId, proofId);
        if (!CollectionUtils.isEmpty(displayImgDAOByProofIdList)) {
            for (ActivityDisplayImgPO activityDisplayImgPO : displayImgDAOByProofIdList) {
                activityDisplayImgDAO.delete(tenantId, Integer.parseInt(userId), activityDisplayImgPO.getId().toString());
            }
        }
    }

    private ActivityDisplayImgPO buildActivityDisplayImgPOForAddOrUpdate(String tenantId, String proofId, IObjectData img, String path, IObjectData proofObj, Detect.Result detectResult) {
        ActivityDisplayImgPO activityDisplayImgPO = new ActivityDisplayImgPO();
        activityDisplayImgPO.setNPath(path);
        activityDisplayImgPO.setProofId(proofId);
        activityDisplayImgPO.setVisitId(proofObj.get(TPMActivityProofFields.VISIT_ID, String.class, ""));
        activityDisplayImgPO.setActivityDisplayImgId(img.getId());
        activityDisplayImgPO.setStatus(detectResult.getErrorCode() == 0 ? StatusType.NORMAL.value() : StatusType.ERROR.value());
        activityDisplayImgPO.setTenantId(tenantId);

        if (!Strings.isNullOrEmpty(detectResult.getErrorMessage())) {
            activityDisplayImgPO.setErrorMessage(detectResult.getErrorMessage());
        }
        return activityDisplayImgPO;
    }

    /**
     * 提取图片路径
     * [
     * {
     * "ext": "jpg",
     * "path": "N_202312_20_7d3c574166684e26862a28ea0b33a50c",
     * "filename": "039c12a2-f2b9-4424-99f0-ab83080affc7.jpg",
     * "is_recapture": false,
     * "size": 96722,
     * "signature": "Enc(699F7171F85CF12993DE20565CBF66A90CAED80CCCEAC66C86B417329FDDCD1F4BB0D96A8A0BED41DEDD39257FC24CC74B6C74E7DF88744A08BC1168A33F657DCE415A8B1B33376AB76A313625BA192A)",
     * "signedUrl": "https://img.ceshi112.com/FilesOne/Sign?Fid=A34A40FEE22A1D741706C7FF10766F2CF4614D4052C02441255ECFBD692AC66552F4F99597083C7FED7FB5A741996EFDB8B426A135B9F1BE90863065A9D9E0899D56AF14EC80471AF3E8AE9B40FC10A25FA6A789775DD016A9170154C332191073C09F215C80671F8D9FC66086933B1965D81E5FEBAD7E4D&Acid=84931.1001&Ets=1741873107376&Ak=50wUnFnpCJiGB8mOMWte6l2A&Fn=039c12a2-f2b9-4424-99f0-ab83080affc7.jpg&Sig=Xi-m5jgE2KvTtFy0YeVn9ThPGJA=&Ds=yMjEirFkUTxocQLjJL4A4g==&linkId=E-E.84931.1001-38803415"
     * }
     * ]
     */
    private List<String> extractImagePaths(IObjectData imageData) {
        List<String> result = new ArrayList<>();
        Object img = imageData.get(TPMActivityProofDisplayImgFields.IMAGE);

        if (Objects.nonNull(img)) {
            List<Map<String, Object>> imgCast = (List<Map<String, Object>>) img;
            if (!CollectionUtils.isEmpty(imgCast)) {
                for (Map<String, Object> imgMap : imgCast) {
                    result.add((String) imgMap.get("path"));
                }
            }
        }

        return result;
    }


    private List<IObjectData> queryTPMActivityProofDetailsByProofId(String tenantId, String proofId) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityProofIdFilter = new Filter();
        activityProofIdFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        activityProofIdFilter.setOperator(Operator.EQ);
        activityProofIdFilter.setFieldValues(Lists.newArrayList(proofId));

        query.setFilters(Lists.newArrayList(activityProofIdFilter));

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, query);
    }

    private List<IObjectData> queryTPMActivityProofDisplayImgsByProofIds(String tenantId, List<String> proofIds) {
        if (CollectionUtils.isEmpty(proofIds)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityProofDisplayImgFields.ACTIVITY_PROOF_ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(proofIds);

        query.setFilters(Lists.newArrayList(activityIdFilter));

        // ACTIVITY_PROOF_ID + DISPLAY_FORM_ID + ACTIVITY_ITEM_ID 共同组成一组举证项目
        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ, query);
    }

    /**
     * 通过举证ID查询举证产品明细对象
     *
     * @param tenantId 租户ID
     * @param proofId  举证ID
     * @return 举证产品明细对象列表
     */
    private List<IObjectData> queryTPMActivityProofProductDetailsByProofIds(String tenantId, String proofId) {
        if (Objects.isNull(proofId)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityProofIdFilter = new Filter();
        activityProofIdFilter.setFieldName(TPMActivityProofProductDetailFields.ACTIVITY_PROOF_ID);
        activityProofIdFilter.setOperator(Operator.EQ);
        activityProofIdFilter.setFieldValues(Lists.newArrayList(proofId));

        query.setFilters(Lists.newArrayList(activityProofIdFilter));

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_PRODUCT_DETAIL_OBJ, query);
    }

    /**
     * 通过举证ID查询举证物料明细对象
     *
     * @param tenantId 租户ID
     * @param proofId  举证ID
     * @return 举证物料明细对象列表
     */
    private List<IObjectData> queryTPMActivityProofMaterialDetailsByProofIds(String tenantId, String proofId) {
        if (Objects.isNull(proofId)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityProofIdFilter = new Filter();
        activityProofIdFilter.setFieldName(TPMActivityProofMaterialDetailFields.ACTIVITY_PROOF_ID);
        activityProofIdFilter.setOperator(Operator.EQ);
        activityProofIdFilter.setFieldValues(Lists.newArrayList(proofId));

        query.setFilters(Lists.newArrayList(activityProofIdFilter));

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_MATERIAL_DETAIL_OBJ, query);
    }


    private List<IObjectData> queryTPMAgreementDetailByObjectId(String tenantId, String id) {

        if (StringUtils.isEmpty(id)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter refrenceIdFilter = new Filter();
        refrenceIdFilter.setFieldName(TPMActivityAgreementDetailFields.ACTIVITY_AGREEMENT_ID);
        refrenceIdFilter.setOperator(Operator.EQ);
        refrenceIdFilter.setFieldValues(Lists.newArrayList(id));

        query.setFilters(Lists.newArrayList(refrenceIdFilter));

        List<IObjectData> details = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(details)) {
            return Lists.newArrayList();
        }
        return details;
    }

    private List<IObjectData> queryTPMActivityDetailByObjectId(String tenantId, String id) {

        if (StringUtils.isEmpty(id)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter refrenceIdFilter = new Filter();
        refrenceIdFilter.setFieldName(TPMActivityDetailFields.ACTIVITY_ID);
        refrenceIdFilter.setOperator(Operator.EQ);
        refrenceIdFilter.setFieldValues(Lists.newArrayList(id));

        query.setFilters(Lists.newArrayList(refrenceIdFilter));

        List<IObjectData> details = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(details)) {
            return Lists.newArrayList();
        }
        return details;
    }


    /**
     * 查询AI规则
     *
     * @param tenantId     租户ID
     * @param activityType 活动类型
     * @return AI规则和模型ID
     */
    private BatchQueryAIRuleByIds.AIDetectRule queryAIRule(String tenantId, ActivityTypeExt activityType) {
        ActivityProofAiConfigEntity aiConfig = activityType.proofNode().getActivityProofConfig().getAiConfig();
        if (Objects.isNull(aiConfig)) {
            throw new ValidateException("aiConfig is null");
        }
        String modelId = aiConfig.getDisplayRecognitionModel();

        //查规则
        BatchQueryAIRuleByIds.Arg arg = new BatchQueryAIRuleByIds.Arg();
        arg.setQueryIds(Lists.newArrayList(String.format("%s&&%s", modelId, aiConfig.getAdaptationRule())));
        BatchQueryAIRuleByIds.Result aiRuleResult = fmcgServiceProxy.batchQueryAIRuleByIds(Integer.parseInt(tenantId), -10000, arg);
        if (Objects.isNull(aiRuleResult)) {
            throw new ValidateException("ai rule is null");
        }
        return aiRuleResult.getRules().get(0);
    }


    private boolean tryLock(String tenantId, String proofId) {
        String key = formatLockKey(tenantId, proofId);
        RLock lock = redissonCmd.getLock(key);
        log.info("[asyncProcessProofDisplayImgAi_lock] lock: {}", key);
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format("asyncProcessProofDisplayImgAi Lock Fail: %s", key));
        }
    }

    private boolean isLocked(String tenantId, String proofId) {
        RLock lock = redissonCmd.getLock(formatLockKey(tenantId, proofId));
        return lock.isLocked() || lock.isHeldByCurrentThread();
    }

    private void unlock(String tenantId, String proofId) {
        String key = formatLockKey(tenantId, proofId);
        RLock lock = redissonCmd.getLock(key);
        log.info("[asyncProcessProofDisplayImgAi_lock] unlock: {}", key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    private String formatLockKey(String tenantId, String dataId) {
        return String.format(ASYNC_PROCESS_PROOF_DISPLAY_IMG, tenantId, dataId);
    }


    /**
     * AI处理上下文，封装处理过程中需要的所有对象和状态
     */
    @Data
    private static class AIProcessingContext {
        private final String tenantId;
        private final String userId;
        private final String proofId;
        private final String modelId;
        private final boolean existAgreementNode;
        private String proofAiStatus;
        private List<IObjectData> proofDisplayImgs;
        private final IObjectData proofObj;
        private final BatchQueryAIRuleByIds.AIDetectRule aiRule;
        private final Map<String, IObjectData> displayFormItemDTOMap;
        private final Map<String, Integer> tpmDetectCapabilityMap;
        private final Map<String, List<String>> tpmDetectCapabilityFieldRelationMap;
        private List<FailedImage> failedQueue;
        private List<IObjectData> proofDetails;
        private Map<String, AIDetectionResults> aiResultsMapGroupByProofDisplayImgId;
        private List<TenantDetectFaultTolerantForDisPlayForm> tenantDetectFaultTolerantForDisPlayFormList;

        public AIProcessingContext(
                String tenantId, String userId, String proofId, String modelId, boolean existAgreementNode, String proofAiStatus,
                List<IObjectData> proofDisplayImgs, IObjectData proofObj, BatchQueryAIRuleByIds.AIDetectRule aiRule,
                Map<String, IObjectData> displayFormItemDTOMap,
                Map<String, Integer> tpmDetectCapabilityMap, Map<String, List<String>> tpmDetectCapabilityFieldRelationMap,
                List<TenantDetectFaultTolerantForDisPlayForm> tenantDetectFaultTolerantForDisPlayFormList) {
            this.tenantId = tenantId;
            this.userId = userId;
            this.proofId = proofId;
            this.modelId = modelId;
            this.existAgreementNode = existAgreementNode;
            this.proofAiStatus = proofAiStatus;
            this.proofDisplayImgs = proofDisplayImgs;
            this.proofObj = proofObj;
            this.aiRule = aiRule;
            this.displayFormItemDTOMap = displayFormItemDTOMap;
            this.tpmDetectCapabilityMap = tpmDetectCapabilityMap;
            this.tpmDetectCapabilityFieldRelationMap = tpmDetectCapabilityFieldRelationMap;
            this.aiResultsMapGroupByProofDisplayImgId = new HashMap<>();
            this.tenantDetectFaultTolerantForDisPlayFormList = tenantDetectFaultTolerantForDisPlayFormList;
        }
    }

    /**
     * 处理结果类，封装图片处理的状态和数据
     */
    @Data
    private static class ProcessingResult {
        private final boolean success;
        private final List<DetectResultDTO> detectResultDTOS;
        private final List<ActivityDisplayImgPO> addActivityDisplayImgPOs;
        private final List<String> productIdList;
        private final String errorMessage;

        public ProcessingResult(boolean success, List<DetectResultDTO> detectResultDTOS,
                                List<ActivityDisplayImgPO> addActivityDisplayImgPOs, List<String> productIdList,
                                String errorMessage
        ) {
            this.success = success;
            this.detectResultDTOS = detectResultDTOS;
            this.addActivityDisplayImgPOs = addActivityDisplayImgPOs;
            this.productIdList = productIdList;
            this.errorMessage = errorMessage;
        }
    }

    /**
     * AI检测结果，封装AI识别的关键信息
     */
    @Data
    private static class AIDetectionResults {
        private final String imgId;
        private final List<String> aiDisplayFormIdList;
        private final int aiLayerNumber;
        private final int aiGroupNumber;
        private final int aiVisibleNumber;
        private final int pileHeadCount;//堆头的个数
        private final Map<String, Long> countGroupByDisplayId;//每个陈列形式的个数

        public AIDetectionResults(List<String> aiDisplayFormIdList, String imgId, int aiLayerNumber, int aiGroupNumber, int aiVisibleNumber, int pileHeadCount, Map<String, Long> countGroupByDisplayId) {
            this.imgId = imgId;
            this.aiDisplayFormIdList = aiDisplayFormIdList;
            this.aiLayerNumber = aiLayerNumber;
            this.aiGroupNumber = aiGroupNumber;
            this.aiVisibleNumber = aiVisibleNumber;
            this.pileHeadCount = pileHeadCount;
            this.countGroupByDisplayId = countGroupByDisplayId;
        }
    }


    @Data
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProofPeriod {

        @JSONField(name = "stage")
        @JsonProperty("stage")
        @SerializedName("stage")
        private int stage;

        @JSONField(name = "begin_date")
        @JsonProperty("begin_date")
        @SerializedName("begin_date")
        private long beginDate;

        @JSONField(name = "end_date")
        @JsonProperty("end_date")
        @SerializedName("end_date")
        private long endDate;
    }

    // 定义失败重试队列
    public static class FailedImage {
        final IObjectData img;
        int retryCount = 0;

        FailedImage(IObjectData img) {
            this.img = img;
        }

        FailedImage(IObjectData img, int retryCount) {
            this.img = img;
            this.retryCount = retryCount;
        }
    }

    @Data
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    private static class TPMProductSkuRowNumberDTO {

        private String productSkuId;
        private String productId;
        private Double rowNumber;
        private String displayFormId;
    }

    @Data
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    private static class TPMMaterialAiNumberDTO {

        private String objectId;
        private Double count;
        private String displayFormId;
    }

    @Data
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    private static class ProductOrMaterialAchieveStatusDTO {
        private String proofId;
        private String dataId;
        private String productAchieveStatus;
        private String materialAchieveStatus;
    }

    @Data
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    private static class TenantDetectFaultTolerantForDisPlayForm {
        /**
         * 识别错误的key
         */
        private List<String> faultKeys;

        /**
         * 错误对应正确的key
         */
        private String reliabilityKey;

        /**
         * 识别错误的value
         */
        private List<String> faultValues;

        /**
         * 错误对应正确的value
         */
        private String reliabilityValue;
    }
}

