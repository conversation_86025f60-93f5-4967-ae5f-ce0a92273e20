package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.web.contract.model.TPMPreDisplayReport;
import com.facishare.crm.fmcg.tpm.web.contract.model.TPMProofPeriodTime;
import com.facishare.crm.fmcg.tpm.web.contract.model.ValidateCheckin;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;

public interface ITPMDisplayReportService {

    void validateDisplayReport(BaseObjectSaveAction.Arg arg);

    void addProofPeriodTime(String userId, BaseObjectSaveAction.Result result);

    void editProofPeriodTime(String userId, BaseObjectSaveAction.Result result);

    TPMProofPeriodTime.Result queryAllProofPeriodTimeData(TPMProofPeriodTime.Arg arg);

    TPMProofPeriodTime.ProofResult queryRangeProofPeriodTime(TPMProofPeriodTime.Arg arg);

    // 异步处理Display img  ai 识别
    void asyncProcessProofDisplayImgAi(String tenantId, String userId, ActivityTypeExt activityType, String proofId);

    void testAsyncProcessProofDisplayImgAi(String activityTypeId, String proofId);

    void addProofValidation(String tenantId, BaseObjectSaveAction.Arg arg);

    TPMPreDisplayReport.Result preDisplayReport(TPMPreDisplayReport.Arg arg);

    void processOnePeriodDetail(String tenantId, IObjectData periodDetailObj);

    ValidateCheckin.Result validateCheckin(ValidateCheckin.Arg validateCheckin);
}