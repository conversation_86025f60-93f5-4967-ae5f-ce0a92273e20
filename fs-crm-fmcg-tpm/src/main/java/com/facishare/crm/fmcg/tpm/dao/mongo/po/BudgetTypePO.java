package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetTypeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IBudgetType;
import com.facishare.crm.fmcg.tpm.web.utils.TPMI18Utils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/8/10 11:41
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("Duplicates")
@Entity(value = "fmcg_tpm_budget_type", noClassnameStored = true)
public class BudgetTypePO extends MongoPO implements IBudgetType, Serializable {

    public static final String F_NAME = "name";
    public static final String F_API_NAME = "api_name";
    public static final String F_DESCRIPTION = "description";
    public static final String F_STATUS = "status";
    public static final String F_VERSION = "version";
    public static final String F_DEPARTMENT_RANGE = "department_range";
    public static final String F_NODES = "nodes";

    @Property(F_VERSION)
    private long version;

    @Property(F_DEPARTMENT_RANGE)
    private List<Integer> departmentRange;

    public List<Integer> getDepartmentRange() {
        return this.departmentRange;
    }

    @Property(F_API_NAME)
    private String apiName;

    @Property(F_NAME)
    private String name;

    @Property(F_DESCRIPTION)
    private String description;

    @Embedded(F_NODES)
    private List<BudgetTypeNodeEntity> nodes;

    public List<BudgetTypeNodeEntity> getNodes() {
        return this.nodes == null ? Lists.newArrayList() : this.nodes;
    }

    @Property(F_STATUS)
    private String status;

    public static BudgetTypePO fromAddVO(BudgetTypeVO vo, List<BudgetTypeNodeEntity> nodes) {
        if (vo == null) {
            return null;
        }
        BudgetTypePO po = new BudgetTypePO();
        po.setApiName(vo.getApiName());
        po.setUniqueId(vo.getId());
        po.setName(vo.getName());
        po.setDescription(vo.getDescription());
        po.setStatus(vo.getStatus());
        po.setNodes(nodes);
        po.setDepartmentRange(vo.getDepartmentRange());
        po.setVersion(0L);
        return po;
    }

    public static BudgetTypeVO toVO(BudgetTypePO po) {
        if (Objects.isNull(po)) {
            return null;
        }
        BudgetTypeVO vo = new BudgetTypeVO();
        vo.setName(po.getName());
        vo.setDescription(po.getDescription());
        vo.setStatus(po.getStatus());
        vo.setVersion(po.getVersion());
        vo.setId(po.getId().toString());
        vo.setTenantId(po.getTenantId());
        vo.setCreator(po.getCreator());
        vo.setCreateTime(po.getCreateTime());
        vo.setLastUpdater(po.getLastUpdater());
        vo.setLastUpdateTime(po.getLastUpdateTime());
        vo.setDeleted(po.isDeleted());
        vo.setDeleteBy(po.getDeleteBy());
        vo.setDeleteTime(po.getDeleteTime());

        if (CollectionUtils.isNotEmpty(po.getDepartmentRange())) {
            vo.setDepartmentRange(po.getDepartmentRange());
        } else {
            vo.setDepartmentRange(Lists.newArrayList());
        }

        return vo;
    }

    public static BudgetTypePO fromEditVO(BudgetTypePO old, BudgetTypeVO vo, List<BudgetTypeNodeEntity> nodes) {
        if (vo == null) {
            return null;
        }
        BudgetTypePO po = new BudgetTypePO();
        po.setId(old.getOriginalId());
        po.setUniqueId(vo.getId());
        po.setVersion(old.getVersion());
        po.setApiName(old.getApiName());
        po.setTenantId(old.getTenantId());
        po.setCreator(old.getCreator());
        po.setCreateTime(old.getCreateTime());
        po.setLastUpdater(old.getLastUpdater());
        po.setLastUpdateTime(old.getLastUpdateTime());
        po.setDeleted(old.isDeleted());
        po.setDeleteBy(old.getDeleteBy());
        po.setDeleteTime(old.getDeleteTime());
        po.setName(vo.getName());
        po.setStatus(vo.getStatus());
        if (CollectionUtils.isNotEmpty(vo.getDepartmentRange())) {
            po.setDepartmentRange(vo.getDepartmentRange());
        } else {
            po.setDepartmentRange(Lists.newArrayList());
        }
        po.setDescription(vo.getDescription());
        po.setNodes(nodes);
        return po;
    }

    @Override
    public String getName() {
        if (Objects.isNull(getId()) || Strings.isNullOrEmpty(super.getTenantId())){
            return name;
        }
        String text = TPMI18Utils.getBudgeText(super.getTenantId(), getId().toString());
        if (!Strings.isNullOrEmpty(text)) {
            name = text;
        }
        return name;
    }

}