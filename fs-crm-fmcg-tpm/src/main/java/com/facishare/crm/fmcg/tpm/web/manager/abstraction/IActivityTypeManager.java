package com.facishare.crm.fmcg.tpm.web.manager.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IActivityType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 15:17
 */
public interface IActivityTypeManager {

    boolean deleteAble(String tenantId, String id);

    boolean editAble(String tenantId, String id);

    void fieldEditAbleValidation(String tenantId, String id, ActivityTypePO old, ActivityTypeVO activityType);

    ActivityTypeExt find(String tenantId, String activityTypeId);

    void update(String tenantId, Integer employeeId, String activityTypeId, ActivityTypePO data);

    List<ActivityTypeExt> findByActivityTypeIds(String tenantId, List<String> activityTypeIds);

    ActivityTypeExt findByActivityId(String tenantId, String activityId);

    ActivityTypeExt findByActivityUnifiedCaseId(String tenantId, String activityUnifiedCaseId);

    void basicInformationValidation(String tenantId, IActivityType activityType);

    void basicInformationValidation(String tenantId, String id, IActivityType activityType);

    void duplicateNameValidation(String tenantId, String name);

    void duplicateNameValidation(String tenantId, String id, String name);

    void duplicateApiNameValidation(String tenantId, String name);

    void duplicateApiNameValidation(String tenantId, String id, String name);

    void publishSyncActivityTypeFieldTask(String tenantId);

    void fillObjectDisplayNameAndRecordTypeDisplayName(String tenantId, ActivityTypeVO vo);

    void fillNodeTemplateDescription(String tenantId, ActivityTypeVO vo);

    void fillActivityReportData(String tenantId, String activityId, ActivityTypeVO vo);

    Map<String, IObjectDescribe> loadDescribeMap(String tenantId, String activityTypeId);

    Map<String, IObjectDescribe> loadDescribeMap(String tenantId, ActivityTypePO activityType);

    List<IObjectData> findActivityByActivityTypeId(String tenantId, String activityTypeId);

    List<IObjectData> findUnCloseActivityByActivityTypeIds(String tenantId, String activityTypeId);

    IObjectData findFundAccountObjById(String tenantId, String fundAccountId);

    String checkValidation(String tenantId, ActivityTypePO activityType);

    List<String> queryProofActivityTypeIds(String tenantId);

    List<ActivityTypeExt> queryActivityTypeContainsAudit(String tenantId);

    List<ActivityTypeExt> queryActivityTypeContainsAgreement(String tenantId);

    List<String> tryInitSystemTemplate(String tenantId, Integer employeeId);
    boolean getEnableAuditMode(String tenantId, String id);

    void publishSyncActivityTypeExceptionTask(String tenantId, String errorMessage, ActivityTypePO po);

    boolean judgeOpenCostAssignConfig(String tenantId);

    boolean verifyExistsStoreWriteOff(String id, String tenantId);

    void deleteReference(String tenantId, ActivityTypePO po);

    void createReference(String tenantId, ActivityTypeVO vo);

    boolean getEnableChargeUp(String tenantId);

    boolean getEnableAi(String tenantId);

    List<ActivityTypeExt> queryActivityTypesByModelIdOrRuleId(String tenantId, String modelId, String ruleId);
}
