package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface TPMPreDisplayReport {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "proof_id")
        @JsonProperty(value = "proof_id")
        @SerializedName("proof_id")
        private String proofId;

    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        // 驼峰格式，前端要求

        private String pageTemplateId;

        private String actionId;

        private String checkinsId;

        private String accountId;

        private String activityId;

        private String proofId;

        private String sourceActionId;


        private List<JumpActionButton> showButton;

    }

    @Data
    @ToString
    class JumpActionButton implements Serializable {
        private String btnName;//按钮名称
        private int btnType;
        private String actionId;//跳转动作id
        private String funcApiName;

        private Map<String,Object> customParams;
        private boolean needPopup;
        private String popupBtnText;
        private String actionCode;
    }

}
