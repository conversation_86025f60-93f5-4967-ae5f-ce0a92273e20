package com.facishare.crm.fmcg.common.adapter;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.adapter.abstraction.IPayService;
import com.facishare.crm.fmcg.common.adapter.dto.UserInfo;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetailStatusEnum;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.*;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.BatchWXTenantTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.QueryWXTenantTransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXPersonalAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXTenantAccount;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.i18n.I18NKeys;
import com.facishare.crm.pay.rest.api.common.HeaderObj;
import com.facishare.crm.pay.rest.api.common.RestResult;
import com.facishare.crm.pay.rest.api.model.cloudaccount.BatchPayOrderModel;
import com.facishare.crm.pay.rest.api.model.cloudaccount.PayOrderModel;
import com.facishare.crm.pay.rest.api.model.cloudaccount.QueryPayResultModel;
import com.facishare.crm.pay.rest.api.service.CloudAccountPayService;
import com.facishare.crm.pay.rest.api.service.WechatTransferService;
import com.facishare.paas.I18N;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/7/27 10:54
 */
//IgnoreI18nFile
@Service
@Slf4j
public class PayService implements IPayService {

    @Resource
    private CloudAccountPayService cloudAccountPayService;


    @Resource
    private WechatTransferService wechatTransferService;

    @Resource
    private EIEAConverter eieaConverter;


    @Override
    public CloudTransfer.Result cloudTransfer(UserInfo transferUser, CloudTransfer.Arg transferArg) {

        //先查询是否已经存在转账记录，如果存在则直接返回
        QueryCloudTransferDetails.Arg queryArg = new QueryCloudTransferDetails.Arg();
        queryArg.setBusinessId(transferArg.getBusinessId());
        QueryCloudTransferDetails.Result queryResult = queryCloudTransferDetails(transferUser, queryArg);
        if (CollectionUtils.isNotEmpty(queryResult.getTransferDetails())) {
            TransferDetail detail = checkTransferDetail(queryResult.getTransferDetails().get(0));
            CloudTransfer.Result result = new CloudTransfer.Result();
            result.setTransferId(detail.getTransferId());
            result.setRefTransferId(detail.getTransferId());
            result.setBusinessId(detail.getBusinessId());
            return result;
        }

        BaseCloudPayReceiverAccount receiverPayAccount = transferArg.getReceiverPayAccount();
        CloudAccount payeeCloudAccount = transferArg.getPayerCloudAccount();

        PayOrderModel.Arg payModelArg = fromPayOderArg(transferUser, payeeCloudAccount, receiverPayAccount, transferArg.getBusinessId(), transferArg.getAmount(), transferArg.getRemarks());

        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(transferUser.getTenantId()));
        RestResult<PayOrderModel.Result> payOrderResult = cloudAccountPayService.payOrder(headerObj, payModelArg);
        if (!payOrderResult.isSuccess()) {
            log.info("云支付失败，参数：{},结果：{}", payModelArg, payOrderResult);
            throw new RewardFmcgException(String.valueOf(payOrderResult.getErrCode()), I18N.text(I18NKeys.REWARD_PAY_SERVICE_0) + payOrderResult.getErrMessage());
        }
        CloudTransfer.Result result = new CloudTransfer.Result();
        result.setTransferId(payOrderResult.getResult().getOrderId());
        result.setBusinessId(payOrderResult.getResult().getBusiNo());
        result.setRefTransferId(payOrderResult.getResult().getRefOrderId());

        return result;
    }

    @Override
    public BatchCloudTransfer.Result batchCloudTransfer(UserInfo transferUser, BatchCloudTransfer.Arg transferArg) {
        checkTransferChannel(transferArg.getTransferList());
        BatchPayOrderModel.Arg batchPayOrderModelArg = new BatchPayOrderModel.Arg();

        batchPayOrderModelArg.setBatchId(transferArg.getBatchTransferId());

        BatchCloudTransfer.Result result = new BatchCloudTransfer.Result();
        result.setBatchTransferId(transferArg.getBatchTransferId());
        result.setTransferResultList(Lists.newArrayList());

        CloudAccount payeeCloudAccount = transferArg.getPayerCloudAccount();
        batchPayOrderModelArg.setEnterpriseAccount(payeeCloudAccount.getTenantAccount());
        batchPayOrderModelArg.setFsUserId(transferUser.getOuterUserIdOrUserId());
        batchPayOrderModelArg.setOrderList(Lists.newArrayList());

        //查询是否入账过
        QueryCloudTransferDetails.Arg queryArg = new QueryCloudTransferDetails.Arg();
        queryArg.setBatchTransferId(transferArg.getBatchTransferId());
        QueryCloudTransferDetails.Result queryResult = queryCloudTransferDetails(transferUser, queryArg);
        if (CollectionUtils.isNotEmpty(queryResult.getTransferDetails())) {
            if (queryResult.getTransferDetails().size() != transferArg.getTransferList().size()) {
                throw new RewardFmcgException("1001", I18N.text(I18NKeys.REWARD_PAY_SERVICE_1));
            }
            queryResult.getTransferDetails().forEach(transferDetail -> {
                TransferDetail detail = checkTransferDetail(transferDetail);
                BatchCloudTransfer.DetailResult detailResult = new BatchCloudTransfer.DetailResult();
                detailResult.setTransferId(detail.getTransferId());
                detailResult.setRefTransferId(detail.getTransferId());
                detailResult.setBusinessId(detail.getBusinessId());
                result.getTransferResultList().add(detailResult);
            });
            return result;
        }

        transferArg.getTransferList().forEach(transfer -> {
            BaseCloudPayReceiverAccount receiverPayAccount = transfer.getReceiverPayAccount();
            batchPayOrderModelArg.getOrderList().add(fromPayOderArg(transferUser, payeeCloudAccount, receiverPayAccount, transfer.getBusinessId(), transfer.getAmount(), transfer.getRemarks()));
        });

        batchPayOrderModelArg.setPayChannel(batchPayOrderModelArg.getOrderList().get(0).getPayChannel());
        RestResult<BatchPayOrderModel.Result> payOrderResult = cloudAccountPayService.batchPayOrder(HeaderObj.newInstance(Integer.parseInt(transferUser.getTenantId())), batchPayOrderModelArg);

        if (!payOrderResult.isSuccess()) {
            log.info("批量云支付失败，参数：{},结果：{}", batchPayOrderModelArg, payOrderResult);
            throw new RewardFmcgException(String.valueOf(payOrderResult.getErrCode()), I18N.text(I18NKeys.REWARD_PAY_SERVICE_2) + payOrderResult.getErrMessage());
        }


        payOrderResult.getResult().getResultList().forEach(detail -> {
            BatchCloudTransfer.DetailResult detailResult = new BatchCloudTransfer.DetailResult();
            detailResult.setTransferId(detail.getOrderId());
            detailResult.setTransferId(detail.getRefOrderId());
            detailResult.setBusinessId(detail.getBusiNo());
            result.getTransferResultList().add(detailResult);
        });
        return result;
    }

    private TransferDetail checkTransferDetail(TransferDetail detail) {
        if (TransferDetailStatusEnum.FAIL.codes().contains(detail.getStatus())) {
            throw new RewardFmcgException("10001", I18N.text(I18NKeys.REWARD_PAY_SERVICE_3));
        } else if (TransferDetailStatusEnum.CANCEL.codes().contains(detail.getStatus())) {
            throw new RewardFmcgException("10002", I18N.text(I18NKeys.REWARD_PAY_SERVICE_4));
        } else if (TransferDetailStatusEnum.INVALID.codes().contains(detail.getStatus())) {
            throw new RewardFmcgException("10003", I18N.text(I18NKeys.REWARD_PAY_SERVICE_5));
        }
        return detail;
    }

    private PayOrderModel.Arg fromPayOderArg(UserInfo transferUser, CloudAccount payeeCloudAccount, BaseCloudPayReceiverAccount receiverPayAccount, String businessId, BigDecimal amount, String remarks) {
        PayOrderModel.Arg payOrderArg = new PayOrderModel.Arg();
        payOrderArg.setPay(amount.toString());
        payOrderArg.setBusiNo(businessId);
        payOrderArg.setPayRemark(remarks);
        payOrderArg.setIdCard(receiverPayAccount.getIdCardNumber());
        payOrderArg.setRealName(receiverPayAccount.getRealName());
        payOrderArg.setPhoneNo(receiverPayAccount.getPhoneNumber());
        payOrderArg.setEnterpriseAccount(payeeCloudAccount.getTenantAccount());
        payOrderArg.setDealerId(payeeCloudAccount.getCloudAccountDealerId());
        payOrderArg.setBrokerId(payeeCloudAccount.getCloudAccountBrokerId());
        payOrderArg.setFsUserId(transferUser.getOuterUserIdOrUserId());
        if (receiverPayAccount instanceof WXCloudPayReceiverAccount) {
            WXCloudPayReceiverAccount wxPayAccount = (WXCloudPayReceiverAccount) receiverPayAccount;
            payOrderArg.setOpenId(wxPayAccount.getOpenId());
            payOrderArg.setWxPayMode(wxPayAccount.getPayMode());
            payOrderArg.setWxAppId(wxPayAccount.getAppId());
            payOrderArg.setPayChannel("wechat");
        } else if (receiverPayAccount instanceof AliCloudPayReceiverAccount) {
            AliCloudPayReceiverAccount aliPayAccount = (AliCloudPayReceiverAccount) receiverPayAccount;
            payOrderArg.setCardNO(aliPayAccount.getCardNumber());
            payOrderArg.setCheckName(receiverPayAccount.getRealName());
            payOrderArg.setPayChannel("alipay");
        } else if (receiverPayAccount instanceof BankCloudPayReceiverAccount) {
            BankCloudPayReceiverAccount bankPayAccount = (BankCloudPayReceiverAccount) receiverPayAccount;
            payOrderArg.setCardNO(bankPayAccount.getIdCardNumber());
            payOrderArg.setPayChannel("bank");
        } else {
            throw new RewardFmcgException("10004", I18N.text(I18NKeys.REWARD_PAY_SERVICE_6));
        }
        return payOrderArg;
    }

    private void checkTransferChannel(List<BatchCloudTransfer.Detail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        BatchCloudTransfer.Detail last = details.get(0);
        for (int i = 1; i < details.size(); i++) {
            if (last.getReceiverPayAccount().getClass() != details.get(i).getReceiverPayAccount().getClass()) {
                throw new RewardFmcgException("10005", I18N.text(I18NKeys.REWARD_PAY_SERVICE_7));
            }
        }
    }

    @Override
    public QueryCloudTransferDetails.Result queryCloudTransferDetails(UserInfo queryUser, QueryCloudTransferDetails.Arg arg) {

        QueryPayResultModel.Arg queryPayResultModelArg = new QueryPayResultModel.Arg();

        queryPayResultModelArg.setEnterpriseAccount(eieaConverter.enterpriseIdToAccount(Integer.parseInt(queryUser.getTenantId())));
        queryPayResultModelArg.setBatchId(arg.getBatchTransferId());
        queryPayResultModelArg.setBusiNo(arg.getBusinessId());
        queryPayResultModelArg.setOrderId(arg.getTransferId());
        queryPayResultModelArg.setFsUserId(queryUser.getOuterUserIdOrUserId());

        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(queryUser.getTenantId()));
        RestResult<QueryPayResultModel.Result> queryPayResultResult = cloudAccountPayService.queryPay(headerObj, queryPayResultModelArg);
        if (!queryPayResultResult.isSuccess()) {
            log.info("云支付查询失败，参数：{},结果：{}", queryPayResultModelArg, queryPayResultResult);
            throw new RewardFmcgException(String.valueOf(queryPayResultResult.getErrCode()), I18N.text(I18NKeys.REWARD_PAY_SERVICE_8) + queryPayResultResult.getErrMessage());
        }
        QueryCloudTransferDetails.Result result = new QueryCloudTransferDetails.Result();
        result.setTransferDetails(Lists.newArrayList());
        if (queryPayResultResult.getResult() != null) {
            result.setBatchTransferId(queryPayResultResult.getResult().getBatchId());
            result.setTransferId(queryPayResultResult.getResult().getOrderId());
            result.setBusinessId(queryPayResultResult.getResult().getBusiNo());
            if (CollectionUtils.isNotEmpty(queryPayResultResult.getResult().getResultList())) {
                queryPayResultResult.getResult().getResultList().forEach(payResult -> result.getTransferDetails().add(covertToTransferDetail(payResult)));
            }
        }
        return result;
    }

    @Override
    public BatchWXTenantTransfer.Result batchWXTenantTransfer(UserInfo transferUser, BatchWXTenantTransfer.Arg transferArg) {
        checkWXPersonalAccounts(transferArg.getReceiverAccounts());
        com.facishare.crm.pay.rest.api.model.wechattransfer.BatchPayOrderModel.Arg batchPayOrderModelArg = new com.facishare.crm.pay.rest.api.model.wechattransfer.BatchPayOrderModel.Arg();
        batchPayOrderModelArg.setBatchId(transferArg.getBatchTransferId());
        batchPayOrderModelArg.setBatchName(transferArg.getBatchName());
        batchPayOrderModelArg.setBatchRemark(transferArg.getBatchRemarks());
        batchPayOrderModelArg.setOrderList(Lists.newArrayList());

        WXTenantAccount payeeAccount = transferArg.getPayeeWXAccount();

        batchPayOrderModelArg.setEnterpriseAccount(payeeAccount.getTenantAccount());
        batchPayOrderModelArg.setFsUserId(transferUser.getOuterUserIdOrUserId());
        batchPayOrderModelArg.setTransferSceneId(transferArg.getTransferSceneId());
        if (!Strings.isNullOrEmpty(payeeAccount.getSubMchId()) ) {
            batchPayOrderModelArg.setSubEnterpriseAccount(payeeAccount.getSubMchId());
        }

        BatchWXTenantTransfer.Result result = new BatchWXTenantTransfer.Result();
        result.setBatchTransferId(transferArg.getBatchTransferId());
        result.setDetailResults(Lists.newArrayList());

        QueryWXTenantTransferDetail.Arg batchQueryArg = new QueryWXTenantTransferDetail.Arg();
        batchQueryArg.setBatchTransferId(transferArg.getBatchTransferId());
        QueryWXTenantTransferDetail.Result batchQueryResult = queryWXTenantTransferDetails(transferUser, batchQueryArg);
        if (CollectionUtils.isNotEmpty(batchQueryResult.getTransferDetails())) {
            if (batchQueryResult.getTransferDetails().size() != transferArg.getReceiverAccounts().size()) {
                throw new RewardFmcgException("10006", I18N.text(I18NKeys.REWARD_PAY_SERVICE_9));
            }
            batchQueryResult.getTransferDetails().forEach(detail -> {
                TransferDetail transferDetail = checkTransferDetail(detail);
                BatchWXTenantTransfer.DetailResult detailResult = new BatchWXTenantTransfer.DetailResult();
                detailResult.setTransferId(transferDetail.getTransferId());
                detailResult.setBusinessId(transferDetail.getBusinessId());
                detailResult.setStatus(transferDetail.getStatus());
                detailResult.setWechatOrderId(transferDetail.getWechatOrderId());
                result.getDetailResults().add(detailResult);
            });
            result.setWechatBatchStatus(batchQueryResult.getBatchStatus());
            return result;
        }

        transferArg.getReceiverAccounts().forEach(receiverAccount -> {
            com.facishare.crm.pay.rest.api.model.wechattransfer.PayOrderArg payOrderArg = new com.facishare.crm.pay.rest.api.model.wechattransfer.PayOrderArg();
            payOrderArg.setBusiNo(receiverAccount.getBusinessId());
            payOrderArg.setOpenId(receiverAccount.getOpenId());
            payOrderArg.setTransferAmount(receiverAccount.getAmount().setScale(2, RoundingMode.DOWN).multiply(new BigDecimal("100")).longValue());
            payOrderArg.setTransferRemark(receiverAccount.getRemarks());
            batchPayOrderModelArg.getOrderList().add(payOrderArg);
        });

        batchPayOrderModelArg.setAppId(transferArg.getReceiverAccounts().get(0).getAppId());


        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(transferUser.getTenantId()));
        RestResult<com.facishare.crm.pay.rest.api.model.wechattransfer.BatchPayOrderModel.Result> transferResult = wechatTransferService.batchPayOrder(headerObj, batchPayOrderModelArg);

        if (!transferResult.isSuccess()) {
            log.info("微信批量转账失败，参数：{},结果：{}", batchPayOrderModelArg, transferResult);
            throw new RewardFmcgException(String.valueOf(transferResult.getErrCode()), I18N.text(I18NKeys.REWARD_PAY_SERVICE_10) + transferResult.getErrMessage());
        }

        result.setWechatBatchId(transferResult.getResult().getWechatBatchId());
        result.setWechatBatchStatus(transferResult.getResult().getWechatBatchStatus());
        if (CollectionUtils.isNotEmpty(transferResult.getResult().getOrderList())) {
            transferResult.getResult().getOrderList().forEach(payOrder -> {
                BatchWXTenantTransfer.DetailResult detailResult = new BatchWXTenantTransfer.DetailResult();
                detailResult.setStatus(payOrder.getStatus());
                detailResult.setTransferId(payOrder.getOrderId());
                detailResult.setWechatOrderId(payOrder.getWechatOrderId());
                detailResult.setBusinessId(payOrder.getBusiNo());
                result.getDetailResults().add(detailResult);
            });
        }

        return result;
    }

    private void checkWXPersonalAccounts(List<WXPersonalAccount> accounts) {
        if (CollectionUtils.isNotEmpty(accounts)) {
            WXPersonalAccount last = accounts.get(0);
            for (int i = 1; i < accounts.size(); i++) {
                WXPersonalAccount current = accounts.get(i);
                if (!last.getAppId().equals(current.getAppId())) {
                    throw new RewardFmcgException("10006", I18N.text(I18NKeys.REWARD_PAY_SERVICE_11));
                }
                last = current;
            }
        }
    }

    @Override
    public QueryWXTenantTransferDetail.Result queryWXTenantTransferDetails(UserInfo queryUser, QueryWXTenantTransferDetail.Arg arg) {
        com.facishare.crm.pay.rest.api.model.wechattransfer.QueryPayResultModel.Arg queryPayResultModelArg = new com.facishare.crm.pay.rest.api.model.wechattransfer.QueryPayResultModel.Arg();

        queryPayResultModelArg.setBatchId(arg.getBatchTransferId());
        queryPayResultModelArg.setOrderId(arg.getTransferId());
        queryPayResultModelArg.setBusiNo(arg.getBusinessId());
        queryPayResultModelArg.setFsUserId(queryUser.getOuterUserIdOrUserId());
        queryPayResultModelArg.setEnterpriseAccount(eieaConverter.enterpriseIdToAccount(Integer.parseInt(queryUser.getTenantId())));

        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(queryUser.getTenantId()));
        RestResult<com.facishare.crm.pay.rest.api.model.wechattransfer.QueryPayResultModel.Result> queryResult = wechatTransferService.queryPay(headerObj, queryPayResultModelArg);

        if (!queryResult.isSuccess()) {
            log.info("微信批量转账查询失败，参数：{},结果：{}", queryPayResultModelArg, queryResult);
            throw new RewardFmcgException(String.valueOf(queryResult.getErrCode()), I18N.text(I18NKeys.REWARD_PAY_SERVICE_12) + queryResult.getErrMessage());
        }

        QueryWXTenantTransferDetail.Result result = new QueryWXTenantTransferDetail.Result();
        result.setBatchTransferId(queryResult.getResult().getBatchId());
        result.setTransferId(queryResult.getResult().getOrderId());
        result.setBusinessId(queryResult.getResult().getBusiNo());
        result.setTransferDetails(Lists.newArrayList());
        result.setBatchStatus(queryResult.getResult().getBatchStatus());

        if (CollectionUtils.isNotEmpty(queryResult.getResult().getResultList())) {
            queryResult.getResult().getResultList().forEach(detail -> {
                TransferDetail transferDetail = new TransferDetail();
                transferDetail.setTransferId(detail.getOrderId());
                transferDetail.setBusinessId(detail.getBusiNo());
                transferDetail.setBatchTransferId(detail.getBatchId());
                transferDetail.setPay(String.valueOf(detail.getTransferAmount() / 100.0));
                transferDetail.setStatus(detail.getStatus());
                transferDetail.setCreateTime(detail.getCreateTime());
                transferDetail.setOpenId(detail.getOpenId());
                transferDetail.setOpFsUserId(detail.getOpFsUserId());
                transferDetail.setWechatBatchId(detail.getWechatBatchId());
                transferDetail.setWechatOrderId(detail.getWechatOrderId());
                transferDetail.setPayRemark(detail.getTransferRemark());
                transferDetail.setTransferSceneId(detail.getTransferSceneId());
                transferDetail.setFailReason(detail.getFailReason());
                if ("WAIT_PAY".equals(transferDetail.getStatus()) && Strings.isNullOrEmpty(transferDetail.getFailReason())) {
                    transferDetail.setFailReason("待确认。待商户确认, 符合免密条件时, 系统会自动扭转为转账中");
                }
                transferDetail.setSubMchId(detail.getSubMchId());
                result.getTransferDetails().add(transferDetail);
            });
        }

        return result;
    }


    private TransferDetail covertToTransferDetail(QueryPayResultModel.GetPayOrderResult payOrder) {
        TransferDetail transferDetail = new TransferDetail();
        transferDetail.setTransferId(payOrder.getOrderId());
        transferDetail.setBusinessId(payOrder.getBusiNo());
        transferDetail.setBatchTransferId(payOrder.getBatchId());
        transferDetail.setBatchTransferId(payOrder.getBatchId());
        transferDetail.setBrokerFee(payOrder.getBrokerFee());
        transferDetail.setBrokerDeductFee(payOrder.getBrokerDeductFee());
        transferDetail.setBrokerBankBill(payOrder.getBrokerBankBill());
        transferDetail.setCardNo(payOrder.getCardNo());
        transferDetail.setBrokerRealFee(payOrder.getBrokerRealFee());
        transferDetail.setCreateTime(payOrder.getCreateTime());
        transferDetail.setCreatedAt(payOrder.getCreatedAt());
        transferDetail.setFinishedTime(transferDetail.getFinishedTime());
        transferDetail.setStatus(payOrder.getStatus());
        transferDetail.setUserFee(payOrder.getUserFee());
        transferDetail.setUserId(payOrder.getUserId());
        transferDetail.setProjectId(payOrder.getProjectId());
        transferDetail.setOpFsUserId(payOrder.getOpFsUserId());
        transferDetail.setPay(payOrder.getPay());
        transferDetail.setRefOrderId(payOrder.getRefOrderId());
        transferDetail.setRealName(payOrder.getRealName());
        transferDetail.setIdCard(payOrder.getIdCard());
        transferDetail.setCheckName(payOrder.getCheckName());
        transferDetail.setOpenId(payOrder.getOpenId());
        transferDetail.setPayChannel(payOrder.getPayChannel());
        transferDetail.setPayRemark(payOrder.getPayRemark());
        transferDetail.setPhoneNo(payOrder.getPhoneNo());
        transferDetail.setWxAppId(payOrder.getWxAppId());
        transferDetail.setWxPayMode(payOrder.getWxPayMode());
        transferDetail.setFailReason(payOrder.getStatusDetailMessage());
        transferDetail.setDealerId(payOrder.getDealerId());
        transferDetail.setBrokerId(payOrder.getBrokerId());
        return transferDetail;
    }
}
