package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 16:57
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/ActivityType", produces = "application/json")
public class ActivityTypeController {

    @Resource
    private IActivityTypeService activityTypeService;

    @PostMapping(value = "SetStatus")
    public SetActivityTypeStatus.Result setStatus(@RequestBody SetActivityTypeStatus.Arg arg) {
        return activityTypeService.setStatus(arg);
    }

    @PostMapping(value = "Add")
    public AddActivityType.Result add(@RequestBody AddActivityType.Arg arg) {
        return activityTypeService.add(arg);
    }

    @PostMapping(value = "Edit")
    public EditActivityType.Result edit(@RequestBody EditActivityType.Arg arg) {
        return activityTypeService.edit(arg);
    }

    @PostMapping(value = "Get")
    public GetActivityType.Result get(@RequestBody GetActivityType.Arg arg) {
        return activityTypeService.get(arg);
    }

    @PostMapping(value = "Delete")
    public DeleteActivityType.Result delete(@RequestBody DeleteActivityType.Arg arg) {
        return activityTypeService.delete(arg);
    }

    @PostMapping(value = "List")
    public ListActivityType.Result list(@RequestBody ListActivityType.Arg arg) {
        return activityTypeService.list(arg);
    }

    @PostMapping(value = "ValidActivityType")
    public ValidActivityType.Result validActivityType(@RequestBody ValidActivityType.Arg arg) {
        return activityTypeService.validActivityType(arg);
    }

    @PostMapping(value = "LoadDesignerConfig")
    public LoadDesignerConfig.Result loadDesignerConfig(@RequestBody LoadDesignerConfig.Arg arg) {
        return activityTypeService.loadDesignerConfig(arg);
    }

    @PostMapping(value = "DataActivityType")
    public DataActivityType.Result dataActivityType(@RequestBody DataActivityType.Arg arg) {
        return activityTypeService.dataActivityType(arg);
    }

    @PostMapping(value = "FindActivityData")
    public FindActivityData.Result findActivityData(@RequestBody FindActivityData.Arg arg) {
        return activityTypeService.findActivityData(arg);
    }

    @PostMapping(value = "ValidActivityDateByRecordType")
    public ValidActivityDateByRecordType.Result ValidActivityDateByRecordType(@RequestBody ValidActivityDateByRecordType.Arg arg) {
        return activityTypeService.validActivityDateByRecordType(arg);
    }

    @PostMapping(value = "Permission")
    public TPMPermission.Result permission() {
        return activityTypeService.permission();
    }

    @PostMapping(value = "ListForPoofAi")
    public ListActivityTypeForProofAi.Result listForPoofAi(@RequestBody ListActivityTypeForProofAi.Arg arg) {
        return activityTypeService.listForPoofAi(arg);
    }
}