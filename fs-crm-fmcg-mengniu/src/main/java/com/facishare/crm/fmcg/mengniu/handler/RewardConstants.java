package com.facishare.crm.fmcg.mengniu.handler;

import com.facishare.crm.fmcg.mengniu.dto.CloudAccountInformation;
import com.google.common.collect.Maps;

import java.util.Map;
//IgnoreI18nFile
public abstract class RewardConstants {

    protected static final Map<String, String> EVENT_TYPE_VALUE_MAP = Maps.newHashMap();
    protected static final Map<String, String> EVENT_TYPE_VALUE_MAP_V2 = Maps.newHashMap();
    protected static final Map<String, String> ROLE_VALUE_MAP = Maps.newHashMap();
    protected static final Map<String, String> ROLE_VALUE_MAP_V2 = Maps.newHashMap();
    protected static final Map<String, String> ROLE_VALUE_MAP_REVERSE = Maps.newHashMap();
    protected static final Map<String, String> ACCOUNT_TYPE_VALUE_MAP = Maps.newHashMap();
    protected static final Map<String, String> ACCOUNT_TYPE_VALUE_MAP_V2 = Maps.newHashMap();
    protected static final Map<String, CloudAccountInformation> CLOUD_ACCOUNT_DEALER_ID_MAP = Maps.newHashMap();

    public static final String ROLE_CONSUMER = "CONSUMER";
    public static final String ROLE_STORE_OWNER = "STORE_OWNER";
    public static final String ROLE_SW_OWNER = "SW_OWNER";
    public static final String ROLE_SALESMEN = "SALESMEN";
    public static final String ROLE_DEALER = "DEALER";
    public static final String ROLE_M_BOSS = "M_BOSS";
    public static final String SYSTEM_USER = "-10000";
    public static final String ACTIVITY_REWARD = "activityIncentives";



    static {

        EVENT_TYPE_VALUE_MAP.put("SIGN_IN_GOODS", "1");
        EVENT_TYPE_VALUE_MAP.put("CONSUMER_SCAN_CODE", "2");
        EVENT_TYPE_VALUE_MAP.put("USER_OBJECT_ACTION_REWARDS", "3");

        EVENT_TYPE_VALUE_MAP_V2.put("SIGN_IN_GOODS", "STORE_SIGN");
        EVENT_TYPE_VALUE_MAP_V2.put("STORE_STOCK_CHECK", "STORE_STOCK_CHECK");
        EVENT_TYPE_VALUE_MAP_V2.put("CONSUMER_SCAN_CODE", "CONSUMER_SCAN_CODE");
        EVENT_TYPE_VALUE_MAP_V2.put("USER_OBJECT_ACTION_REWARDS", "USER_OBJECT_ACTION_REWARDS");
        // 新增自定义对象触发
        EVENT_TYPE_VALUE_MAP_V2.put("CUSTOM_OBJECT_ACTION_TRIGGER", "CUSTOM_OBJECT_ACTION_TRIGGER");

        ROLE_VALUE_MAP.put("CONSUMER", "1");
        ROLE_VALUE_MAP.put("STORE_OWNER", "2");
        ROLE_VALUE_MAP.put("SALESMEN", "3");
        ROLE_VALUE_MAP.put("DEALER", "4");
        ROLE_VALUE_MAP.put("M_BOSS", "5");
        ROLE_VALUE_MAP.put("SW_OWNER", "6");
        ROLE_VALUE_MAP.put("SP_GUIDE", "7");

        ROLE_VALUE_MAP_V2.put("CONSUMER", "消费者");
        ROLE_VALUE_MAP_V2.put("STORE_OWNER", "店老板");
        ROLE_VALUE_MAP_V2.put("SALESMEN", "业代");
        ROLE_VALUE_MAP_V2.put("DEALER", "经销商");
        ROLE_VALUE_MAP_V2.put("M_BOSS", "经销商老板");
        ROLE_VALUE_MAP_V2.put("SW_OWNER", "店仓老板");
        ROLE_VALUE_MAP_V2.put("SP_GUIDE", "导购");

        ROLE_VALUE_MAP_REVERSE.put("1", "CONSUMER");
        ROLE_VALUE_MAP_REVERSE.put("2", "STORE_OWNER");
        ROLE_VALUE_MAP_REVERSE.put("3", "SALESMEN");
        ROLE_VALUE_MAP_REVERSE.put("4", "DEALER");
        ROLE_VALUE_MAP_REVERSE.put("5", "M_BOSS");
        ROLE_VALUE_MAP_REVERSE.put("6", "SW_OWNER");
        ROLE_VALUE_MAP_REVERSE.put("7", "SP_GUIDE");

        ACCOUNT_TYPE_VALUE_MAP.put("WeChat", "1");
        ACCOUNT_TYPE_VALUE_MAP.put("TenantCloud", "2");
        ACCOUNT_TYPE_VALUE_MAP.put("AliPay", "3");
        ACCOUNT_TYPE_VALUE_MAP.put("Bank", "4");
        ACCOUNT_TYPE_VALUE_MAP.put("TenantWeChat", "5");

        ACCOUNT_TYPE_VALUE_MAP_V2.put("WeChat", "wechat");
        ACCOUNT_TYPE_VALUE_MAP_V2.put("TenantCloud", "cloud");
        ACCOUNT_TYPE_VALUE_MAP_V2.put("TenantWeChat", "wechatMerchant");

        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1001", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 河南大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1002", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 河北大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1003", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 津冀大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1004", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 云贵广大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1005", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 山东大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1006", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 粤海大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1007", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 上海大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1008", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 东北大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1009", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 安徽大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1010", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 江苏大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1011", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 川藏渝大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1012", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 浙江大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1013", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 闽赣大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1014", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 华中大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1015", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 北京大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1016", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 晋蒙西大区
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1017", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 网格化红包
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1018", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 店仓业务红包
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1019", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 传统业务红包
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("1020", CloudAccountInformation.builder().dealerId("********").brokerId("yiyun73").build()); // 蒙牛乳业
        CLOUD_ACCOUNT_DEALER_ID_MAP.put("9001", CloudAccountInformation.builder().dealerId("********").brokerId("********").build()); // 纷享测试
    }
}
