package com.facishare.crm.fmcg.mengniu.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventAbandonException;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventRetryException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.business.ObjectActionActivitySelector;
import com.facishare.crm.fmcg.mengniu.dto.*;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.crm.fmcg.tpm.business.abstraction.IMengNiuAICalculateService;
import com.facishare.crm.fmcg.tpm.web.condition.ConditionAdapter;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.facishare.crm.fmcg.mengniu.service.ObjectActionService.AI_SUPPORT_OBJECT_API_NAMES;
import static com.facishare.crm.fmcg.tpm.action.TPMActivityObjEditAction.TEMPLATE_TENANT_ID;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class ObjectActionEventHandler extends CommonSalesEventHandler<ObjectActionEventData> {

    @Resource
    private ObjectActionActivitySelector objectActionActivitySelector;
    @Resource
    private ConditionAdapter conditionAdapter;
    @Resource
    private IMengNiuAICalculateService mengNiuAICalculateService;
    protected static final Map<String, String> STORE_ID_FIELD_MAP = Maps.newHashMap();

    static {
        STORE_ID_FIELD_MAP.put(ApiNames.SALES_ORDER_OBJ, SalesOrderObjFields.ACCOUNT_ID);
        STORE_ID_FIELD_MAP.put(ApiNames.SHELF_REPORT_OBJ, ShelfReportObjFields.CUSTOMER_ID);
        STORE_ID_FIELD_MAP.put(ApiNames.DEALER_DELIVERY_NOTE_OBJ, DealerDeliveryNoteObjFields.ACCOUNT_ID);
        STORE_ID_FIELD_MAP.put(ApiNames.CHECKINS_OBJ, CheckinsObjFields.CUSTOMER_ID);
        STORE_ID_FIELD_MAP.put(ApiNames.PURCHASE_REPORTING_OBJ, PurchaseReportingObjFields.CUSTOMER_ID);
    }

    @Override
    protected String buildEventIdentityKey(final SalesEvent<ObjectActionEventData> event) {
        return String.format(
            "%s.%s.%s.%s.%s",
            event.getTenantId(),
            event.getEventType(),
            event.getData().getObjectData().getDescribeApiName(),
            event.getData().getObjectAction(),
            event.getData().getObjectData().getId()
        );
    }

    @Override
    protected List<RedPacketReward> calculateRewards(SalesEvent<ObjectActionEventData> event) {
        MengNiuTenantInformation tenant = tenantHierarchyService.load(event.getData().getObjectData().getTenantId());
        if (MengNiuTenantInformation.ROLE_OTHERS.equals(tenant.getRole())) {
            return Lists.newArrayList();
        }
        if (!TPMGrayUtils.objectActionRewardsEnable(tenant.getManufacturer().getTenantId())) {
            return Lists.newArrayList();
        }
        String eventTenantName = getTenantName(event.getData().getObjectData().getTenantId());

        IObjectData store = findRelatedStore(event);
        if (Objects.isNull(store)) {
            throw new EventAbandonException("store not found.");
        }

        ObjectActionActivitySelector.Arg selectorArg = ObjectActionActivitySelector.Arg.builder()
            .tenant(tenant)
            .storeId(store.getId())
            .objectApiName(event.getData().getObjectData().getDescribeApiName())
            .objectAction(event.getData().getObjectAction())
            .build();

        IObjectData activity = objectActionActivitySelector.invoke(selectorArg);
        if (Objects.isNull(activity)) {
            throw new EventAbandonException("activity not found.");
        }

        log.info("activity : {}", activity);

        if (!match(activity, event)) {
            throw new EventAbandonException("rule not match.");
        }

        RedPacketReward storeOwnerReward = calculateStoreOwnerReward(tenant, eventTenantName, event, activity, store);
        RedPacketReward mBossReward = calculateMBossReward(tenant, eventTenantName, event, activity, store);
        RedPacketReward salesmenReward = calculateSalesmenReward(tenant, eventTenantName, event, activity, store);

        List<RedPacketReward> rewards = Lists.newArrayList(mBossReward, storeOwnerReward, salesmenReward);
        validateActivityAmount(activity, rewards);
        return rewards;
    }

    private boolean match(IObjectData activity, SalesEvent<ObjectActionEventData> event) {
        if (AI_SUPPORT_OBJECT_API_NAMES.contains(event.getData().getObjectData().getDescribeApiName())) {
            boolean aiRuleValidate = mengNiuAICalculateService.isFitMengNiuAiRule(activity, event.getTenantId(), event.getData().getObjectData().getDescribeApiName(), event.getData().getObjectData().getId(), true);
            if (!Boolean.TRUE.equals(aiRuleValidate)) {
                log.info("ai rule not match.activityId:{}", activity.getId());
                return false;
            }
        }

        if (ApiNames.SALES_ORDER_OBJ.equals(event.getData().getObjectData().getDescribeApiName())) {
            boolean productRuleValidate = mengNiuAICalculateService.isFitMengNiuProductRule(activity, event.getTenantId(), event.getData().getObjectData().getDescribeApiName(), event.getData().getObjectData().getId(), true);
            if (!Boolean.TRUE.equals(productRuleValidate)) {
                log.info("product rule not match.activityId:{}", activity.getId());
                return false;
            }
        }


        String ruleCode = activity.get(TPMActivityFields.PERSON_REWARD_RULE_CODE, String.class);
        String ruleConditionStr = activity.get(TPMActivityFields.PERSON_REWARD_RULE_WHERE, String.class);
        JSONArray ruleCondition = JSON.parseArray(ruleConditionStr);
        if (StringUtils.isEmpty(ruleCode) && (StringUtils.isEmpty(ruleConditionStr) || CollectionUtils.isEmpty(ruleCondition))) {
            return true;
        }
        if (StringUtils.isEmpty(ruleCode)) {
            log.error("activity rule code is null,please check rule code field in activity,activityId:{}", activity.getId());
            throw new EventRetryException("activity rule code query error");
        }
        String ruleTemplateTenant = activity.get(TPMActivityFields.ACTION_RULE_TEMPLATE_TENANT, String.class);
        String ruleTemplateTenantId = TEMPLATE_TENANT_ID.get(ruleTemplateTenant);
        if (StringUtils.isEmpty(ruleTemplateTenantId)) {
            throw new EventRetryException("ruleTemplateTenantId not found");
        }
        if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(ruleTemplateTenantId), -10000, event.getData().getObjectData().getDescribeApiName(), ruleCode, ObjectDataExt.toMap(event.getData().getObjectData()))) {
            log.info("activity rule not match.activityId:{},ruleCode:{}", activity.getId(), ruleCode);
            return false;
        }
        log.info("match success.activityId:{},ruleCode:{}", activity.getId(), ruleCode);
        return true;
    }

    private IObjectData findRelatedStore(SalesEvent<ObjectActionEventData> event) {
        String storeId = null;
        if (STORE_ID_FIELD_MAP.containsKey(event.getData().getObjectData().getDescribeApiName())) {
            String storeIdFieldKey = STORE_ID_FIELD_MAP.get(event.getData().getObjectData().getDescribeApiName());
            storeId = event.getData().getObjectData().get(storeIdFieldKey, String.class);
        }
        log.info("find related store : {}.{}", event.getData().getObjectData().getTenantId(), storeId);

        if (Strings.isNullOrEmpty(storeId)) {
            return null;
        }
        try {
            return serviceFacade.findObjectData(User.systemUser(event.getData().getObjectData().getTenantId()), storeId, ApiNames.ACCOUNT_OBJ);
        } catch (ObjectDataNotFoundException ex) {
            log.warn(String.format("try find related store error - %s.%s : ", event.getData().getObjectData().getTenantId(), storeId), ex);
            return null;
        }
    }

    private RedPacketReward calculateSalesmenReward(
        MengNiuTenantInformation tenant,
        String eventTenantName,
        SalesEvent<ObjectActionEventData> event,
        IObjectData activity,
        IObjectData store) {
        if (idempotentValidate(event, tenant.getManufacturer().getTenantId(), activity.getId(), activity.getRecordType(), RewardConstants.ROLE_SALESMEN)) {
            return null;
        }
        if (overFrequency(tenant.getManufacturer().getTenantId(), activity, store, event, RewardConstants.ROLE_SALESMEN)) {
            return null;
        }
        RoleRewardAmount amount = calculateRewardAmount(activity, RewardConstants.ROLE_SALESMEN, store.getId(), null);
        PaymentAccount from = loadTenantCloudAccountByRole(tenant, activity, RewardConstants.ROLE_SALESMEN);
        String rewardPersonId = event.getData().getObjectData().getOwner().get(0);
        PaymentAccount to = PaymentAccount.of(
            ApiNames.PERSONNEL_OBJ,
            String.format("%s.%s", event.getData().getObjectData().getTenantId(), rewardPersonId),
            loadWeChatPaymentAccountFromEmployee(event.getData().getObjectData().getTenantId(), rewardPersonId)
        );

        String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_SALESMEN.toLowerCase());
        String publishMode = activity.get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);

        return RedPacketReward.of(tenant, eventTenantName, event, activity, store, RewardConstants.ROLE_SALESMEN, from, to, amount, publishMode);
    }

    private boolean overFrequency(String tenantId, IObjectData activity, IObjectData store, SalesEvent<ObjectActionEventData> event, String role) {
        String frequencyFieldKey = String.format("%s_red_packet_reward_frequency__c", role.toLowerCase());
        String frequency = activity.get(frequencyFieldKey, String.class, "0");
        if ("0".equalsIgnoreCase(frequency)) {
            return false;
        }
        String lockKey = String.format("FMCG:TPM:object_action_over_frequency.%s.%s.%s.%s", tenantId, activity.getId(), store.getId(), RewardConstants.ROLE_VALUE_MAP.get(role));
        try {
            if (!tryLock(lockKey)) {
                throw new EventRetryException("try lock object_action_over_frequency fail");
            }
            if ("1".equalsIgnoreCase(frequency)) {
                return existThisMonthRecord(tenantId, activity, store, event, role);
            }
            if ("2".equalsIgnoreCase(frequency)) {
                return existThisActivityRecord(tenantId, activity, store, role);
            }
        } finally {
            unlock(lockKey);
        }
        return false;
    }

    private boolean existThisActivityRecordV2(String tenantId, IObjectData activity, IObjectData store, String role) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordObjFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        IFilter relatedStoreIdFilter = new Filter();
        relatedStoreIdFilter.setFieldName(RedPacketRecordObjFields.ACCOUNT_ID);
        relatedStoreIdFilter.setOperator(Operator.EQ);
        relatedStoreIdFilter.setFieldValues(Lists.newArrayList(store.getId()));

        IFilter roleFilter = new Filter();
        roleFilter.setFieldName("role__c");
        roleFilter.setOperator(Operator.EQ);
        roleFilter.setFieldValues(Lists.newArrayList(RewardConstants.ROLE_VALUE_MAP.get(role)));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
            Lists.newArrayList(activityIdFilter, relatedStoreIdFilter, roleFilter),
            Lists.newArrayList(order)
        );
        stq.setLimit(1);

        return !QueryDataUtil.find(
            serviceFacade,
            tenantId,
            ApiNames.RED_PACKET_RECORD_OBJ,
            stq,
            Lists.newArrayList("_id")).isEmpty();
    }

    private boolean existThisActivityRecord(String tenantId, IObjectData activity, IObjectData store, String role) {
        if (TPMGrayUtils.allowMengNiuRedPacketPublishV2(tenantId, activity.getRecordType())) {
            return existThisActivityRecordV2(tenantId, activity, store, role);
        }
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        IFilter relatedStoreIdFilter = new Filter();
        relatedStoreIdFilter.setFieldName("related_store_id__c");
        relatedStoreIdFilter.setOperator(Operator.EQ);
        relatedStoreIdFilter.setFieldValues(Lists.newArrayList(store.getId()));

        IFilter roleFilter = new Filter();
        roleFilter.setFieldName("role__c");
        roleFilter.setOperator(Operator.EQ);
        roleFilter.setFieldValues(Lists.newArrayList(RewardConstants.ROLE_VALUE_MAP.get(role)));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
            Lists.newArrayList(activityIdFilter, relatedStoreIdFilter, roleFilter),
            Lists.newArrayList(order)
        );
        stq.setLimit(1);

        return !QueryDataUtil.find(
            serviceFacade,
            tenantId,
            "red_packet_record__c",
            stq,
            Lists.newArrayList("_id")).isEmpty();
    }

    private boolean existThisMonthRecordV2(String tenantId, IObjectData activity, IObjectData store, SalesEvent<ObjectActionEventData> event, String role) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(event.getEventTime());
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long begin = cal.getTimeInMillis();
        cal.add(Calendar.MONTH, 1);
        long end = cal.getTimeInMillis();

        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordObjFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        IFilter relatedStoreIdFilter = new Filter();
        relatedStoreIdFilter.setFieldName(RedPacketRecordObjFields.ACCOUNT_ID);
        relatedStoreIdFilter.setOperator(Operator.EQ);
        relatedStoreIdFilter.setFieldValues(Lists.newArrayList(store.getId()));

        IFilter roleFilter = new Filter();
        roleFilter.setFieldName("role__c");
        roleFilter.setOperator(Operator.EQ);
        roleFilter.setFieldValues(Lists.newArrayList(RewardConstants.ROLE_VALUE_MAP.get(role)));

        IFilter eventTimeFilter = new Filter();
        eventTimeFilter.setFieldName(RedPacketRecordObjFields.REWARD_TIME);
        eventTimeFilter.setOperator(Operator.BETWEEN);
        eventTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(begin), String.valueOf(end)));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
            Lists.newArrayList(activityIdFilter, relatedStoreIdFilter, roleFilter, eventTimeFilter),
            Lists.newArrayList(order)
        );
        stq.setLimit(1);

        return !QueryDataUtil.find(
            serviceFacade,
            tenantId,
            ApiNames.RED_PACKET_RECORD_OBJ,
            stq,
            Lists.newArrayList("_id")).isEmpty();
    }


    private boolean existThisMonthRecord(String tenantId, IObjectData activity, IObjectData store, SalesEvent<ObjectActionEventData> event, String role) {
        if (TPMGrayUtils.allowMengNiuRedPacketPublishV2(tenantId, activity.getRecordType())) {
            return existThisMonthRecordV2(tenantId, activity, store, event, role);
        }
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(event.getEventTime());
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long begin = cal.getTimeInMillis();
        cal.add(Calendar.MONTH, 1);
        long end = cal.getTimeInMillis();

        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        IFilter relatedStoreIdFilter = new Filter();
        relatedStoreIdFilter.setFieldName("related_store_id__c");
        relatedStoreIdFilter.setOperator(Operator.EQ);
        relatedStoreIdFilter.setFieldValues(Lists.newArrayList(store.getId()));

        IFilter roleFilter = new Filter();
        roleFilter.setFieldName("role__c");
        roleFilter.setOperator(Operator.EQ);
        roleFilter.setFieldValues(Lists.newArrayList(RewardConstants.ROLE_VALUE_MAP.get(role)));

        IFilter eventTimeFilter = new Filter();
        eventTimeFilter.setFieldName("event_time__c");
        eventTimeFilter.setOperator(Operator.BETWEEN);
        eventTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(begin), String.valueOf(end)));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
            Lists.newArrayList(activityIdFilter, relatedStoreIdFilter, roleFilter, eventTimeFilter),
            Lists.newArrayList(order)
        );
        stq.setLimit(1);

        return !QueryDataUtil.find(
            serviceFacade,
            tenantId,
            "red_packet_record__c",
            stq,
            Lists.newArrayList("_id")).isEmpty();
    }

    private RedPacketReward calculateMBossReward(
        MengNiuTenantInformation tenant,
        String eventTenantName,
        SalesEvent<ObjectActionEventData> event,
        IObjectData activity,
        IObjectData store) {
        if (!tenant.getRole().equals(MengNiuTenantInformation.ROLE_M)) {
            return null;
        }
        if (idempotentValidate(event, tenant.getManufacturer().getTenantId(), activity.getId(), activity.getRecordType(), RewardConstants.ROLE_M_BOSS)) {
            return null;
        }
        if (overFrequency(tenant.getManufacturer().getTenantId(), activity, store, event, RewardConstants.ROLE_M_BOSS)) {
            return null;
        }
        RoleRewardAmount amount = calculateRewardAmount(activity, RewardConstants.ROLE_M_BOSS, store.getId(), null);
        PaymentAccount from = loadTenantCloudAccountByRole(tenant, activity, RewardConstants.ROLE_M_BOSS);

        String mBossEmployeeId = findMBossEmployeeId(event.getData().getObjectData().getTenantId());
        WeChatPaymentAccount account;
        if (Strings.isNullOrEmpty(mBossEmployeeId)) {
            account = WeChatPaymentAccount.builder().build();
        } else {
            account = loadWeChatPaymentAccountFromEmployee(event.getData().getObjectData().getTenantId(), mBossEmployeeId);
        }
        PaymentAccount to = PaymentAccount.of(
            ApiNames.PERSONNEL_OBJ,
            String.format("%s.%s", event.getData().getObjectData().getTenantId(), mBossEmployeeId),
            account
        );

        String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_M_BOSS.toLowerCase());
        String publishMode = activity.get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);

        return RedPacketReward.of(tenant, eventTenantName, event, activity, store, RewardConstants.ROLE_M_BOSS, from, to, amount, publishMode);
    }

    private RedPacketReward calculateStoreOwnerReward(
        MengNiuTenantInformation tenant,
        String eventTenantName,
        SalesEvent<ObjectActionEventData> event,
        IObjectData activity,
        IObjectData store) {
        if (idempotentValidate(event, tenant.getManufacturer().getTenantId(), activity.getId(), activity.getRecordType(), RewardConstants.ROLE_STORE_OWNER)) {
            return null;
        }
        if (overFrequency(tenant.getManufacturer().getTenantId(), activity, store, event, RewardConstants.ROLE_STORE_OWNER)) {
            return null;
        }
        RoleRewardAmount amount = calculateRewardAmount(activity, RewardConstants.ROLE_STORE_OWNER, store.getId(), null);
        PaymentAccount from = loadTenantCloudAccountByRole(tenant, activity, RewardConstants.ROLE_STORE_OWNER);

        IObjectData contact = findContactData(store.getTenantId(), store.getId());
        //如果联系人类型不是店主、店老板激励直接不生成
        if (Objects.isNull(contact)) {
            return null;
        }
        PaymentAccount to = loadWeChatPaymentAccountFromStore(store.getTenantId(), contact);

        String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_STORE_OWNER.toLowerCase());
        String publishMode = activity.get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);

        return RedPacketReward.of(tenant, eventTenantName, event, activity, store, RewardConstants.ROLE_STORE_OWNER, from, to, amount, publishMode);
    }

    private void validateActivityAmount(IObjectData activity, List<RedPacketReward> rewards) {
        Boolean allowOverLimitReward = activity.get("allow_over_limit_reward__c", Boolean.class);
        BigDecimal budget = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class);

        BigDecimal incomingRewardAmount = calculateIncomingRewardAmount(rewards);
        BigDecimal rewardedAmount = calculateRewardedAmount(activity);

        log.info("after amount information : {} - {} - {}", budget, rewardedAmount, incomingRewardAmount);

        if (budget.compareTo(rewardedAmount.add(incomingRewardAmount)) <= 0) {
            if (Boolean.TRUE.equals(allowOverLimitReward)) {
                for (RedPacketReward reward : rewards) {
                    if (Objects.nonNull(reward)){
                        reward.setOverLimit(true);
                    }
                }
            } else {
                throw new MetaDataBusinessException("insufficient balance.");
            }
        }
    }

    @Override
    protected void validateEventData(ObjectActionEventData data) {
        if (Strings.isNullOrEmpty(data.getObjectAction())) {
            throw new EventAbandonException("event object action is empty.");
        }
        if (Objects.isNull(data.getObjectData())) {
            throw new EventAbandonException("event object data is empty.");
        }
        if (Strings.isNullOrEmpty(data.getObjectData().getTenantId())) {
            throw new EventAbandonException("event object data tenant id is empty.");
        }
        if (Strings.isNullOrEmpty(data.getObjectData().getDescribeApiName())) {
            throw new EventAbandonException("event object data describe api name is empty.");
        }
    }

    private boolean idempotentValidate(SalesEvent<ObjectActionEventData> event, String tenantId, String activityId, String recordType,  String role) {
        if (TPMGrayUtils.allowMengNiuRedPacketPublishV2(tenantId, recordType)) {
            return idempotentValidateV2(event, tenantId, activityId, role);
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setOffset(0);
        query.setLimit(1);

        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");


        IFilter identityFilter = new Filter();
        identityFilter.setFieldName(RedPacketRecordFields.RECORD_IDENTITY);
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(RedPacketReward.buildIdentityForActionReward(role, event)));


        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.IN);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL, CommonFields.LIFE_STATUS__IN_CHANGE,
            CommonFields.LIFE_STATUS__INEFFECTIVE, CommonFields.LIFE_STATUS__INVALID, CommonFields.LIFE_STATUS__UNDER_REVIEW));

        query.setFilters(Lists.newArrayList(identityFilter, activityIdFilter, lifeStatusFilter));

        List<IObjectData> result = QueryDataUtil.find(
            serviceFacade,
            tenantId,
            RedPacketRecordFields.API_NAME,
            query,
            Lists.newArrayList(
                CommonFields.ID
            ),
            true
        );

        return CollectionUtils.isNotEmpty(result);
    }

    private boolean idempotentValidateV2(SalesEvent<ObjectActionEventData> event, String tenantId, String activityId, String role) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");


        IFilter identityFilter = new Filter();
        identityFilter.setFieldName(RedPacketRecordObjFields.RECORD_IDENTITY);
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(RedPacketReward.buildIdentityForActionReward(role, event)));


        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordObjFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.IN);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL, CommonFields.LIFE_STATUS__IN_CHANGE,
            CommonFields.LIFE_STATUS__INEFFECTIVE, CommonFields.LIFE_STATUS__INVALID, CommonFields.LIFE_STATUS__UNDER_REVIEW));

        query.setFilters(Lists.newArrayList(identityFilter, activityIdFilter, lifeStatusFilter));

        List<IObjectData> result = QueryDataUtil.find(
            serviceFacade,
            tenantId,
            ApiNames.RED_PACKET_RECORD_OBJ,
            query,
            Lists.newArrayList(
                CommonFields.ID
            ),
            true
        );

        return CollectionUtils.isNotEmpty(result);
    }

    private void unlock(String key) {
        RLock lock = redissonCmd.getLock(key);

        log.info("unlock object action over frequency : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    private boolean tryLock(String key) {
        RLock lock = redissonCmd.getLock(key);

        log.info("try lock object action over frequency : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new EventRetryException(String.format("try lock object action over frequency cause thread interrupted exception : %s", key));
        }
    }
}