package com.facishare.crm.fmcg.dms.web.abstraction;

import com.facishare.crm.fmcg.dms.model.*;

import java.util.List;

public interface IAccountsReceivableNoteService {

    ReceivableNoSettledAmountSum.Result sum(ReceivableNoSettledAmountSum.Arg arg);
    AccountsReceivableCreate.Result createRedAccountsReceivableFromRebate(AccountsReceivableCreate.Arg arg);

    boolean denyAccountsReceivableEnable(String tenantId);

    void initField(List<Integer> tenantIds);

    void initRefundButton(List<Integer> tenantIds);

    RetryMatch.Result retryMatch(RetryMatch.Arg arg);
    RetryConvert.Result retryConvert(RetryConvert.Arg arg);

    void invalidMatchNoteWithRebateAccounts(List<String> tenantIds,String flag);

    EnableReceivableAutoMatch.Result enableAutoMatch(EnableReceivableAutoMatch.Arg arg);

    QueryReceivableAutoMatchStatus.Result queryAutoMatchStatus(QueryReceivableAutoMatchStatus.Arg arg);
    void batchEnableAutoMatch(List<Integer> tenantIds, String flag,String env);

    void fixMengniuPaymentMatch(RetryMatch.Arg arg, Long begin, Long end);
}
