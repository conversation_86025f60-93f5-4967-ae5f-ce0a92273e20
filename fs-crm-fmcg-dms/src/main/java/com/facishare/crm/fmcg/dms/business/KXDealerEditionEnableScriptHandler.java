package com.facishare.crm.fmcg.dms.business;

import com.facishare.crm.fmcg.common.apiname.AccountsReceivableDetailFields;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.business.abstraction.BaseHandler;
import com.facishare.crm.fmcg.dms.business.abstraction.IDMSScriptHandler;
import com.facishare.crm.fmcg.dms.business.enums.ScriptHandlerNameEnum;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class KXDealerEditionEnableScriptHandler extends BaseHandler implements IDMSScriptHandler {
    @Override
    public boolean checkHandler(String handlerName) {
        return ScriptHandlerNameEnum.KX_DEALER_EDITION_ENABLE.getHandlerName().equals(handlerName);
    }


    @Override
    public void addFields(String tenantId, Map<String, IObjectDescribe> describes) {
        IDMSScriptHandler.super.addFields(tenantId, describes);


        try {
            fieldBusiness.addField(tenantId, describes.get(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ), ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                    AccountsReceivableDetailFields.DELIVERY_NOTE_ID, true, true);
        } catch (Exception ex) {
            log.error("add ACCOUNTS_RECEIVABLE_DETAIL_OBJ DELIVERY_NOTE_ID error", ex);
        }

        try {
            fieldBusiness.addField(tenantId, describes.get(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ), ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                    AccountsReceivableDetailFields.DELIVERY_NOTE_PRODUCT_ID, true, true);
        } catch (Exception ex) {
            log.error("add ACCOUNTS_RECEIVABLE_DETAIL_OBJ DELIVERY_NOTE_PRODUCT_ID error", ex);
        }

        try {
            fieldBusiness.addField(tenantId, describes.get(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ), ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                    AccountsReceivableDetailFields.GOODS_RECEIVED_NOTE_ID, true, true);
        } catch (Exception ex) {
            log.error("add ACCOUNTS_RECEIVABLE_DETAIL_OBJ GOODS_RECEIVED_NOTE_ID error", ex);
        }

        try {
            fieldBusiness.addField(tenantId, describes.get(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ), ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                    AccountsReceivableDetailFields.GOODS_RECEIVED_NOTE_PRODUCT_ID, true, true);
        } catch (Exception ex) {
            log.error("add ACCOUNTS_RECEIVABLE_DETAIL_OBJ GOODS_RECEIVED_NOTE_PRODUCT_ID error", ex);
        }
    }
}
